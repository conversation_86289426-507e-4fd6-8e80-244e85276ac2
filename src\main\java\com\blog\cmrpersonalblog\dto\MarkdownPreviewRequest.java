package com.blog.cmrpersonalblog.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * Markdown预览请求DTO
 */
@Data
public class MarkdownPreviewRequest {

    /**
     * Markdown内容
     */
    @NotBlank(message = "Markdown内容不能为空")
    private String content;

    /**
     * 是否启用代码高亮
     */
    private Boolean enableCodeHighlight = true;

    /**
     * 是否启用数学公式
     */
    private Boolean enableMath = false;

    /**
     * 是否启用表格
     */
    private Boolean enableTable = true;

    /**
     * 是否启用任务列表
     */
    private Boolean enableTaskList = true;

    /**
     * 是否启用删除线
     */
    private Boolean enableStrikethrough = true;

    /**
     * 是否启用自动链接
     */
    private Boolean enableAutolink = true;

    /**
     * 主题样式（light, dark, github, default）
     */
    private String theme = "default";
}

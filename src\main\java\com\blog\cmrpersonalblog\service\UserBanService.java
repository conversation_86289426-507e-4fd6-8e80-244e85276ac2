package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.cmrpersonalblog.dto.UserBanRequest;
import com.blog.cmrpersonalblog.entity.UserBan;

import java.util.List;
import java.util.Map;

/**
 * 用户禁言服务接口
 */
public interface UserBanService {

    /**
     * 禁言用户
     * @param banRequest 禁言请求
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean banUser(UserBanRequest banRequest, Long operatorId);

    /**
     * 解除用户禁言
     * @param userId 用户ID
     * @param banType 禁言类型
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean unbanUser(Long userId, String banType, Long operatorId);

    /**
     * 批量解除禁言
     * @param userIds 用户ID列表
     * @param banType 禁言类型
     * @param operatorId 操作人ID
     * @return 解除数量
     */
    int batchUnbanUsers(List<Long> userIds, String banType, Long operatorId);

    /**
     * 检查用户是否被禁言
     * @param userId 用户ID
     * @param banType 禁言类型
     * @return 是否被禁言
     */
    boolean isUserBanned(Long userId, String banType);

    /**
     * 获取用户生效中的禁言记录
     * @param userId 用户ID
     * @return 禁言记录列表
     */
    List<UserBan> getActiveBans(Long userId);

    /**
     * 获取用户禁言历史
     * @param userId 用户ID
     * @return 禁言历史列表
     */
    List<UserBan> getUserBanHistory(Long userId);

    /**
     * 分页查询禁言记录
     * @param current 页码
     * @param size 每页大小
     * @param userId 用户ID（可选）
     * @param banType 禁言类型（可选）
     * @param status 状态（可选）
     * @return 禁言记录分页列表
     */
    IPage<UserBan> getBanRecords(Long current, Long size, Long userId, String banType, Integer status);

    /**
     * 延长禁言时间
     * @param banId 禁言记录ID
     * @param additionalMinutes 延长的分钟数
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean extendBanTime(Long banId, Integer additionalMinutes, Long operatorId);

    /**
     * 自动解除过期禁言
     * @return 解除数量
     */
    int autoUnbanExpiredBans();

    /**
     * 获取即将到期的禁言记录
     * @param beforeMinutes 多少分钟内到期
     * @return 禁言记录列表
     */
    List<UserBan> getExpiringBans(Integer beforeMinutes);

    /**
     * 获取禁言统计信息
     * @return 统计信息
     */
    Map<String, Object> getBanStatistics();

    /**
     * 获取禁言类型分布
     * @return 类型分布
     */
    Map<String, Long> getBanTypeDistribution();

    /**
     * 获取最近禁言记录
     * @param limit 数量限制
     * @return 禁言记录列表
     */
    List<UserBan> getRecentBans(Integer limit);

    /**
     * 检查用户禁言次数
     * @param userId 用户ID
     * @return 禁言次数
     */
    int getUserBanCount(Long userId);

    /**
     * 获取操作人的禁言操作记录
     * @param operatorId 操作人ID
     * @param limit 数量限制
     * @return 操作记录列表
     */
    List<UserBan> getBanOperationsByOperator(Long operatorId, Integer limit);

    /**
     * 验证禁言请求
     * @param banRequest 禁言请求
     * @return 验证结果
     */
    String validateBanRequest(UserBanRequest banRequest);

    /**
     * 获取用户当前禁言状态
     * @param userId 用户ID
     * @return 禁言状态信息
     */
    Map<String, Object> getUserBanStatus(Long userId);

    /**
     * 发送禁言通知
     * @param userBan 禁言记录
     * @return 是否成功
     */
    boolean sendBanNotification(UserBan userBan);

    /**
     * 发送解禁通知
     * @param userId 用户ID
     * @param banType 禁言类型
     * @return 是否成功
     */
    boolean sendUnbanNotification(Long userId, String banType);
}

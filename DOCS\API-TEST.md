# API 测试文档

## 说明

本文档包含所有测试接口，仅用于开发和测试阶段，不计入正式API文档。

## 测试账号

| 用户名 | 密码   | 角色     | 权限                    |
|--------|--------|----------|-------------------------|
| admin  | 123456 | 超级管理员 | 所有权限                |
| editor | 123456 | 编辑员   | 用户相关权限            |
| user   | 123456 | 普通用户 | 查看权限                |

## 测试接口

### 1. 健康检查
```http
GET http://localhost:8080/test/health
```

### 2. 用户登录
```http
POST http://localhost:8080/auth/login
Content-Type: application/json

{
    "userName": "admin",
    "password": "123456"
}
```

### 3. 获取当前用户信息
```http
GET http://localhost:8080/auth/userinfo
Authorization: {token}
```

### 4. 用户注销
```http
POST http://localhost:8080/auth/logout
Authorization: {token}
```

### 5. 需要登录的接口测试
```http
GET http://localhost:8080/test/protected
Authorization: {token}
```

### 6. 管理员权限测试
```http
GET http://localhost:8080/test/admin
Authorization: {admin_token}
```

### 7. 特定权限测试
```http
GET http://localhost:8080/test/permission
Authorization: {token}
```

### 8. 获取角色列表（测试用）
```http
GET http://localhost:8080/test/roles
Authorization: {admin_token}
```

### 9. 获取权限列表（测试用）
```http
GET http://localhost:8080/test/permissions
Authorization: {admin_token}
```

### 10. 获取用户角色权限信息（测试用）
```http
GET http://localhost:8080/test/user/roles-permissions
Authorization: {token}
```

### 11. 生成SHA-256密码（基于用户名盐值）
```http
GET http://localhost:8081/test/generate-password/admin/123456
Authorization: {admin_token}
```

### 12. 生成SHA-256密码（全局固定盐值）
```http
GET http://localhost:8081/test/generate-password-global/123456
Authorization: {admin_token}
```

### 13. 修改用户密码（测试用）
```http
POST http://localhost:8081/test/change-password/1/newpassword123
Authorization: {admin_token}
```

### 14. 验证密码（测试用）
```http
POST http://localhost:8081/test/verify-password/admin/123456
Authorization: {admin_token}
```

## 前端调用问题排查

### 1. 跨域问题
如果前端无法调用接口，首先检查是否是跨域问题：

**错误现象**:
- 浏览器控制台出现 CORS 错误
- 请求被阻止，状态码为 0 或者没有响应

**解决方案**:
- 后端已配置跨域支持，允许所有域名访问
- 确保请求头 `Content-Type: application/json`
- 确保 Authorization 头格式正确

### 2. 端口问题
- 后端服务运行在 `http://localhost:8081`
- 确保前端请求的地址正确

### 3. 测试页面
访问 `http://localhost:8081/test.html` 可以直接测试接口调用

### 4. 常见错误

**401 未授权**:
```json
{
    "code": 401,
    "message": "当前会话未登录"
}
```
解决：先调用登录接口获取 token

**403 权限不足**:
```json
{
    "code": 403,
    "message": "权限不足：user:list"
}
```
解决：使用有相应权限的账号登录

## 注意事项

1. 所有测试接口都在 `/test` 路径下
2. Token 有效期为30天
3. 测试接口仅用于开发调试，不计入正式API文档
4. 权限验证失败会返回相应的错误信息
5. 已配置跨域支持，前端可以直接调用

package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户活动记录查询请求DTO
 */
@Data
public class UserActivityQueryRequest {

    /**
     * 用户ID（可选，不填则查询所有用户）
     */
    private Long userId;

    /**
     * 活动类型列表
     */
    private List<String> activityTypes;

    /**
     * 活动类型分组
     * AUTH - 认证相关
     * MANAGEMENT - 管理操作
     * CONTENT - 内容管理
     * SOCIAL - 社交互动
     * SYSTEM - 系统管理
     * IMPORTANT - 重要操作
     */
    private String typeGroup;

    /**
     * 操作结果
     */
    private String result;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 关键词搜索（活动描述）
     */
    private String keyword;

    /**
     * 目标类型
     */
    private String targetType;

    /**
     * 目标ID
     */
    private Long targetId;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向
     */
    private String orderDirection = "desc";

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 20;

    /**
     * 是否只查询重要操作
     */
    private Boolean importantOnly = false;

    /**
     * 快速时间范围
     * TODAY - 今天
     * YESTERDAY - 昨天
     * LAST_7_DAYS - 最近7天
     * LAST_30_DAYS - 最近30天
     * LAST_90_DAYS - 最近90天
     */
    private String timeRange;

    /**
     * 设置快速时间范围
     */
    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
        LocalDateTime now = LocalDateTime.now();
        
        switch (timeRange) {
            case "TODAY":
                this.startTime = now.toLocalDate().atStartOfDay();
                this.endTime = now;
                break;
            case "YESTERDAY":
                this.startTime = now.minusDays(1).toLocalDate().atStartOfDay();
                this.endTime = now.toLocalDate().atStartOfDay();
                break;
            case "LAST_7_DAYS":
                this.startTime = now.minusDays(7);
                this.endTime = now;
                break;
            case "LAST_30_DAYS":
                this.startTime = now.minusDays(30);
                this.endTime = now;
                break;
            case "LAST_90_DAYS":
                this.startTime = now.minusDays(90);
                this.endTime = now;
                break;
        }
    }
}

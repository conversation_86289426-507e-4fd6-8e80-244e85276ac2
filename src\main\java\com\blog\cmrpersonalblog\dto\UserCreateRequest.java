package com.blog.cmrpersonalblog.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 用户创建请求DTO
 */
@Data
public class UserCreateRequest {

    /**
     * 用户账号
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 30, message = "用户名长度必须在3-30个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String userName;

    /**
     * 用户昵称
     */
    @Size(min = 1, max = 50, message = "昵称长度必须在1-50个字符之间")
    private String nickName;

    /**
     * 用户密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 手机号码
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phonenumber;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别（0=男，1=女，2=未知）
     */
    @Pattern(regexp = "^[0-2]$", message = "性别值必须为0、1或2")
    private String sex;

    /**
     * 状态（0=禁用，1=启用）
     */
    private Integer status = 1;

    /**
     * 角色ID列表
     */
    private List<Long> roleIds;

    /**
     * 创建人
     */
    private String createBy;
}

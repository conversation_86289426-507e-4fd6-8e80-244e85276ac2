package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.cmrpersonalblog.dto.*;

/**
 * 用户详情服务接口
 */
public interface UserProfileService {

    /**
     * 获取用户详情信息
     * @param userId 用户ID
     * @param currentUserId 当前登录用户ID（用于判断关注状态）
     * @return 用户详情
     */
    UserProfileResponse getUserProfile(Long userId, Long currentUserId);

    /**
     * 分页查询用户发布的文章
     * @param query 查询条件
     * @return 文章分页列表
     */
    IPage<UserProfileResponse.ArticleInfo> getUserArticles(UserArticleQueryRequest query);

    /**
     * 获取用户互动数据
     * @param userId 用户ID
     * @param currentUserId 当前登录用户ID
     * @return 互动数据
     */
    UserInteractionResponse getUserInteractions(Long userId, Long currentUserId);

    /**
     * 关注/取消关注用户
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @param isFollow 是否关注
     * @return 操作结果
     */
    boolean followUser(Long followerId, Long followingId, boolean isFollow);

    /**
     * 初始化用户统计数据
     * @param userId 用户ID
     */
    void initUserStats(Long userId);

    /**
     * 更新用户统计数据
     * @param userId 用户ID
     * @param field 字段名
     * @param increment 增量
     */
    void updateUserStats(Long userId, String field, Integer increment);

    /**
     * 重新计算用户统计数据
     * @param userId 用户ID
     */
    void recalculateUserStats(Long userId);

    // ==================== 后台管理专用方法 ====================

    /**
     * 获取后台管理用户详情信息
     * @param userId 用户ID
     * @return 后台用户详情
     */
    AdminUserProfileResponse getAdminUserProfile(Long userId);

    /**
     * 获取用户内容发布统计信息
     * @param userId 用户ID
     * @return 内容发布信息
     */
    AdminUserProfileResponse.ContentInfo getUserContentInfo(Long userId);

    /**
     * 记录用户活动
     * @param userId 用户ID
     * @param activityType 活动类型
     * @param description 活动描述
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param result 操作结果
     */
    void recordUserActivity(Long userId, String activityType, String description,
                           Long targetId, String targetType, String ipAddress,
                           String userAgent, String result);

    /**
     * 获取用户活动统计
     * @param userId 用户ID
     * @param days 统计天数
     * @return 活动统计数据
     */
    AdminUserProfileResponse.InteractionInfo getUserActivityStats(Long userId, Integer days);

    /**
     * 清理过期活动记录
     * @param days 保留天数
     * @return 清理数量
     */
    Integer cleanExpiredActivities(Integer days);
}

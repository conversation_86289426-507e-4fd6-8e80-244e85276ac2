package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 文章查询请求DTO
 */
@Data
public class ArticleQueryRequest {

    /**
     * 页码
     */
    private Long current = 1L;

    /**
     * 每页大小
     */
    private Long size = 10L;

    /**
     * 文章标题（模糊查询）
     */
    private String title;

    /**
     * 作者ID
     */
    private Long authorId;

    /**
     * 作者用户名（模糊查询）
     */
    private String authorName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称（模糊查询）
     */
    private String categoryName;

    /**
     * 文章状态（0-草稿 1-已发布 2-已删除 3-待审核 4-审核拒绝 5-已下架）
     */
    private Integer status;

    /**
     * 是否置顶（0-否 1-是）
     */
    private Integer isTop;

    /**
     * 是否原创（0-转载 1-原创）
     */
    private Integer isOriginal;

    /**
     * 标签（模糊查询，逗号分隔）
     */
    private String tags;

    /**
     * 关键词（标题和内容模糊查询）
     */
    private String keyword;

    /**
     * 开始时间（发布时间范围查询）
     */
    private String startTime;

    /**
     * 结束时间（发布时间范围查询）
     */
    private String endTime;

    /**
     * 排序字段（publish_time, view_count, like_count, create_time）
     */
    private String sortField = "create_time";

    /**
     * 排序方向（asc, desc）
     */
    private String sortOrder = "desc";

    /**
     * 最小浏览量
     */
    private Integer minViewCount;

    /**
     * 最大浏览量
     */
    private Integer maxViewCount;

    /**
     * 最小点赞数
     */
    private Integer minLikeCount;

    /**
     * 最大点赞数
     */
    private Integer maxLikeCount;
}

package com.blog.cmrpersonalblog.utils;

/**
 * 密码迁移工具类
 * 用于生成SHA-256密码，方便数据库迁移
 */
public class PasswordMigrationUtils {

    public static void main(String[] args) {
        // 为测试用户生成SHA-256密码
        String password = "123456";
        
        System.out.println("=== SHA-256 + 盐值密码生成 ===");
        System.out.println("原始密码: " + password);
        System.out.println();
        
        // 为admin用户生成
        generatePasswordForUser("admin", password);
        
        // 为editor用户生成
        generatePasswordForUser("editor", password);
        
        // 为user用户生成
        generatePasswordForUser("user", password);
    }
    
    private static void generatePasswordForUser(String username, String password) {
        // 生成盐值
        String salt = PasswordUtils.generateSalt();
        
        // 加密密码
        String encryptedPassword = PasswordUtils.encryptPassword(password, salt);
        
        System.out.println("用户: " + username);
        System.out.println("盐值: " + salt);
        System.out.println("加密后密码: " + encryptedPassword);
        System.out.println("SQL更新语句:");
        System.out.println("UPDATE `sys_user` SET ");
        System.out.println("    `salt` = '" + salt + "', ");
        System.out.println("    `password` = '" + encryptedPassword + "'");
        System.out.println("WHERE `user_name` = '" + username + "';");
        System.out.println();
        
        // 验证密码
        boolean isValid = PasswordUtils.verifyPassword(password, salt, encryptedPassword);
        System.out.println("密码验证: " + (isValid ? "✓ 成功" : "✗ 失败"));
        System.out.println("----------------------------------------");
    }
}

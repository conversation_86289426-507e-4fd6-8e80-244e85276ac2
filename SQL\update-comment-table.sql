-- 更新评论表结构，添加评论管理需要的字段
USE cmr_personal_blog;

-- 添加评论管理相关字段
ALTER TABLE `comment` 
ADD COLUMN `original_content` TEXT NULL COMMENT '原始内容（未过滤敏感词）' AFTER `content`,
ADD COLUMN `root_id` BIGINT DEFAULT 0 COMMENT '根评论ID，用于构建评论树' AFTER `parent_id`,
ADD COLUMN `level` INT DEFAULT 1 COMMENT '评论层级' AFTER `root_id`,
ADD COLUMN `path` VARCHAR(500) NULL COMMENT '评论路径，用于树形结构' AFTER `level`,
ADD COLUMN `is_sensitive` TINYINT DEFAULT 0 COMMENT '是否敏感评论 0-否 1-是' AFTER `status`,
ADD COLUMN `sensitive_type` VARCHAR(50) NULL COMMENT '敏感词类型' AFTER `is_sensitive`,
ADD COLUMN `sensitive_words` TEXT NULL COMMENT '敏感词列表（JSON格式）' AFTER `sensitive_type`,
ADD COLUMN `sensitive_score` INT DEFAULT 0 COMMENT '敏感度评分（0-100）' AFTER `sensitive_words`,
ADD COLUMN `ip_address` VARCHAR(128) NULL COMMENT 'IP地址' AFTER `sensitive_score`,
ADD COLUMN `location` VARCHAR(200) NULL COMMENT '地理位置' AFTER `ip_address`,
ADD COLUMN `user_agent` TEXT NULL COMMENT '用户代理' AFTER `location`,
ADD COLUMN `audit_time` DATETIME NULL COMMENT '审核时间' AFTER `update_time`,
ADD COLUMN `audit_user_id` BIGINT NULL COMMENT '审核人ID' AFTER `audit_time`,
ADD COLUMN `audit_remark` VARCHAR(500) NULL COMMENT '审核备注' AFTER `audit_user_id`;

-- 更新评论状态枚举，支持更多状态
-- 0-待审核 1-已通过 2-已拒绝 3-已删除
ALTER TABLE `comment` 
MODIFY COLUMN `status` TINYINT DEFAULT 0 COMMENT '状态 0-待审核 1-已通过 2-已拒绝 3-已删除';

-- 添加索引以提高查询性能
ALTER TABLE `comment` 
ADD INDEX `idx_root_id` (`root_id`),
ADD INDEX `idx_level` (`level`),
ADD INDEX `idx_is_sensitive` (`is_sensitive`),
ADD INDEX `idx_sensitive_type` (`sensitive_type`),
ADD INDEX `idx_audit_user_id` (`audit_user_id`),
ADD INDEX `idx_audit_time` (`audit_time`),
ADD INDEX `idx_ip_address` (`ip_address`);

-- 创建用户禁言表
CREATE TABLE IF NOT EXISTS `user_ban` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '禁言记录ID',
    `user_id` BIGINT NOT NULL COMMENT '被禁言用户ID',
    `ban_type` VARCHAR(20) NOT NULL COMMENT '禁言类型（COMMENT-禁止评论 LOGIN-禁止登录 ALL-全站禁言）',
    `ban_reason` VARCHAR(500) NULL COMMENT '禁言原因',
    `ban_start_time` DATETIME NOT NULL COMMENT '禁言开始时间',
    `ban_end_time` DATETIME NULL COMMENT '禁言结束时间（NULL表示永久禁言）',
    `is_permanent` TINYINT DEFAULT 0 COMMENT '是否永久禁言 0-否 1-是',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-已解除 1-生效中',
    `operator_id` BIGINT NOT NULL COMMENT '操作人ID',
    `related_comment_id` BIGINT NULL COMMENT '相关评论ID（如果是因为评论被禁言）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_ban_type` (`ban_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_ban_end_time` (`ban_end_time`),
    INDEX `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户禁言表';

-- 创建敏感词表
CREATE TABLE IF NOT EXISTS `sensitive_word` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
    `word` VARCHAR(100) NOT NULL COMMENT '敏感词内容',
    `type` VARCHAR(50) NOT NULL COMMENT '敏感词类型（profanity-脏话 politics-政治 advertisement-广告等）',
    `severity` INT DEFAULT 1 COMMENT '严重程度（1-5）',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_word` (`word`),
    INDEX `idx_type` (`type`),
    INDEX `idx_severity` (`severity`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- 插入默认敏感词数据
INSERT IGNORE INTO `sensitive_word` (`word`, `type`, `severity`) VALUES
-- 脏话类
('傻逼', 'profanity', 4),
('草泥马', 'profanity', 4),
('去死', 'profanity', 4),
('滚蛋', 'profanity', 3),
('白痴', 'profanity', 3),
('智障', 'profanity', 3),
('废物', 'profanity', 3),
('垃圾', 'profanity', 2),
('操你', 'profanity', 4),
('妈的', 'profanity', 3),
('狗屎', 'profanity', 3),
('混蛋', 'profanity', 3),
('王八蛋', 'profanity', 3),
('贱人', 'profanity', 4),
('婊子', 'profanity', 4),

-- 政治敏感词
('法轮功', 'politics', 5),
('六四', 'politics', 5),
('天安门', 'politics', 5),
('达赖', 'politics', 5),
('台独', 'politics', 5),
('藏独', 'politics', 5),
('疆独', 'politics', 5),
('反政府', 'politics', 5),
('颠覆国家', 'politics', 5),
('煽动分裂', 'politics', 5),

-- 广告词
('代开发票', 'advertisement', 3),
('办证', 'advertisement', 3),
('刻章', 'advertisement', 3),
('贷款', 'advertisement', 2),
('股票推荐', 'advertisement', 3),
('彩票', 'advertisement', 3),
('博彩', 'advertisement', 4),
('赌博', 'advertisement', 4),
('色情', 'advertisement', 4),
('成人用品', 'advertisement', 3),
('微信号', 'advertisement', 2),
('QQ群', 'advertisement', 2),

-- 暴力词汇
('杀死', 'violence', 4),
('自杀', 'violence', 4),
('爆炸', 'violence', 4),
('恐怖袭击', 'violence', 5),
('血腥', 'violence', 3),
('暴力', 'violence', 3),
('砍死', 'violence', 4),
('毒死', 'violence', 4),
('勒死', 'violence', 4),
('枪杀', 'violence', 4),

-- 违法内容
('毒品', 'illegal', 5),
('海洛因', 'illegal', 5),
('冰毒', 'illegal', 5),
('摇头丸', 'illegal', 5),
('大麻', 'illegal', 5),
('制毒', 'illegal', 5),
('贩毒', 'illegal', 5),
('洗钱', 'illegal', 5),
('诈骗', 'illegal', 4),
('传销', 'illegal', 4),
('非法集资', 'illegal', 4);

-- 显示表结构确认修改成功
DESCRIBE `comment`;
DESCRIBE `user_ban`;
DESCRIBE `sensitive_word`;

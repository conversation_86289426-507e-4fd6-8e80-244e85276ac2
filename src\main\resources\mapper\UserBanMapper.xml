<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.UserBanMapper">

    <!-- 检查用户是否被禁言 -->
    <select id="selectActiveBan" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT * FROM user_ban 
        WHERE user_id = #{userId} 
        AND ban_type = #{banType} 
        AND status = 1
        AND (is_permanent = 1 OR ban_end_time > NOW())
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 获取用户所有生效中的禁言记录 -->
    <select id="selectActiveBansByUserId" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT * FROM user_ban 
        WHERE user_id = #{userId} 
        AND status = 1
        AND (is_permanent = 1 OR ban_end_time > NOW())
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询禁言记录 -->
    <select id="selectBanRecordsPage" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT 
            ub.*,
            u.user_name,
            u.nick_name,
            ou.user_name as operator_name
        FROM user_ban ub
        LEFT JOIN sys_user u ON ub.user_id = u.user_id
        LEFT JOIN sys_user ou ON ub.operator_id = ou.user_id
        <where>
            <if test="userId != null">
                AND ub.user_id = #{userId}
            </if>
            <if test="banType != null and banType != ''">
                AND ub.ban_type = #{banType}
            </if>
            <if test="status != null">
                AND ub.status = #{status}
            </if>
        </where>
        ORDER BY ub.create_time DESC
    </select>

    <!-- 解除用户禁言 -->
    <update id="unbanUser">
        UPDATE user_ban 
        SET status = 0, update_time = NOW()
        WHERE user_id = #{userId} 
        AND ban_type = #{banType} 
        AND status = 1
    </update>

    <!-- 批量解除禁言 -->
    <update id="batchUnbanUsers">
        UPDATE user_ban 
        SET status = 0, update_time = NOW()
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND ban_type = #{banType} 
        AND status = 1
    </update>

    <!-- 自动解除过期禁言 -->
    <update id="autoUnbanExpiredBans">
        UPDATE user_ban 
        SET status = 0, update_time = NOW()
        WHERE status = 1 
        AND is_permanent = 0 
        AND ban_end_time &lt;= NOW()
    </update>

    <!-- 获取即将到期的禁言记录 -->
    <select id="selectExpiringBans" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT 
            ub.*,
            u.user_name,
            u.nick_name
        FROM user_ban ub
        LEFT JOIN sys_user u ON ub.user_id = u.user_id
        WHERE ub.status = 1 
        AND ub.is_permanent = 0 
        AND ub.ban_end_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{beforeMinutes} MINUTE)
        ORDER BY ub.ban_end_time
    </select>

    <!-- 获取禁言统计信息 -->
    <select id="selectBanStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_bans,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_bans,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as expired_bans,
            SUM(CASE WHEN is_permanent = 1 THEN 1 ELSE 0 END) as permanent_bans,
            SUM(CASE WHEN ban_type = 'COMMENT' THEN 1 ELSE 0 END) as comment_bans,
            SUM(CASE WHEN ban_type = 'LOGIN' THEN 1 ELSE 0 END) as login_bans,
            SUM(CASE WHEN ban_type = 'ALL' THEN 1 ELSE 0 END) as all_bans,
            COUNT(DISTINCT user_id) as banned_users
        FROM user_ban
    </select>

    <!-- 获取禁言类型分布 -->
    <select id="selectBanTypeDistribution" resultType="map">
        SELECT 
            ban_type,
            COUNT(*) as count
        FROM user_ban
        WHERE status = 1
        GROUP BY ban_type
        ORDER BY count DESC
    </select>

    <!-- 获取最近禁言记录 -->
    <select id="selectRecentBans" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT 
            ub.*,
            u.user_name,
            u.nick_name,
            ou.user_name as operator_name
        FROM user_ban ub
        LEFT JOIN sys_user u ON ub.user_id = u.user_id
        LEFT JOIN sys_user ou ON ub.operator_id = ou.user_id
        ORDER BY ub.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 检查用户禁言历史次数 -->
    <select id="countUserBanHistory" resultType="int">
        SELECT COUNT(*) FROM user_ban WHERE user_id = #{userId}
    </select>

    <!-- 获取用户禁言历史 -->
    <select id="selectUserBanHistory" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT 
            ub.*,
            ou.user_name as operator_name
        FROM user_ban ub
        LEFT JOIN sys_user ou ON ub.operator_id = ou.user_id
        WHERE ub.user_id = #{userId}
        ORDER BY ub.create_time DESC
    </select>

    <!-- 延长禁言时间 -->
    <update id="extendBanTime">
        UPDATE user_ban 
        SET ban_end_time = #{newEndTime}, 
            update_time = NOW()
        WHERE id = #{banId}
    </update>

    <!-- 获取操作人的禁言操作记录 -->
    <select id="selectBanOperationsByOperator" resultType="com.blog.cmrpersonalblog.entity.UserBan">
        SELECT 
            ub.*,
            u.user_name,
            u.nick_name
        FROM user_ban ub
        LEFT JOIN sys_user u ON ub.user_id = u.user_id
        WHERE ub.operator_id = #{operatorId}
        ORDER BY ub.create_time DESC
        LIMIT #{limit}
    </select>

</mapper>

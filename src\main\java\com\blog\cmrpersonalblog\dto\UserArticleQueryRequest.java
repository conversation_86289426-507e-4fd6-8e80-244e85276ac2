package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 用户文章查询请求DTO
 */
@Data
public class UserArticleQueryRequest {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 文章状态 0-草稿 1-已发布 2-已删除
     */
    private Integer status;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 关键词搜索（标题、摘要）
     */
    private String keyword;

    /**
     * 排序方式
     * publish_time - 发布时间
     * view_count - 浏览量
     * like_count - 点赞数
     * comment_count - 评论数
     */
    private String orderBy = "publish_time";

    /**
     * 排序方向 asc/desc
     */
    private String orderDirection = "desc";

    /**
     * 页码（从1开始）
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;
}

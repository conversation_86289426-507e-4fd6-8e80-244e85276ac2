# 后台管理用户详情 API 文档

## 后台管理用户详情接口

### 1. 获取后台管理用户详情信息

**接口地址**: `GET /admin/user-profile/{userId}`

**权限要求**: 管理员权限 (`admin` 角色)

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "basicInfo": {
            "userId": 1,
            "userName": "admin",
            "nickName": "管理员",
            "email": "<EMAIL>",
            "phonenumber": "13800138000",
            "avatar": "http://example.com/avatar.jpg",
            "sex": "0",
            "sexName": "男",
            "status": 1,
            "statusName": "启用",
            "createTime": "2024-01-01T10:00:00",
            "updateTime": "2024-01-01T10:00:00",
            "createBy": "system",
            "updateBy": "admin",
            "roleNames": ["管理员"]
        },
        "stats": {
            "articleCount": 10,
            "draftCount": 2,
            "deletedCount": 1,
            "totalViewCount": 5000,
            "totalLikeCount": 200,
            "totalCommentCount": 50,
            "totalCollectCount": 30,
            "followerCount": 100,
            "followingCount": 50,
            "receivedLikeCount": 200,
            "givenLikeCount": 150,
            "commentGivenCount": 80,
            "commentReceivedCount": 50
        },
        "contentInfo": {
            "firstPublishTime": "2024-01-01T10:00:00",
            "lastPublishTime": "2024-01-15T10:00:00",
            "avgViewsPerArticle": 500.0,
            "avgLikesPerArticle": 20.0,
            "categoryStats": [
                {
                    "categoryName": "后端",
                    "articleCount": 5,
                    "totalViews": 2500,
                    "totalLikes": 100
                }
            ],
            "topTags": ["Spring Boot", "Java", "MySQL"],
            "publishFrequency": 7
        },
        "interactionInfo": {
            "lastLoginTime": "2024-01-15T10:00:00",
            "lastActiveTime": "2024-01-15T11:00:00",
            "loginDays": 30,
            "avgDailyActiveTime": 120.5,
            "totalSessions": 45,
            "activeTimeRanges": ["09:00-12:00", "14:00-18:00"]
        },
        "securityInfo": {
            "lastLoginIp": "*************",
            "lastLoginLocation": "北京市",
            "lastLoginDevice": "Chrome/Windows",
            "recentLoginIps": ["*************", "*************"],
            "suspiciousLoginCount": 0,
            "isEmailVerified": true,
            "isPhoneVerified": true,
            "passwordLastChanged": "2024-01-01T10:00:00"
        },
        "recentActivities": [
            {
                "activityType": "LOGIN",
                "activityDescription": "用户登录",
                "activityTime": "2024-01-15T10:00:00",
                "ipAddress": "*************",
                "userAgent": "Mozilla/5.0...",
                "result": "SUCCESS"
            }
        ]
    }
}
```

### 2. 获取用户内容发布统计

**接口地址**: `GET /admin/user-profile/{userId}/content-stats`

**权限要求**: 管理员权限

### 3. 获取用户活动统计

**接口地址**: `GET /admin/user-profile/{userId}/activity-stats`

**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| days | Integer | 否 | 统计天数，默认30天 |

### 4. 重新计算用户统计数据

**接口地址**: `POST /admin/user-profile/{userId}/recalculate-stats`

**权限要求**: 管理员权限

### 5. 清理过期活动记录

**接口地址**: `POST /admin/user-profile/clean-expired-activities`

**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| days | Integer | 否 | 保留天数，默认90天 |

## 用户活动记录管理接口

### 1. 分页查询用户活动记录

**接口地址**: `GET /admin/user-activity/list`

**权限要求**: 管理员权限

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 否 | 用户ID，不填则查询所有用户 |
| activityTypes | List&lt;String&gt; | 否 | 活动类型列表 |
| typeGroup | String | 否 | 活动类型分组（AUTH/MANAGEMENT/CONTENT/SOCIAL/IMPORTANT） |
| result | String | 否 | 操作结果（SUCCESS/FAILED） |
| ipAddress | String | 否 | IP地址（模糊查询） |
| timeRange | String | 否 | 时间范围（TODAY/YESTERDAY/LAST_7_DAYS/LAST_30_DAYS/LAST_90_DAYS） |
| keyword | String | 否 | 关键词搜索（活动描述） |
| targetType | String | 否 | 目标类型 |
| targetId | Long | 否 | 目标ID |
| importantOnly | Boolean | 否 | 是否只查询重要操作，默认false |
| orderBy | String | 否 | 排序字段，默认create_time |
| orderDirection | String | 否 | 排序方向（asc/desc），默认desc |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认20 |

**响应数据**:

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "activityType": "LOGIN",
                "activityDescription": "用户登录成功",
                "activityTime": "2024-01-15T10:00:00",
                "ipAddress": "*************",
                "userAgent": "Mozilla/5.0...",
                "result": "SUCCESS"
            }
        ],
        "total": 100,
        "current": 1,
        "size": 20
    }
}
```

### 2. 获取用户最近活动记录

**接口地址**: `GET /admin/user-activity/recent/{userId}`

**权限要求**: 管理员权限

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| limit | Integer | 否 | 限制数量，默认20 |

### 3. 获取活动统计信息

**接口地址**: `GET /admin/user-activity/statistics`

**权限要求**: 管理员权限

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 否 | 用户ID，不填则统计所有用户 |
| days | Integer | 否 | 统计天数，默认30天 |

**响应数据**:

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "totalActivities": 1500,
        "successActivities": 1450,
        "failedActivities": 50,
        "successRate": 96.67
    }
}
```

### 4. 获取活动类型分组信息

**接口地址**: `GET /admin/user-activity/type-groups`

**权限要求**: 管理员权限

**响应数据**:

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "AUTH": {
            "name": "认证相关",
            "types": ["LOGIN", "LOGOUT", "LOGIN_FAILED", "PASSWORD_CHANGED"]
        },
        "MANAGEMENT": {
            "name": "管理操作",
            "types": ["USER_CREATED", "USER_UPDATED", "USER_DELETED", "USER_STATUS_CHANGED"]
        },
        "CONTENT": {
            "name": "内容管理",
            "types": ["ARTICLE_PUBLISHED", "ARTICLE_UPDATED", "ARTICLE_DELETED"]
        },
        "SOCIAL": {
            "name": "社交互动",
            "types": ["USER_FOLLOWED", "ARTICLE_LIKED", "COMMENT_POSTED"]
        },
        "IMPORTANT": {
            "name": "重要操作",
            "description": "只显示重要的操作记录"
        }
    }
}
```

### 5. 获取时间范围选项

**接口地址**: `GET /admin/user-activity/time-ranges`

**权限要求**: 管理员权限

**响应数据**:

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "TODAY": "今天",
        "YESTERDAY": "昨天",
        "LAST_7_DAYS": "最近7天",
        "LAST_30_DAYS": "最近30天",
        "LAST_90_DAYS": "最近90天"
    }
}
```

### 6. 清理过期活动记录

**接口地址**: `POST /admin/user-activity/clean-expired`

**权限要求**: 管理员权限

**响应数据**:

```json
{
    "code": 200,
    "message": "操作成功",
    "data": "成功清理1250条过期活动记录"
}
```

## 活动记录类型说明

### 重要操作类型（会被记录）

- **认证相关**: LOGIN（登录）、LOGIN_FAILED（登录失败）、PASSWORD_CHANGED（密码修改）
- **用户管理**: USER_CREATED（创建用户）、USER_UPDATED（更新用户）、USER_DELETED（删除用户）、USER_STATUS_CHANGED（用户状态变更）
- **角色权限**: USER_ROLE_ASSIGNED（分配角色）、ROLE_CREATED（创建角色）、PERMISSION_ASSIGNED（分配权限）
- **内容管理**: ARTICLE_DELETED（删除文章）、ARTICLE_STATUS_CHANGED（文章状态变更）
- **系统管理**: SYSTEM_CONFIG_CHANGED（系统配置变更）、DATA_EXPORT（数据导出）、SECURITY_VIOLATION（安全违规）

### 普通操作类型（有条件记录）

- **内容操作**: ARTICLE_UPDATED（更新文章）、ARTICLE_PUBLISHED（发布文章）
- **社交互动**: USER_FOLLOWED（关注用户）、ARTICLE_LIKED（点赞文章）、COMMENT_POSTED（发表评论）
- **文件操作**: FILE_UPLOADED（文件上传）、FILE_DELETED（文件删除）

### 数据保留策略

- **重要操作**: 保留90-365天
- **认证记录**: 保留30天
- **普通操作**: 保留7-30天
- **自动清理**: 每天凌晨2点执行清理任务

## 注意事项

1. **权限控制**：部分接口需要登录后才能访问，后台管理接口需要管理员权限
2. **数据一致性**：统计数据通过定时任务或触发器保持一致
3. **性能优化**：大数据量查询建议添加索引，系统自动分页处理
4. **扩展性**：预留了点赞和收藏功能的接口，后续可扩展实现
5. **智能记录**：系统只记录重要操作，避免产生过多无用记录
6. **防重复记录**：5分钟内相同操作不重复记录，提高数据质量
7. **自动清理**：系统自动清理过期记录，保持数据库性能
8. **安全监控**：自动记录登录信息，支持安全分析和异常检测
9. **查询优化**：支持多维度筛选和搜索，方便管理员快速定位问题
10. **数据安全**：后台管理接口会记录管理员的操作日志，确保操作可追溯

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.SysRolePermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.blog.cmrpersonalblog.entity.SysRolePermission">
        <result column="role_id" property="roleId" />
        <result column="perm_id" property="permId" />
    </resultMap>

    <!-- 根据角色ID删除角色权限关联 -->
    <delete id="deleteByRoleId" parameterType="Long">
        DELETE FROM sys_role_permission WHERE role_id = #{roleId}
    </delete>

    <!-- 根据权限ID删除角色权限关联 -->
    <delete id="deleteByPermissionId" parameterType="Long">
        DELETE FROM sys_role_permission WHERE perm_id = #{permissionId}
    </delete>

    <!-- 批量插入角色权限关联 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sys_role_permission (role_id, perm_id) VALUES
        <foreach collection="rolePermissions" item="item" separator=",">
            (#{item.roleId}, #{item.permId})
        </foreach>
    </insert>

    <!-- 根据角色ID查询权限ID列表 -->
    <select id="selectPermissionIdsByRoleId" parameterType="Long" resultType="Long">
        SELECT perm_id FROM sys_role_permission WHERE role_id = #{roleId}
    </select>

    <!-- 根据权限ID查询角色ID列表 -->
    <select id="selectRoleIdsByPermissionId" parameterType="Long" resultType="Long">
        SELECT role_id FROM sys_role_permission WHERE perm_id = #{permissionId}
    </select>

    <!-- 检查角色权限关联是否存在 -->
    <select id="countByRoleIdAndPermissionId" resultType="int">
        SELECT COUNT(*) FROM sys_role_permission 
        WHERE role_id = #{roleId} AND perm_id = #{permissionId}
    </select>

    <!-- 批量删除权限的角色关联 -->
    <delete id="deleteByPermissionIds">
        DELETE FROM sys_role_permission WHERE perm_id IN
        <foreach collection="permissionIds" item="permissionId" open="(" separator="," close=")">
            #{permissionId}
        </foreach>
    </delete>

    <!-- 批量删除角色的权限关联 -->
    <delete id="deleteByRoleIds">
        DELETE FROM sys_role_permission WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

</mapper>

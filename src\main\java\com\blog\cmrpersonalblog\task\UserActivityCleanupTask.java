package com.blog.cmrpersonalblog.task;

import com.blog.cmrpersonalblog.service.UserActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 用户活动记录清理定时任务
 */
@Slf4j
@Component
public class UserActivityCleanupTask {

    @Autowired
    private UserActivityService userActivityService;

    /**
     * 每天凌晨2点执行清理任务
     * 清理过期的用户活动记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredActivities() {
        try {
            log.info("开始执行用户活动记录清理任务");
            
            Integer cleanedCount = userActivityService.cleanExpiredActivities();
            
            log.info("用户活动记录清理任务完成，共清理{}条记录", cleanedCount);
        } catch (Exception e) {
            log.error("用户活动记录清理任务执行失败", e);
        }
    }

    /**
     * 每周日凌晨3点执行统计任务
     * 生成活动统计报告
     */
    @Scheduled(cron = "0 0 3 ? * SUN")
    public void generateActivityReport() {
        try {
            log.info("开始生成用户活动统计报告");
            
            // 生成最近7天的统计报告
            var weeklyStats = userActivityService.getActivityStatistics(null, 7);
            log.info("最近7天活动统计: {}", weeklyStats);
            
            // 生成最近30天的统计报告
            var monthlyStats = userActivityService.getActivityStatistics(null, 30);
            log.info("最近30天活动统计: {}", monthlyStats);
            
            log.info("用户活动统计报告生成完成");
        } catch (Exception e) {
            log.error("用户活动统计报告生成失败", e);
        }
    }
}

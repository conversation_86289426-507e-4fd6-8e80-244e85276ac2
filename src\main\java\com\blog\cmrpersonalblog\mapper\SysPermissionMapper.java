package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.cmrpersonalblog.entity.SysPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统权限Mapper接口
 */
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

    /**
     * 根据权限key查询权限信息
     * @param permKey 权限标识
     * @return 权限信息
     */
    SysPermission selectPermissionByPermKey(@Param("permKey") String permKey);

    /**
     * 根据角色ID查询权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询权限列表（通过角色关联）
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 查询所有权限（用于权限管理）
     * @return 权限列表
     */
    List<SysPermission> selectAllPermissions();

    /**
     * 检查权限是否被角色使用
     * @param permId 权限ID
     * @return 使用该权限的角色数量
     */
    int countRolesByPermId(@Param("permId") Long permId);

    /**
     * 批量查询权限信息
     * @param permKeys 权限标识列表
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByPermKeys(@Param("permKeys") List<String> permKeys);
}

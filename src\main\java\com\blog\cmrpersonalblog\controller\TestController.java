package com.blog.cmrpersonalblog.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.blog.cmrpersonalblog.common.Result;
import com.blog.cmrpersonalblog.dto.*;
import com.blog.cmrpersonalblog.entity.SysRole;
import com.blog.cmrpersonalblog.entity.SysPermission;
import com.blog.cmrpersonalblog.entity.SysUser;
import com.blog.cmrpersonalblog.service.SysRoleService;
import com.blog.cmrpersonalblog.service.SysPermissionService;
import com.blog.cmrpersonalblog.service.SysUserService;
import com.blog.cmrpersonalblog.service.FileUploadService;
import com.blog.cmrpersonalblog.service.UserProfileService;
import com.blog.cmrpersonalblog.utils.PasswordUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 测试控制器 - 仅用于开发测试，不计入正式API文档
 */
@Slf4j
@RestController
@RequestMapping("/test")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TestController {

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysPermissionService sysPermissionService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("系统运行正常");
    }

    /**
     * 测试文件访问路径（无需登录）
     */
    @GetMapping("/file-access")
    public Result<String> testFileAccess() {
        return Result.success("文件访问测试 - 如果您能看到这个消息，说明 /files/** 路径已正确排除登录验证");
    }

    /**
     * 需要登录的接口
     */
    @GetMapping("/protected")
    public Result<String> protectedEndpoint() {
        return Result.success("这是一个需要登录才能访问的接口");
    }

    /**
     * 测试管理员权限的接口
     */
    @GetMapping("/admin")
    @SaCheckRole("admin")
    public Result<String> adminTest() {
        return Result.success("管理员权限验证通过");
    }

    /**
     * 测试特定权限的接口
     */
    @GetMapping("/permission")
    @SaCheckPermission("user:list")
    public Result<String> permissionTest() {
        return Result.success("权限验证通过");
    }

    /**
     * 获取所有角色列表（测试用）
     */
    @GetMapping("/roles")
    @SaCheckPermission("role:list")
    public Result<List<SysRole>> getRoles() {
        try {
            List<SysRole> roles = sysRoleService.getEnabledRoles();
            return Result.success(roles);
        } catch (Exception e) {
            return Result.error("获取角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据角色ID获取权限列表（测试用）
     */
    @GetMapping("/roles/{roleId}/permissions")
    @SaCheckPermission("role:list")
    public Result<List<SysPermission>> getRolePermissions(@PathVariable Long roleId) {
        try {
            List<SysPermission> permissions = sysRoleService.getPermissionsByRoleId(roleId);
            return Result.success(permissions);
        } catch (Exception e) {
            return Result.error("获取角色权限失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有权限列表（测试用）
     */
    @GetMapping("/permissions")
    @SaCheckPermission("system:config")
    public Result<List<SysPermission>> getPermissions() {
        try {
            List<SysPermission> permissions = sysPermissionService.getAllPermissions();
            return Result.success(permissions);
        } catch (Exception e) {
            return Result.error("获取权限列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户的角色和权限信息（测试用）
     */
    @GetMapping("/user/roles-permissions")
    public Result<UserRolePermissionResponse> getCurrentUserRolesAndPermissions() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            // 获取用户角色（带权限信息）
            List<SysRole> roles = sysRoleService.getRolesByUserIdWithPermissions(userId);

            // 获取用户权限
            List<SysPermission> permissions = sysPermissionService.getPermissionsByUserId(userId);

            // 创建响应对象
            UserRolePermissionResponse response = new UserRolePermissionResponse(roles, permissions);

            return Result.success(response);
        } catch (Exception e) {
            return Result.error("获取用户权限信息失败：" + e.getMessage());
        }
    }

    /**
     * 检查当前用户是否拥有指定权限（测试用）
     */
    @GetMapping("/check-permission/{permKey}")
    public Result<Boolean> checkPermission(@PathVariable String permKey) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            boolean hasPermission = sysPermissionService.hasPermission(userId, permKey);
            return Result.success(hasPermission);
        } catch (Exception e) {
            return Result.error("权限检查失败：" + e.getMessage());
        }
    }

    /**
     * 根据权限key获取权限详情（测试用）
     */
    @GetMapping("/permissions/key/{permKey}")
    @SaCheckRole("admin")
    public Result<SysPermission> getPermissionByKey(@PathVariable String permKey) {
        try {
            SysPermission permission = sysPermissionService.getPermissionByPermKey(permKey);
            if (permission == null) {
                return Result.error("权限不存在");
            }
            return Result.success(permission);
        } catch (Exception e) {
            return Result.error("获取权限详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据角色key获取角色详情（测试用）
     */
    @GetMapping("/roles/key/{roleKey}")
    @SaCheckRole("admin")
    public Result<SysRole> getRoleByKey(@PathVariable String roleKey) {
        try {
            SysRole role = sysRoleService.getRoleByRoleKey(roleKey);
            if (role == null) {
                return Result.error("角色不存在");
            }
            return Result.success(role);
        } catch (Exception e) {
            return Result.error("获取角色详情失败：" + e.getMessage());
        }
    }

    /**
     * 生成SHA-256密码（测试用）- 基于用户名的盐值
     */
    @GetMapping("/generate-password/{userName}/{password}")
    public Result<PasswordGenerateResponse> generatePassword(@PathVariable String userName, @PathVariable String password) {
        try {
            // 使用用户名生成SHA-256密码
            String encryptedPassword = PasswordUtils.sha256EncryptWithUserSalt(password, userName);

            // 验证密码
            boolean isValid = PasswordUtils.verifyPasswordWithUserSalt(password, userName, encryptedPassword);

            // 创建响应对象（盐值显示为基于用户名生成）
            String saltInfo = "基于用户名生成: " + userName;
            PasswordGenerateResponse response = new PasswordGenerateResponse(password, saltInfo, encryptedPassword, isValid);

            return Result.success(response);
        } catch (Exception e) {
            return Result.error("生成密码失败：" + e.getMessage());
        }
    }

    /**
     * 生成SHA-256密码（测试用）- 全局固定盐值
     */
    @GetMapping("/generate-password-global/{password}")
    @SaCheckRole("admin")
    public Result<PasswordGenerateResponse> generatePasswordGlobal(@PathVariable String password) {
        try {
            // 使用全局固定盐值生成SHA-256密码
            String encryptedPassword = PasswordUtils.sha256EncryptWithGlobalSalt(password);

            // 验证密码
            boolean isValid = PasswordUtils.verifyPasswordWithGlobalSalt(password, encryptedPassword);

            // 创建响应对象
            String saltInfo = "全局固定盐值";
            PasswordGenerateResponse response = new PasswordGenerateResponse(password, saltInfo, encryptedPassword, isValid);

            return Result.success(response);
        } catch (Exception e) {
            return Result.error("生成密码失败：" + e.getMessage());
        }
    }

    /**
     * 修改用户密码（测试用）
     */
    @PostMapping("/change-password/{userId}/{newPassword}")
    @SaCheckRole("admin")
    public Result<String> changePassword(@PathVariable Long userId, @PathVariable String newPassword) {
        try {
            boolean success = sysUserService.changePassword(userId, newPassword);
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            return Result.error("密码修改失败：" + e.getMessage());
        }
    }

    /**
     * 验证密码（测试用）
     */
    @PostMapping("/verify-password/{userName}/{password}")
    @SaCheckRole("admin")
    public Result<Boolean> verifyPassword(@PathVariable String userName, @PathVariable String password) {
        try {
            SysUser user = sysUserService.getUserByUserName(userName);
            if (user == null) {
                return Result.error("用户不存在");
            }

            boolean isValid = sysUserService.verifyPassword(user, password);
            return Result.success(isValid);
        } catch (Exception e) {
            return Result.error("密码验证失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户管理 - 分页查询用户列表（测试用）
     */
    @GetMapping("/user-management/list")
    @SaCheckRole("admin")
    public Result<PageResult<UserResponse>> testGetUserPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) Integer status) {
        try {
            UserQueryRequest queryRequest = new UserQueryRequest();
            queryRequest.setPageNum(pageNum);
            queryRequest.setPageSize(pageSize);
            queryRequest.setUserName(userName);
            queryRequest.setEmail(email);
            queryRequest.setStatus(status);

            PageResult<UserResponse> pageResult = sysUserService.getUserPage(queryRequest);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户管理 - 查询用户详情（测试用）
     */
    @GetMapping("/user-management/{userId}/detail")
    @SaCheckRole("admin")
    public Result<UserResponse> testGetUserDetail(@PathVariable Long userId) {
        try {
            UserResponse userResponse = sysUserService.getUserDetail(userId);
            if (userResponse == null) {
                return Result.error("用户不存在");
            }
            return Result.success(userResponse);
        } catch (Exception e) {
            return Result.error("查询用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户管理 - 创建用户（测试用）
     */
    @PostMapping("/user-management/createUser")
    @SaCheckRole("admin")
    public Result<String> testCreateUser(@RequestBody UserCreateRequest createRequest) {
        try {
            // 设置创建人
            createRequest.setCreateBy("admin");

            boolean success = sysUserService.createUser(createRequest);
            if (success) {
                return Result.success("用户创建成功");
            } else {
                return Result.error("用户创建失败");
            }
        } catch (Exception e) {
            return Result.error("创建用户失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户管理 - 更新用户状态（测试用）
     */
    @PostMapping("/user-management/{userId}/updateStatus/{status}")
    @SaCheckRole("admin")
    public Result<String> testUpdateUserStatus(@PathVariable Long userId, @PathVariable Integer status) {
        try {
            boolean success = sysUserService.updateUserStatus(userId, status, "admin");
            if (success) {
                String statusText = status == 1 ? "启用" : "禁用";
                return Result.success("用户" + statusText + "成功");
            } else {
                return Result.error("用户状态更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户管理 - 检查用户名是否存在（测试用）
     */
    @GetMapping("/user-management/checkUserName/{userName}")
    @SaCheckRole("admin")
    public Result<Boolean> testCheckUserName(@PathVariable String userName) {
        try {
            boolean exists = sysUserService.isUserNameExists(userName, null);
            return Result.success(exists);
        } catch (Exception e) {
            return Result.error("检查用户名失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户管理 - 重置密码（测试用）
     */
    @PostMapping("/user-management/{userId}/resetPassword/{newPassword}")
    @SaCheckRole("admin")
    public Result<String> testResetPassword(@PathVariable Long userId, @PathVariable String newPassword) {
        try {
            UserPasswordChangeRequest passwordRequest = new UserPasswordChangeRequest();
            passwordRequest.setUserId(userId);
            passwordRequest.setNewPassword(newPassword);
            passwordRequest.setConfirmPassword(newPassword);
            passwordRequest.setUpdateBy("admin");

            boolean success = sysUserService.resetPassword(passwordRequest);
            if (success) {
                return Result.success("密码重置成功");
            } else {
                return Result.error("密码重置失败");
            }
        } catch (Exception e) {
            return Result.error("重置密码失败：" + e.getMessage());
        }
    }

    /**
     * 测试用户头像上传（管理员为用户上传）
     */
    @PostMapping("/user-management/{userId}/uploadAvatar")
    @SaCheckRole("admin")
    public Result<FileUploadResponse> testUploadUserAvatar(@PathVariable Long userId,
                                                          @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }

            // 检查用户是否存在
            UserResponse user = sysUserService.getUserDetail(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 上传头像
            FileUploadResponse response = fileUploadService.uploadAvatar(file, userId);

            // 更新用户头像URL
            UserUpdateRequest updateRequest = new UserUpdateRequest();
            updateRequest.setUserId(userId);
            updateRequest.setAvatar(response.getFileUrl());
            updateRequest.setUpdateBy("admin");

            boolean updateSuccess = sysUserService.updateUser(updateRequest);
            if (!updateSuccess) {
                return Result.error("更新用户头像失败");
            }

            return Result.success("头像上传成功", response);
        } catch (Exception e) {
            return Result.error("上传用户头像失败：" + e.getMessage());
        }
    }

    /**
     * 测试当前用户头像上传（测试用）
     */
    @PostMapping("/user/uploadAvatar")
    @SaCheckLogin
    public Result<FileUploadResponse> testUploadMyAvatar(@RequestParam("file") MultipartFile file) {
        try {
            Long currentUserId = StpUtil.getLoginIdAsLong();

            if (file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }

            // 检查用户是否存在
            UserResponse user = sysUserService.getUserDetail(currentUserId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 上传头像
            FileUploadResponse response = fileUploadService.uploadAvatar(file, currentUserId);

            // 更新用户头像URL
            UserUpdateRequest updateRequest = new UserUpdateRequest();
            updateRequest.setUserId(currentUserId);
            updateRequest.setAvatar(response.getFileUrl());
            updateRequest.setUpdateBy(StpUtil.getLoginIdAsString());

            boolean updateSuccess = sysUserService.updateUser(updateRequest);
            if (!updateSuccess) {
                return Result.error("更新用户头像失败");
            }

            return Result.success("头像上传成功", response);
        } catch (Exception e) {
            return Result.error("上传头像失败：" + e.getMessage());
        }
    }

    /**
     * 测试获取可用角色列表（测试用）
     */
    @GetMapping("/user-management/getRoles")
    @SaCheckRole("admin")
    public Result<List<SysRole>> testGetAvailableRoles() {
        try {
            List<SysRole> roles = sysRoleService.getEnabledRoles();
            return Result.success(roles);
        } catch (Exception e) {
            return Result.error("获取角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 测试为用户分配角色（测试用）
     */
    @PostMapping("/user-management/{userId}/assignRoles")
    @SaCheckRole("admin")
    public Result<String> testAssignRolesToUser(@PathVariable Long userId, @RequestBody List<Long> roleIds) {
        try {
            // 获取当前登录的管理员ID
            Long currentAdminId = StpUtil.getLoginIdAsLong();

            // 检查是否为自己分配角色
            if (currentAdminId.equals(userId)) {
                return Result.error("管理员不能为自己分配角色");
            }

            // 检查用户是否存在
            UserResponse user = sysUserService.getUserDetail(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 更新用户角色
            UserUpdateRequest updateRequest = new UserUpdateRequest();
            updateRequest.setUserId(userId);
            updateRequest.setRoleIds(roleIds);
            updateRequest.setUpdateBy(StpUtil.getLoginIdAsString());

            boolean success = sysUserService.updateUser(updateRequest);
            if (success) {
                return Result.success("角色分配成功");
            } else {
                return Result.error("角色分配失败");
            }
        } catch (Exception e) {
            return Result.error("分配角色失败：" + e.getMessage());
        }
    }

    /**
     * 测试查询用户角色（测试用）
     */
    @GetMapping("/user-management/{userId}/getRoles")
    @SaCheckRole("admin")
    public Result<List<SysRole>> testGetUserRoles(@PathVariable Long userId) {
        try {
            UserResponse user = sysUserService.getUserDetail(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            List<SysRole> roles = user.getRoles() != null ? user.getRoles() : List.of();
            return Result.success(roles);
        } catch (Exception e) {
            return Result.error("查询用户角色失败：" + e.getMessage());
        }
    }

    public static void main(String[] args) {
        String userName = "editor";
        String password = "123456editor";
        String encryptedPassword = PasswordUtils.sha256EncryptWithUserSalt(password, userName);
        System.out.println("密码====: " + encryptedPassword);
    }

    /**
     * 测试角色管理 - 获取角色列表（测试用）
     */
    @GetMapping("/role-management/list")
    @SaCheckRole("admin")
    public Result<String> testGetRoleList(@RequestParam(defaultValue = "1") Integer page,
                                          @RequestParam(defaultValue = "10") Integer size) {
        try {
            RoleQueryRequest queryRequest = new RoleQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);

            var pageResult = sysRoleService.getRoleList(queryRequest);
            return Result.success("查询成功，共 " + pageResult.getTotal() + " 条记录");
        } catch (Exception e) {
            return Result.error("查询角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 测试角色管理 - 创建角色（测试用）
     */
    @PostMapping("/role-management/create")
    @SaCheckRole("admin")
    public Result<String> testCreateRole(@RequestBody RoleCreateRequest createRequest) {
        try {
            createRequest.setCreateBy(StpUtil.getLoginIdAsString());
            boolean success = sysRoleService.createRole(createRequest);
            if (success) {
                return Result.success("角色创建成功");
            } else {
                return Result.error("角色创建失败");
            }
        } catch (Exception e) {
            return Result.error("创建角色失败：" + e.getMessage());
        }
    }

    /**
     * 测试角色管理 - 更新角色（测试用）
     */
    @PutMapping("/role-management/{roleId}/update")
    @SaCheckRole("admin")
    public Result<String> testUpdateRole(@PathVariable Long roleId, @RequestBody RoleUpdateRequest updateRequest) {
        try {
            log.info("测试更新角色: roleId={}, request={}", roleId, updateRequest);
            updateRequest.setRoleId(roleId);
            updateRequest.setUpdateBy(StpUtil.getLoginIdAsString());
            boolean success = sysRoleService.updateRole(updateRequest);
            if (success) {
                return Result.success("角色更新成功");
            } else {
                return Result.error("角色更新失败");
            }
        } catch (Exception e) {
            log.error("测试更新角色失败", e);
            return Result.error("更新角色失败：" + e.getMessage());
        }
    }

    /**
     * 测试角色管理 - 简单更新角色（测试用）
     */
    @PutMapping("/role-management/{roleId}/simpleUpdate")
    @SaCheckRole("admin")
    public Result<String> testSimpleUpdateRole(@PathVariable Long roleId, @RequestParam String roleName) {
        try {
            log.info("简单测试更新角色: roleId={}, roleName={}", roleId, roleName);
            RoleUpdateRequest updateRequest = new RoleUpdateRequest();
            updateRequest.setRoleId(roleId);
            updateRequest.setRoleName(roleName);
            updateRequest.setUpdateBy(StpUtil.getLoginIdAsString());

            boolean success = sysRoleService.updateRole(updateRequest);
            if (success) {
                return Result.success("角色更新成功");
            } else {
                return Result.error("角色更新失败");
            }
        } catch (Exception e) {
            log.error("简单测试更新角色失败", e);
            return Result.error("更新角色失败：" + e.getMessage());
        }
    }

    /**
     * 测试角色管理 - 删除角色（测试用）
     */
    @DeleteMapping("/role-management/{roleId}/delete")
    @SaCheckRole("admin")
    public Result<String> testDeleteRole(@PathVariable Long roleId) {
        try {
            boolean success = sysRoleService.deleteRole(roleId);
            if (success) {
                return Result.success("角色删除成功");
            } else {
                return Result.error("角色删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除角色失败：" + e.getMessage());
        }
    }

    /**
     * 测试角色权限管理 - 为角色分配权限（测试用）
     */
    @PostMapping("/role-management/{roleId}/assignPermissions")
    @SaCheckRole("admin")
    public Result<String> testAssignPermissionsToRole(@PathVariable Long roleId, @RequestBody List<Long> permissionIds) {
        try {
            boolean success = sysRoleService.assignPermissions(roleId, permissionIds);
            if (success) {
                return Result.success("权限分配成功");
            } else {
                return Result.error("权限分配失败");
            }
        } catch (Exception e) {
            return Result.error("分配权限失败：" + e.getMessage());
        }
    }

    /**
     * 测试角色权限管理 - 获取角色权限（测试用）
     */
    @GetMapping("/role-management/{roleId}/permissions")
    @SaCheckRole("admin")
    public Result<String> testGetRolePermissions(@PathVariable Long roleId) {
        try {
            List<SysPermission> permissions = sysRoleService.getPermissionsByRoleId(roleId);
            return Result.success("查询成功，角色拥有 " + permissions.size() + " 个权限");
        } catch (Exception e) {
            return Result.error("查询角色权限失败：" + e.getMessage());
        }
    }

    // ==================== 用户详情功能测试 ====================

    /**
     * 测试初始化用户统计数据
     */
    @PostMapping("/user-profile/{userId}/init-stats")
    @SaCheckRole("admin")
    public Result<String> testInitUserStats(@PathVariable Long userId) {
        try {
            log.info("测试初始化用户统计数据: userId={}", userId);
            userProfileService.initUserStats(userId);
            return Result.success("初始化用户统计数据成功");
        } catch (Exception e) {
            log.error("测试初始化用户统计数据失败", e);
            return Result.error("初始化失败：" + e.getMessage());
        }
    }

    /**
     * 测试重新计算用户统计数据
     */
    @PostMapping("/user-profile/{userId}/recalculate-stats")
    @SaCheckRole("admin")
    public Result<String> testRecalculateUserStats(@PathVariable Long userId) {
        try {
            log.info("测试重新计算用户统计数据: userId={}", userId);
            userProfileService.recalculateUserStats(userId);
            return Result.success("重新计算用户统计数据成功");
        } catch (Exception e) {
            log.error("测试重新计算用户统计数据失败", e);
            return Result.error("重新计算失败：" + e.getMessage());
        }
    }

    /**
     * 测试关注用户
     */
    @PostMapping("/user-profile/{userId}/follow")
    @SaCheckLogin
    public Result<String> testFollowUser(@PathVariable Long userId, @RequestParam boolean isFollow) {
        try {
            Long currentUserId = StpUtil.getLoginIdAsLong();
            log.info("测试关注用户: currentUserId={}, targetUserId={}, isFollow={}",
                    currentUserId, userId, isFollow);

            boolean success = userProfileService.followUser(currentUserId, userId, isFollow);
            if (success) {
                // 记录用户活动
                userProfileService.recordUserActivity(currentUserId,
                    isFollow ? "FOLLOW_USER" : "UNFOLLOW_USER",
                    isFollow ? "关注用户" : "取消关注用户",
                    userId, "user", getClientIp(),
                    request.getHeader("User-Agent"), "SUCCESS");

                return Result.success(isFollow ? "关注成功" : "取消关注成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("测试关注用户失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 测试记录用户活动
     */
    @PostMapping("/user-activity/record")
    @SaCheckLogin
    public Result<String> testRecordUserActivity(
            @RequestParam String activityType,
            @RequestParam String description,
            @RequestParam(required = false) Long targetId,
            @RequestParam(required = false) String targetType) {
        try {
            Long currentUserId = StpUtil.getLoginIdAsLong();
            log.info("测试记录用户活动: userId={}, activityType={}", currentUserId, activityType);

            userProfileService.recordUserActivity(currentUserId, activityType, description,
                    targetId, targetType, getClientIp(),
                    request.getHeader("User-Agent"), "SUCCESS");

            return Result.success("记录用户活动成功");
        } catch (Exception e) {
            log.error("测试记录用户活动失败", e);
            return Result.error("记录失败：" + e.getMessage());
        }
    }

    /**
     * 测试检查user_activity表是否存在
     */
    @GetMapping("/check-user-activity-table")
    @SaCheckRole("admin")
    public Result<String> checkUserActivityTable() {
        try {
            log.info("检查user_activity表是否存在");

            // 尝试查询user_activity表
            userProfileService.getUserActivityStats(1L, 30);

            return Result.success("user_activity表存在且可正常访问");
        } catch (Exception e) {
            log.error("user_activity表检查失败", e);
            return Result.error("user_activity表不存在或无法访问，请执行create-user-activity-table.sql创建表。错误信息：" + e.getMessage());
        }
    }

    /**
     * 测试调试用户角色信息
     */
    @GetMapping("/debug-user-roles/{userId}")
    @SaCheckRole("admin")
    public Result<Object> debugUserRoles(@PathVariable Long userId) {
        try {
            log.info("调试用户角色信息: userId={}", userId);

            // 获取用户详情
            UserResponse userResponse = sysUserService.getUserDetail(userId);
            if (userResponse == null) {
                return Result.error("用户不存在");
            }

            // 构建调试信息
            java.util.Map<String, Object> debugInfo = new java.util.HashMap<>();
            debugInfo.put("userId", userResponse.getUserId());
            debugInfo.put("userName", userResponse.getUserName());
            debugInfo.put("roles", userResponse.getRoles());
            debugInfo.put("roleNames", userResponse.getRoleNames());
            debugInfo.put("rolesCount", userResponse.getRoles() != null ? userResponse.getRoles().size() : 0);

            log.info("用户角色调试信息: {}", debugInfo);

            return Result.success(debugInfo);
        } catch (Exception e) {
            log.error("调试用户角色信息失败", e);
            return Result.error("调试失败：" + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp() {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        if (xip != null && !xip.isEmpty() && !"unknown".equalsIgnoreCase(xip)) {
            return xip;
        }
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            return xfor;
        }
        return request.getRemoteAddr();
    }
    public void test() {
        TimedCache<String, String> cache = CacheUtil.newTimedCache(1000 * 60); // 1分钟过期
        cache.put("key", "value");
        String value = cache.get("key");
    }
}

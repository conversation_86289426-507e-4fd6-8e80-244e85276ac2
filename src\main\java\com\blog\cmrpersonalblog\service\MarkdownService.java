package com.blog.cmrpersonalblog.service;

import com.blog.cmrpersonalblog.dto.MarkdownPreviewRequest;
import com.blog.cmrpersonalblog.dto.MarkdownPreviewResponse;

/**
 * Markdown服务接口
 */
public interface MarkdownService {

    /**
     * 将Markdown转换为HTML
     * @param markdownContent Markdown内容
     * @return HTML内容
     */
    String convertToHtml(String markdownContent);

    /**
     * 预览Markdown内容
     * @param request 预览请求
     * @return 预览响应
     */
    MarkdownPreviewResponse preview(MarkdownPreviewRequest request);

    /**
     * 提取文章目录
     * @param markdownContent Markdown内容
     * @return 目录JSON字符串
     */
    String extractTableOfContents(String markdownContent);

    /**
     * 计算字数
     * @param markdownContent Markdown内容
     * @return 字数
     */
    Integer calculateWordCount(String markdownContent);

    /**
     * 计算阅读时长
     * @param wordCount 字数
     * @return 阅读时长（分钟）
     */
    Integer calculateReadingTime(Integer wordCount);

    /**
     * 清理HTML标签，获取纯文本
     * @param htmlContent HTML内容
     * @return 纯文本内容
     */
    String stripHtmlTags(String htmlContent);

    /**
     * 生成文章摘要
     * @param markdownContent Markdown内容
     * @param maxLength 最大长度
     * @return 摘要
     */
    String generateSummary(String markdownContent, Integer maxLength);
}

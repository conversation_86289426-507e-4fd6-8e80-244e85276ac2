<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论管理系统演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        .section h2 {
            color: #495057;
            margin-top: 0;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .api-info {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 12px;
        }
        .sensitive-demo {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 评论管理系统演示</h1>
            <p>完整的评论管理功能，包括敏感内容过滤、树形结构展示和用户禁言操作</p>
        </div>

        <!-- 敏感词检测 -->
        <div class="section">
            <h2>🔍 敏感词检测</h2>
            <div class="api-info">
                <strong>API:</strong> POST /admin/comments/check-sensitive<br>
                <strong>功能:</strong> 检测文本中的敏感词，支持多种类型（脏话、政治、广告等）
            </div>
            <div class="sensitive-demo">
                <strong>测试文本示例:</strong> 傻逼、法轮功、代开发票、杀死、毒品等
            </div>
            <div class="form-group">
                <label for="sensitiveText">输入要检测的文本:</label>
                <textarea id="sensitiveText" class="form-control" rows="3" placeholder="输入评论内容进行敏感词检测...">这是一条包含傻逼和法轮功的测试评论</textarea>
            </div>
            <button class="btn btn-primary" onclick="checkSensitiveWords()">检测敏感词</button>
            <div id="sensitiveResult" class="result" style="display: none;"></div>
        </div>

        <!-- 评论管理 -->
        <div class="section">
            <h2>📝 评论管理</h2>
            <div class="api-info">
                <strong>API:</strong> GET /admin/comments/list<br>
                <strong>功能:</strong> 分页查询评论列表，支持多维度筛选
            </div>
            <div class="form-group">
                <label for="commentStatus">评论状态:</label>
                <select id="commentStatus" class="form-control">
                    <option value="">全部</option>
                    <option value="0">待审核</option>
                    <option value="1">已通过</option>
                    <option value="2">已拒绝</option>
                    <option value="3">已删除</option>
                </select>
            </div>
            <div class="form-group">
                <label for="isSensitive">是否敏感:</label>
                <select id="isSensitive" class="form-control">
                    <option value="">全部</option>
                    <option value="0">否</option>
                    <option value="1">是</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="getCommentList()">查询评论列表</button>
            <button class="btn btn-success" onclick="getCommentStatistics()">获取统计信息</button>
            <div id="commentResult" class="result" style="display: none;"></div>
        </div>

        <!-- 用户禁言 -->
        <div class="section">
            <h2>🚫 用户禁言管理</h2>
            <div class="api-info">
                <strong>API:</strong> POST /admin/user-ban/ban<br>
                <strong>功能:</strong> 禁言用户，支持多种禁言类型和时长设置
            </div>
            <div class="form-group">
                <label for="banUserId">用户ID:</label>
                <input type="number" id="banUserId" class="form-control" placeholder="输入要禁言的用户ID" value="1">
            </div>
            <div class="form-group">
                <label for="banType">禁言类型:</label>
                <select id="banType" class="form-control">
                    <option value="COMMENT">禁止评论</option>
                    <option value="LOGIN">禁止登录</option>
                    <option value="ALL">全站禁言</option>
                </select>
            </div>
            <div class="form-group">
                <label for="banDuration">禁言时长(分钟):</label>
                <input type="number" id="banDuration" class="form-control" placeholder="输入禁言时长" value="60">
            </div>
            <div class="form-group">
                <label for="banReason">禁言原因:</label>
                <input type="text" id="banReason" class="form-control" placeholder="输入禁言原因" value="发布不当评论">
            </div>
            <button class="btn btn-danger" onclick="banUser()">禁言用户</button>
            <button class="btn btn-warning" onclick="checkBanStatus()">检查禁言状态</button>
            <button class="btn btn-success" onclick="getBanStatistics()">禁言统计</button>
            <div id="banResult" class="result" style="display: none;"></div>
        </div>

        <!-- 系统状态 -->
        <div class="section">
            <h2>📊 系统状态</h2>
            <button class="btn btn-primary" onclick="getSystemStatus()">获取系统状态</button>
            <div id="systemResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 基础配置
        const API_BASE = 'http://localhost:8081';
        const TOKEN = 'your-admin-token-here'; // 需要替换为实际的管理员token

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TOKEN}`
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            try {
                const response = await fetch(API_BASE + url, finalOptions);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        // 敏感词检测
        async function checkSensitiveWords() {
            const text = document.getElementById('sensitiveText').value;
            if (!text.trim()) {
                alert('请输入要检测的文本');
                return;
            }

            const result = await apiRequest('/admin/comments/check-sensitive', {
                method: 'POST',
                headers: { 'Content-Type': 'text/plain' },
                body: text
            });

            showResult('sensitiveResult', result, result.success);
        }

        // 获取评论列表
        async function getCommentList() {
            const status = document.getElementById('commentStatus').value;
            const sensitive = document.getElementById('isSensitive').value;
            
            let url = '/admin/comments/list?current=1&size=10';
            if (status) url += `&status=${status}`;
            if (sensitive) url += `&isSensitive=${sensitive}`;

            const result = await apiRequest(url);
            showResult('commentResult', result, result.success);
        }

        // 获取评论统计
        async function getCommentStatistics() {
            const result = await apiRequest('/admin/comments/statistics');
            showResult('commentResult', result, result.success);
        }

        // 禁言用户
        async function banUser() {
            const userId = document.getElementById('banUserId').value;
            const banType = document.getElementById('banType').value;
            const banDuration = document.getElementById('banDuration').value;
            const banReason = document.getElementById('banReason').value;

            if (!userId || !banDuration || !banReason) {
                alert('请填写完整信息');
                return;
            }

            const banData = {
                userId: parseInt(userId),
                banType,
                banDuration: parseInt(banDuration),
                banReason,
                sendNotification: true
            };

            const result = await apiRequest('/admin/user-ban/ban', {
                method: 'POST',
                body: JSON.stringify(banData)
            });

            showResult('banResult', result, result.success);
        }

        // 检查禁言状态
        async function checkBanStatus() {
            const userId = document.getElementById('banUserId').value;
            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            const result = await apiRequest(`/admin/user-ban/status/${userId}`);
            showResult('banResult', result, result.success);
        }

        // 获取禁言统计
        async function getBanStatistics() {
            const result = await apiRequest('/admin/user-ban/statistics');
            showResult('banResult', result, result.success);
        }

        // 获取系统状态
        async function getSystemStatus() {
            const results = {};
            
            // 获取评论统计
            const commentStats = await apiRequest('/admin/comments/statistics');
            results.commentStatistics = commentStats;
            
            // 获取待审核数量
            const pendingCount = await apiRequest('/admin/comments/pending-audit-count');
            results.pendingAuditCount = pendingCount;
            
            // 获取敏感评论数量
            const sensitiveCount = await apiRequest('/admin/comments/sensitive-count');
            results.sensitiveCommentCount = sensitiveCount;
            
            // 获取禁言统计
            const banStats = await apiRequest('/admin/user-ban/statistics');
            results.banStatistics = banStats;

            showResult('systemResult', results, true);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('评论管理系统演示页面已加载');
            console.log('请确保后端服务已启动，并替换正确的管理员token');
        });
    </script>
</body>
</html>

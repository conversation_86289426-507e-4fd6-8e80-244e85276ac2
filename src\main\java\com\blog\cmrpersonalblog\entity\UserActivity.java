package com.blog.cmrpersonalblog.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户活动记录实体类
 */
@Data
@TableName("user_activity")
public class UserActivity {

    /**
     * 活动记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 活动类型
     * LOGIN - 登录
     * LOGOUT - 登出
     * PUBLISH_ARTICLE - 发布文章
     * UPDATE_ARTICLE - 更新文章
     * DELETE_ARTICLE - 删除文章
     * LIKE_ARTICLE - 点赞文章
     * COMMENT_ARTICLE - 评论文章
     * FOLLOW_USER - 关注用户
     * UPDATE_PROFILE - 更新个人资料
     */
    @TableField("activity_type")
    private String activityType;

    /**
     * 活动描述
     */
    @TableField("activity_description")
    private String activityDescription;

    /**
     * 目标ID（文章ID、用户ID等）
     */
    @TableField("target_id")
    private Long targetId;

    /**
     * 目标类型（article、user、comment等）
     */
    @TableField("target_type")
    private String targetType;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 操作结果（SUCCESS、FAILED）
     */
    @TableField("result")
    private String result;

    /**
     * 额外数据（JSON格式）
     */
    @TableField("extra_data")
    private String extraData;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}

package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.entity.SysUser;
import com.blog.cmrpersonalblog.dto.UserQueryRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统用户Mapper接口
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户名查询用户信息
     * @param userName 用户名
     * @return 用户信息
     */
    SysUser selectUserByUserName(@Param("userName") String userName);

    /**
     * 根据用户ID查询用户权限
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询用户角色
     * @param userId 用户ID
     * @return 角色列表
     */
    List<String> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 更新用户登录信息
     * @param user 用户信息
     * @return 影响行数
     */
    int updateUserLoginInfo(SysUser user);

    // ==================== 用户管理功能 ====================

    /**
     * 分页查询用户列表（带角色信息）
     * @param page 分页参数
     * @param queryRequest 查询条件
     * @return 用户分页数据
     */
    IPage<SysUser> selectUserPageWithRoles(Page<SysUser> page, @Param("query") UserQueryRequest queryRequest);

    /**
     * 根据用户ID查询用户详情（包含角色信息）
     * @param userId 用户ID
     * @return 用户信息（包含角色）
     */
    SysUser selectUserDetailWithRoles(@Param("userId") Long userId);

    /**
     * 检查用户名是否存在
     * @param userName 用户名
     * @param excludeUserId 排除的用户ID
     * @return 存在的用户数量
     */
    int countByUserName(@Param("userName") String userName, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 存在的用户数量
     */
    int countByEmail(@Param("email") String email, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查手机号是否存在
     * @param phonenumber 手机号
     * @param excludeUserId 排除的用户ID
     * @return 存在的用户数量
     */
    int countByPhonenumber(@Param("phonenumber") String phonenumber, @Param("excludeUserId") Long excludeUserId);

    /**
     * 批量删除用户
     * @param userIds 用户ID列表
     * @return 影响行数
     */
    int deleteBatchByIds(@Param("userIds") List<Long> userIds);

    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 状态
     * @param updateBy 更新人
     * @return 影响行数
     */
    int updateUserStatus(@Param("userId") Long userId, @Param("status") Integer status, @Param("updateBy") String updateBy);
}

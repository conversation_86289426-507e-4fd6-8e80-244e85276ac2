package com.blog.cmrpersonalblog.service;

import com.blog.cmrpersonalblog.dto.LoginRequest;
import com.blog.cmrpersonalblog.dto.LoginResponse;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 用户注销
     * @return 是否成功
     */
    boolean logout();

    /**
     * 刷新token
     * @return 新的token信息
     */
    LoginResponse refreshToken();

    /**
     * 获取当前登录用户信息
     * @return 用户信息
     */
    LoginResponse getCurrentUser();
}

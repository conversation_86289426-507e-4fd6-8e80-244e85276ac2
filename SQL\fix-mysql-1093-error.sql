-- 修复MySQL 1093错误的SQL脚本
-- 解决"You can't specify target table for update in FROM clause"问题

USE cmr_personal_blog;

-- 方法1: 使用JOIN方式更新文章评论数统计
UPDATE `article` a 
LEFT JOIN (
    SELECT article_id, COUNT(*) as comment_count 
    FROM `comment` 
    WHERE `status` = 1 
    GROUP BY article_id
) c ON a.id = c.article_id 
SET a.comment_count = COALESCE(c.comment_count, 0);

-- 方法2: 使用JOIN方式更新评论回复数统计
UPDATE `comment` c1 
LEFT JOIN (
    SELECT parent_id, COUNT(*) as reply_count 
    FROM `comment` 
    WHERE `status` = 1 AND `parent_id` > 0
    GROUP BY parent_id
) c2 ON c1.id = c2.parent_id 
SET c1.reply_count = COALESCE(c2.reply_count, 0);

-- 方法3: 如果上述方法仍有问题，使用临时表方式
-- 创建临时表存储统计结果
CREATE TEMPORARY TABLE temp_article_stats AS
SELECT article_id, COUNT(*) as comment_count 
FROM `comment` 
WHERE `status` = 1 
GROUP BY article_id;

-- 使用临时表更新文章统计
UPDATE `article` a 
LEFT JOIN temp_article_stats t ON a.id = t.article_id 
SET a.comment_count = COALESCE(t.comment_count, 0);

-- 删除临时表
DROP TEMPORARY TABLE temp_article_stats;

-- 创建临时表存储回复统计
CREATE TEMPORARY TABLE temp_reply_stats AS
SELECT parent_id, COUNT(*) as reply_count 
FROM `comment` 
WHERE `status` = 1 AND `parent_id` > 0
GROUP BY parent_id;

-- 使用临时表更新回复统计
UPDATE `comment` c 
LEFT JOIN temp_reply_stats t ON c.id = t.parent_id 
SET c.reply_count = COALESCE(t.reply_count, 0);

-- 删除临时表
DROP TEMPORARY TABLE temp_reply_stats;

-- 验证更新结果
SELECT '=== 统计数据更新完成 ===' as message;

-- 显示文章评论数统计
SELECT 
    a.id as article_id,
    a.title,
    a.comment_count as updated_count,
    (SELECT COUNT(*) FROM comment c WHERE c.article_id = a.id AND c.status = 1) as actual_count
FROM article a
WHERE a.comment_count > 0 OR EXISTS (SELECT 1 FROM comment c WHERE c.article_id = a.id AND c.status = 1);

-- 显示评论回复数统计
SELECT 
    c.id as comment_id,
    c.content,
    c.reply_count as updated_count,
    (SELECT COUNT(*) FROM comment c2 WHERE c2.parent_id = c.id AND c2.status = 1) as actual_count
FROM comment c
WHERE c.reply_count > 0 OR EXISTS (SELECT 1 FROM comment c2 WHERE c2.parent_id = c.id AND c2.status = 1);

-- 额外的数据完整性检查
SELECT '=== 数据完整性检查 ===' as message;

-- 检查是否有孤立的回复（父评论不存在）
SELECT 
    c.id,
    c.parent_id,
    c.content
FROM comment c
WHERE c.parent_id > 0 
AND NOT EXISTS (SELECT 1 FROM comment p WHERE p.id = c.parent_id);

-- 检查评论路径是否正确
SELECT 
    c.id,
    c.parent_id,
    c.level,
    c.path,
    CASE 
        WHEN c.parent_id = 0 THEN CAST(c.id AS CHAR)
        ELSE CONCAT((SELECT path FROM comment p WHERE p.id = c.parent_id), '/', c.id)
    END as expected_path
FROM comment c
WHERE c.path IS NOT NULL
AND c.path != CASE 
    WHEN c.parent_id = 0 THEN CAST(c.id AS CHAR)
    ELSE CONCAT((SELECT path FROM comment p WHERE p.id = c.parent_id), '/', c.id)
END;

-- 修复评论路径（如果有问题）
UPDATE comment c1
JOIN (
    SELECT 
        id,
        CASE 
            WHEN parent_id = 0 THEN CAST(id AS CHAR)
            ELSE CONCAT(
                (SELECT path FROM comment p WHERE p.id = c.parent_id), 
                '/', 
                id
            )
        END as correct_path
    FROM comment c
    WHERE parent_id = 0 OR parent_id IN (SELECT id FROM comment WHERE path IS NOT NULL)
) c2 ON c1.id = c2.id
SET c1.path = c2.correct_path
WHERE c1.path != c2.correct_path OR c1.path IS NULL;

SELECT '=== 修复完成 ===' as message;

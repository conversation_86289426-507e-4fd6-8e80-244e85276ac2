package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 文件上传响应DTO
 */
@Data
public class FileUploadResponse {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 文件相对路径
     */
    private String relativePath;

    /**
     * 文件访问URL
     */
    private String fileUrl;

    /**
     * 上传时间戳
     */
    private Long uploadTime;

    public FileUploadResponse() {
        this.uploadTime = System.currentTimeMillis();
    }

    public FileUploadResponse(String fileName, String originalFileName, Long fileSize, 
                             String contentType, String relativePath, String fileUrl) {
        this.fileName = fileName;
        this.originalFileName = originalFileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.relativePath = relativePath;
        this.fileUrl = fileUrl;
        this.uploadTime = System.currentTimeMillis();
    }
}

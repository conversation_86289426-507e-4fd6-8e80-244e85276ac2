package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 评论查询请求DTO
 */
@Data
public class CommentQueryRequest {

    /**
     * 页码
     */
    private Long current = 1L;

    /**
     * 每页大小
     */
    private Long size = 10L;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 评论者ID
     */
    private Long userId;

    /**
     * 评论者用户名（模糊查询）
     */
    private String userName;

    /**
     * 评论内容（模糊查询）
     */
    private String content;

    /**
     * 评论状态（0-待审核 1-已通过 2-已拒绝 3-已删除）
     */
    private Integer status;

    /**
     * 是否敏感评论（0-否 1-是）
     */
    private Integer isSensitive;

    /**
     * 敏感词类型（profanity-脏话 politics-政治 advertisement-广告）
     */
    private String sensitiveType;

    /**
     * 父评论ID（查询回复）
     */
    private Long parentId;

    /**
     * 根评论ID（查询某个评论树）
     */
    private Long rootId;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 关键词（内容和用户名模糊查询）
     */
    private String keyword;

    /**
     * 排序字段（id, create_time, like_count, reply_count）
     */
    private String sortField = "id";

    /**
     * 排序方向（asc, desc）
     */
    private String sortOrder = "asc";

    /**
     * 是否只查询顶级评论
     */
    private Boolean onlyTopLevel = false;

    /**
     * 是否包含子评论
     */
    private Boolean includeChildren = true;

    /**
     * 最小点赞数
     */
    private Integer minLikeCount;

    /**
     * 最大点赞数
     */
    private Integer maxLikeCount;

    /**
     * 最小回复数
     */
    private Integer minReplyCount;

    /**
     * 最大回复数
     */
    private Integer maxReplyCount;
}

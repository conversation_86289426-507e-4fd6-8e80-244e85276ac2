package com.blog.cmrpersonalblog.dto;

import com.blog.cmrpersonalblog.entity.SysRole;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户响应DTO
 */
@Data
public class UserResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别
     */
    private String sex;

    /**
     * 性别显示名称
     */
    private String sexName;

    /**
     * 状态（0=禁用，1=启用）
     */
    private Integer status;

    /**
     * 状态显示名称
     */
    private String statusName;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 用户角色列表
     */
    private List<SysRole> roles;

    /**
     * 角色名称列表（逗号分隔）
     */
    private String roleNames;

    /**
     * 获取性别显示名称
     */
    public String getSexName() {
        if (sex == null) return "未知";
        switch (sex) {
            case "0": return "男";
            case "1": return "女";
            default: return "未知";
        }
    }

    /**
     * 获取状态显示名称
     */
    public String getStatusName() {
        if (status == null) return "未知";
        return status == 1 ? "启用" : "禁用";
    }

    /**
     * 获取角色名称列表
     */
    public String getRoleNames() {
        if (roles == null || roles.isEmpty()) {
            return "";
        }
        return roles.stream()
                .map(SysRole::getRoleName)
                .reduce((a, b) -> a + "," + b)
                .orElse("");
    }
}

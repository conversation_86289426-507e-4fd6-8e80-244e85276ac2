package com.blog.cmrpersonalblog.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 用户禁言请求DTO
 */
@Data
public class UserBanRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 禁言类型（COMMENT-禁止评论 LOGIN-禁止登录 ALL-全站禁言）
     */
    @NotNull(message = "禁言类型不能为空")
    private String banType;

    /**
     * 禁言时长（分钟）
     */
    private Integer banDuration;

    /**
     * 禁言结束时间（如果指定了具体时间）
     */
    private LocalDateTime banEndTime;

    /**
     * 禁言原因
     */
    private String banReason;

    /**
     * 是否永久禁言
     */
    private Boolean isPermanent = false;

    /**
     * 是否发送通知给用户
     */
    private Boolean sendNotification = true;

    /**
     * 相关评论ID（如果是因为评论被禁言）
     */
    private Long relatedCommentId;

    /**
     * 禁言类型枚举
     */
    public enum BanType {
        COMMENT("COMMENT", "禁止评论"),
        LOGIN("LOGIN", "禁止登录"),
        ALL("ALL", "全站禁言");

        private final String code;
        private final String description;

        BanType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static BanType fromCode(String code) {
            for (BanType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 验证禁言类型是否有效
     */
    public boolean isValidBanType() {
        return BanType.fromCode(this.banType) != null;
    }

    /**
     * 获取禁言类型枚举
     */
    public BanType getBanTypeEnum() {
        return BanType.fromCode(this.banType);
    }

    /**
     * 计算禁言结束时间
     */
    public LocalDateTime calculateBanEndTime() {
        if (isPermanent != null && isPermanent) {
            return null; // 永久禁言
        }
        
        if (banEndTime != null) {
            return banEndTime;
        }
        
        if (banDuration != null && banDuration > 0) {
            return LocalDateTime.now().plusMinutes(banDuration);
        }
        
        // 默认禁言24小时
        return LocalDateTime.now().plusHours(24);
    }

    /**
     * 获取禁言时长描述
     */
    public String getBanDurationText() {
        if (isPermanent != null && isPermanent) {
            return "永久";
        }
        
        if (banDuration == null || banDuration <= 0) {
            return "24小时";
        }
        
        if (banDuration < 60) {
            return banDuration + "分钟";
        } else if (banDuration < 1440) {
            return (banDuration / 60) + "小时";
        } else {
            return (banDuration / 1440) + "天";
        }
    }
}

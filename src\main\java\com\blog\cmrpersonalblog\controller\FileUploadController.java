package com.blog.cmrpersonalblog.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.blog.cmrpersonalblog.common.Result;
import com.blog.cmrpersonalblog.dto.FileUploadResponse;
import com.blog.cmrpersonalblog.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/upload")
@CrossOrigin(origins = "*", maxAge = 3600)
@SaCheckLogin
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    /**
     * 上传当前用户头像
     */
    @PostMapping("/uploadAvatar")
    public Result<FileUploadResponse> uploadAvatar(@RequestParam("file") MultipartFile file) {

        Long currentUserId = StpUtil.getLoginIdAsLong();
        log.info("用户上传头像: userId={}, fileName={}, fileSize={}",
                currentUserId, file.getOriginalFilename(), file.getSize());

        try {
            // 验证文件
            if (file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }

            // 上传文件（使用当前登录用户ID）
            FileUploadResponse response = fileUploadService.uploadAvatar(file, currentUserId);

            log.info("头像上传成功: {}", response.getFileUrl());
            return Result.success("头像上传成功", response);

        } catch (Exception e) {
            log.error("头像上传失败", e);
            return Result.error("头像上传失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件（仅管理员）
     */
    @DeleteMapping("/deleteFile")
    @SaCheckRole("admin")
    public Result<Void> deleteFile(@RequestParam("relativePath") String relativePath) {
        log.info("管理员删除文件: relativePath={}", relativePath);

        try {
            boolean success = fileUploadService.deleteFile(relativePath);
            if (success) {
                return Result.success("文件删除成功");
            } else {
                return Result.error("文件删除失败");
            }
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return Result.error("文件删除失败：" + e.getMessage());
        }
    }

    /**
     * 验证文件是否为有效图片
     */
    @PostMapping("/validateImage")
    public Result<Boolean> validateImage(@RequestParam("file") MultipartFile file) {
        try {
            boolean isValid = fileUploadService.isValidImage(file);
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("文件验证失败", e);
            return Result.error("文件验证失败：" + e.getMessage());
        }
    }
}

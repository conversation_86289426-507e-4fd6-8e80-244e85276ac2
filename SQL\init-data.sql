-- 初始化测试数据

-- 插入权限数据
INSERT INTO `sys_permission` (`id`, `perm_key`, `perm_name`, `description`, `create_by`, `create_time`) VALUES
(1, 'user:list', '用户列表', '查看用户列表权限', 'admin', NOW()),
(2, 'user:add', '添加用户', '添加用户权限', 'admin', NOW()),
(3, 'user:edit', '编辑用户', '编辑用户权限', 'admin', NOW()),
(4, 'user:delete', '删除用户', '删除用户权限', 'admin', NOW()),
(5, 'role:list', '角色列表', '查看角色列表权限', 'admin', NOW()),
(6, 'role:add', '添加角色', '添加角色权限', 'admin', NOW()),
(7, 'system:config', '系统配置', '系统配置权限', 'admin', NOW());

-- 插入角色数据
INSERT INTO `sys_role` (`id`, `role_key`, `role_name`, `description`, `status`, `create_by`, `create_time`) VALUES
(1, 'admin', '超级管理员', '拥有所有权限的超级管理员', '0', 'admin', NOW()),
(2, 'editor', '编辑员', '拥有编辑权限的编辑员', '0', 'admin', NOW()),
(3, 'user', '普通用户', '普通用户角色', '0', 'admin', NOW());

-- 插入用户数据（密码为123456的MD5值：e10adc3949ba59abbe56e057f20f883e）
INSERT INTO `sys_user` (`user_id`, `user_name`, `password`, `phonenumber`, `email`, `avatar`, `sex`, `status`, `create_by`, `create_time`) VALUES
(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', '13800138000', '<EMAIL>', '', '1', 1, 'system', NOW()),
(2, 'editor', 'e10adc3949ba59abbe56e057f20f883e', '13800138001', '<EMAIL>', '', '1', 1, 'admin', NOW()),
(3, 'user', 'e10adc3949ba59abbe56e057f20f883e', '13800138002', '<EMAIL>', '', '0', 1, 'admin', NOW());

-- 插入用户角色关联数据
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES
(1, 1), -- admin用户拥有超级管理员角色
(2, 2), -- editor用户拥有编辑员角色
(3, 3); -- user用户拥有普通用户角色

-- 插入角色权限关联数据
-- 超级管理员拥有所有权限
INSERT INTO `sys_role_permission` (`role_id`, `perm_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7);

-- 编辑员拥有用户相关权限
INSERT INTO `sys_role_permission` (`role_id`, `perm_id`) VALUES
(2, 1), (2, 2), (2, 3);

-- 普通用户只有查看权限
INSERT INTO `sys_role_permission` (`role_id`, `perm_id`) VALUES
(3, 1);

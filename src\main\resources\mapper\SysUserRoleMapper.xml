<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.SysUserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.blog.cmrpersonalblog.entity.SysUserRole">
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
    </resultMap>

    <!-- 根据用户ID删除用户角色关联 -->
    <delete id="deleteByUserId" parameterType="Long">
        DELETE FROM sys_user_role WHERE user_id = #{userId}
    </delete>

    <!-- 根据角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId" parameterType="Long">
        DELETE FROM sys_user_role WHERE role_id = #{roleId}
    </delete>

    <!-- 批量插入用户角色关联 -->
    <insert id="insertBatch">
        INSERT INTO sys_user_role (user_id, role_id) VALUES
        <foreach collection="userRoles" item="userRole" separator=",">
            (#{userRole.userId}, #{userRole.roleId})
        </foreach>
    </insert>

    <!-- 根据用户ID查询角色ID列表 -->
    <select id="selectRoleIdsByUserId" parameterType="Long" resultType="Long">
        SELECT role_id FROM sys_user_role WHERE user_id = #{userId}
    </select>

    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" parameterType="Long" resultType="Long">
        SELECT user_id FROM sys_user_role WHERE role_id = #{roleId}
    </select>

    <!-- 检查用户是否拥有指定角色 -->
    <select id="countByUserIdAndRoleId" resultType="int">
        SELECT COUNT(1) FROM sys_user_role 
        WHERE user_id = #{userId} AND role_id = #{roleId}
    </select>

    <!-- 批量删除用户的角色关联 -->
    <delete id="deleteByUserIds">
        DELETE FROM sys_user_role WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>

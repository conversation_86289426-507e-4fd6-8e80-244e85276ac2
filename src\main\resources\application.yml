# 应用配置
spring:
  # 应用名称
  application:
    name: Cmr-Personal-Blog
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************
    username: root
    password: root
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 4
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

# 服务器配置
server:
  port: 8081

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.blog.cmrpersonalblog.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto

# Sa-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
  active-timeout: 1800
  token-prefix: Bearer

# 文件上传配置
file:
  upload:
    base-path: E:/CmrBlogImage
    avatar-path: /UserAvatar
    url-prefix: /files
    max-file-size: 5242880  # 5MB
    allowed-image-types:
      - image/jpeg
      - image/jpg
      - image/png
      - image/gif
      - image/webp
    allowed-extensions:
      - .jpg
      - .jpeg
      - .png
      - .gif
      - .webp

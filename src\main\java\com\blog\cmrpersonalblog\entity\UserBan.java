package com.blog.cmrpersonalblog.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户禁言实体类
 */
@Data
@TableName("user_ban")
public class UserBan {

    /**
     * 禁言记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 被禁言用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 禁言类型（COMMENT-禁止评论 LOGIN-禁止登录 ALL-全站禁言）
     */
    @TableField("ban_type")
    private String banType;

    /**
     * 禁言原因
     */
    @TableField("ban_reason")
    private String banReason;

    /**
     * 禁言开始时间
     */
    @TableField("ban_start_time")
    private LocalDateTime banStartTime;

    /**
     * 禁言结束时间（NULL表示永久禁言）
     */
    @TableField("ban_end_time")
    private LocalDateTime banEndTime;

    /**
     * 是否永久禁言 0-否 1-是
     */
    @TableField("is_permanent")
    private Integer isPermanent;

    /**
     * 状态 0-已解除 1-生效中
     */
    @TableField("status")
    private Integer status;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 相关评论ID（如果是因为评论被禁言）
     */
    @TableField("related_comment_id")
    private Long relatedCommentId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否为永久禁言
     */
    public boolean isPermanentBan() {
        return isPermanent != null && isPermanent == 1;
    }

    /**
     * 是否生效中
     */
    public boolean isActive() {
        return status != null && status == 1;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        if (isPermanentBan()) {
            return false;
        }
        return banEndTime != null && LocalDateTime.now().isAfter(banEndTime);
    }

    /**
     * 获取剩余禁言时间（分钟）
     */
    public long getRemainingMinutes() {
        if (isPermanentBan() || banEndTime == null) {
            return -1; // 永久禁言
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(banEndTime)) {
            return 0; // 已过期
        }
        
        return java.time.Duration.between(now, banEndTime).toMinutes();
    }

    /**
     * 获取禁言类型描述
     */
    public String getBanTypeDescription() {
        if (banType == null) {
            return "未知";
        }
        switch (banType) {
            case "COMMENT":
                return "禁止评论";
            case "LOGIN":
                return "禁止登录";
            case "ALL":
                return "全站禁言";
            default:
                return banType;
        }
    }
}

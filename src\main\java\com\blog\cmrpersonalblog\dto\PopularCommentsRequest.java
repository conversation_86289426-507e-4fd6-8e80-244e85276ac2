package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 热门评论查询请求DTO
 */
@Data
public class PopularCommentsRequest {

    /**
     * 页码
     */
    private Long current = 1L;

    /**
     * 每页大小
     */
    private Long size = 10L;

    /**
     * 文章ID（可选，筛选特定文章的热门评论）
     */
    private Long articleId;

    /**
     * 用户ID（可选，筛选特定用户的热门评论）
     */
    private Long userId;

    /**
     * 评论状态（可选，默认只查询已通过的评论）
     */
    private Integer status = 1;

    /**
     * 是否包含敏感评论（可选，默认包含）
     */
    private Boolean includeSensitive = true;

    /**
     * 开始时间（可选，筛选指定时间范围的评论）
     */
    private String startTime;

    /**
     * 结束时间（可选，筛选指定时间范围的评论）
     */
    private String endTime;

    /**
     * 关键词搜索（可选，在评论内容中搜索）
     */
    private String keyword;

    /**
     * 是否只查询顶级评论（可选，默认false）
     */
    private Boolean onlyTopLevel = false;

    /**
     * 最小点赞数（可选，筛选点赞数不少于指定值的评论）
     */
    private Integer minLikeCount;

    /**
     * 最小回复数（可选，筛选回复数不少于指定值的评论）
     */
    private Integer minReplyCount;

    /**
     * 热度计算权重配置
     */
    
    /**
     * 点赞权重（默认2，表示1个点赞=2分热度）
     */
    private Integer likeWeight = 2;

    /**
     * 回复权重（默认1，表示1个回复=1分热度）
     */
    private Integer replyWeight = 1;

    /**
     * 最小热度分数（可选，筛选热度不少于指定值的评论）
     */
    private Integer minHotScore;

    /**
     * 时间范围类型（可选：today, week, month, all，默认all）
     */
    private String timeRange = "all";
}

package com.blog.cmrpersonalblog.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 角色创建请求DTO
 */
@Data
public class RoleCreateRequest {

    /**
     * 角色标识（如：admin、editor）
     */
    @NotBlank(message = "角色标识不能为空")
    @Size(min = 2, max = 50, message = "角色标识长度必须在2-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "角色标识只能包含字母、数字和下划线")
    private String roleKey;

    /**
     * 角色名称（如：管理员、普通用户）
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 100, message = "角色名称长度必须在2-100个字符之间")
    private String roleName;

    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;

    /**
     * 角色状态（0正常 1停用）
     */
    @Pattern(regexp = "^[01]$", message = "角色状态值必须为0或1")
    private String status = "0";

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 创建人
     */
    private String createBy;
}

### RBAC + SaToken + Redis 后台管理系统 API 测试

### 1. 健康检查
GET http://localhost:8080/test/health

### 2. 管理员登录
POST http://localhost:8080/auth/login
Content-Type: application/json

{
    "userName": "admin",
    "password": "123456"
}

> {%
client.global.set("admin_token", response.body.data.token);
%}

### 3. 编辑员登录
POST http://localhost:8080/auth/login
Content-Type: application/json

{
    "userName": "editor",
    "password": "123456"
}

> {%
client.global.set("editor_token", response.body.data.token);
%}

### 4. 普通用户登录
POST http://localhost:8080/auth/login
Content-Type: application/json

{
    "userName": "user",
    "password": "123456"
}

> {%
client.global.set("user_token", response.body.data.token);
%}

### 5. 获取当前用户信息（管理员）
GET http://localhost:8080/auth/userinfo
Authorization: {{admin_token}}

### 6. 检查登录状态
GET http://localhost:8080/auth/check
Authorization: {{admin_token}}

### 7. 需要登录的接口测试
GET http://localhost:8080/test/protected
Authorization: {{admin_token}}

### 8. 管理员权限测试（应该成功）
GET http://localhost:8080/auth/admin/test
Authorization: {{admin_token}}

### 9. 管理员权限测试（普通用户，应该失败）
GET http://localhost:8080/auth/admin/test
Authorization: {{user_token}}

### 10. 特定权限测试（管理员，应该成功）
GET http://localhost:8080/auth/permission/test
Authorization: {{admin_token}}

### 11. 特定权限测试（普通用户，应该成功，因为有user:list权限）
GET http://localhost:8080/auth/permission/test
Authorization: {{user_token}}

### 12. 刷新Token
POST http://localhost:8080/auth/refresh
Authorization: {{admin_token}}

### 13. 用户注销
POST http://localhost:8080/auth/logout
Authorization: {{admin_token}}

### 14. 注销后访问受保护接口（应该失败）
GET http://localhost:8080/test/protected
Authorization: {{admin_token}}

### 15. 错误的用户名密码测试
POST http://localhost:8080/auth/login
Content-Type: application/json

{
    "userName": "wronguser",
    "password": "wrongpass"
}

### 16. 无Token访问受保护接口
GET http://localhost:8080/test/protected

### 17. 管理员权限测试（应该成功）
GET http://localhost:8080/test/admin
Authorization: {{admin_token}}

### 18. 管理员权限测试（普通用户，应该失败）
GET http://localhost:8080/test/admin
Authorization: {{user_token}}

### 19. 特定权限测试（管理员，应该成功）
GET http://localhost:8080/test/permission
Authorization: {{admin_token}}

### 20. 特定权限测试（普通用户，应该成功，因为有user:list权限）
GET http://localhost:8080/test/permission
Authorization: {{user_token}}

### 21. 获取所有角色列表（测试用）
GET http://localhost:8080/test/roles
Authorization: {{admin_token}}

### 22. 获取角色权限（管理员角色ID=1）
GET http://localhost:8080/test/roles/1/permissions
Authorization: {{admin_token}}

### 23. 获取所有权限列表（测试用）
GET http://localhost:8080/test/permissions
Authorization: {{admin_token}}

### 24. 获取当前用户的角色和权限信息（测试用）
GET http://localhost:8080/test/user/roles-permissions
Authorization: {{admin_token}}

### 25. 检查当前用户是否拥有指定权限（测试用）
GET http://localhost:8080/test/check-permission/user:list
Authorization: {{admin_token}}

### 26. 根据权限key获取权限详情（测试用）
GET http://localhost:8080/test/permissions/key/user:list
Authorization: {{admin_token}}

### 27. 根据角色key获取角色详情（测试用）
GET http://localhost:8080/test/roles/key/admin
Authorization: {{admin_token}}

### 28. 获取用户列表（管理员）
GET http://localhost:8080/admin/users/list?page=1&size=10
Authorization: {{admin_token}}

### 29. 测试管理员为其他用户分配角色（应该成功）
POST http://localhost:8080/admin/users/2/assignRoles
Authorization: {{admin_token}}
Content-Type: application/json

[1, 2]

### 30. 测试管理员为自己分配角色（应该失败）
POST http://localhost:8080/admin/users/1/assignRoles
Authorization: {{admin_token}}
Content-Type: application/json

[1, 2]

### 31. 测试通用用户更新接口 - 管理员为其他用户更新信息（应该成功）
PUT http://localhost:8081/admin/users/2/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "nickName": "更新的昵称",
    "email": "<EMAIL>",
    "phonenumber": "13800138000",
    "roleIds": [2]
}

### 32. 测试通用用户更新接口 - 管理员为自己更新角色（应该失败）
PUT http://localhost:8080/admin/users/1/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "email": "<EMAIL>",
    "roleIds": [1, 2]
}

### 33. 测试通用用户更新接口 - 管理员为自己更新其他信息（应该成功）
PUT http://localhost:8081/admin/users/1/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "nickName": "管理员昵称",
    "email": "<EMAIL>",
    "phonenumber": "13900139000"
}

### 34. 获取角色列表（管理员）
GET http://localhost:8080/admin/roles/list?page=1&size=10
Authorization: {{admin_token}}

### 35. 获取角色详情
GET http://localhost:8080/admin/roles/1/detail
Authorization: {{admin_token}}

### 36. 创建新角色（管理员）
POST http://localhost:8080/admin/roles/createRole
Authorization: {{admin_token}}
Content-Type: application/json

{
    "roleKey": "test_role",
    "roleName": "测试角色",
    "description": "这是一个测试角色",
    "status": "0",
    "permissionIds": [1, 2]
}

### 37. 更新角色信息（管理员）
PUT http://localhost:8081/admin/roles/2/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "roleName": "更新后的角色名称",
    "description": "更新后的角色描述",
    "status": "0"
}

### 37.1 简单的角色更新测试（只更新名称）
PUT http://localhost:8081/admin/roles/2/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "roleName": "简单更新测试"
}

### 37.2 只更新描述的测试
PUT http://localhost:8081/admin/roles/2/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "description": "只更新描述信息"
}

### 38. 测试管理员尝试修改自己的角色（应该失败）
PUT http://localhost:8080/admin/roles/1/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "roleName": "尝试修改管理员角色",
    "description": "这应该失败"
}

### 39. 获取角色权限列表
GET http://localhost:8080/admin/roles/1/getPermissions
Authorization: {{admin_token}}

### 40. 为角色分配权限
POST http://localhost:8080/admin/roles/2/assignPermissions
Authorization: {{admin_token}}
Content-Type: application/json

[1, 2, 3]

### 41. 测试管理员尝试修改自己角色的权限（应该失败）
POST http://localhost:8080/admin/roles/1/assignPermissions
Authorization: {{admin_token}}
Content-Type: application/json

[1, 2, 3, 4]

### 42. 获取所有权限列表
GET http://localhost:8080/admin/roles/getAllPermissions
Authorization: {{admin_token}}

### 43. 更新角色状态
PUT http://localhost:8080/admin/roles/2/updateStatus?status=1
Authorization: {{admin_token}}

### 44. 检查角色标识是否存在
GET http://localhost:8080/admin/roles/checkRoleKey?roleKey=test_role
Authorization: {{admin_token}}

### 45. 获取所有角色（下拉选择用）
GET http://localhost:8080/admin/roles/getAllForSelect
Authorization: {{admin_token}}

### 46. 删除角色
DELETE http://localhost:8080/admin/roles/3/delete
Authorization: {{admin_token}}

### 47. 测试管理员尝试删除自己的角色（应该失败）
DELETE http://localhost:8080/admin/roles/1/delete
Authorization: {{admin_token}}

### 48. 批量删除角色
DELETE http://localhost:8080/admin/roles/batchDelete
Authorization: {{admin_token}}
Content-Type: application/json

[4, 5]

### 49. 测试接口 - 获取角色列表
GET http://localhost:8080/test/role-management/list?page=1&size=5
Authorization: {{admin_token}}

### 50. 测试接口 - 创建角色
POST http://localhost:8080/test/role-management/create
Authorization: {{admin_token}}
Content-Type: application/json

{
    "roleKey": "test_api_role",
    "roleName": "API测试角色",
    "description": "通过测试接口创建的角色",
    "status": "0",
    "permissionIds": [1]
}

### 51. 测试接口 - 更新角色
PUT http://localhost:8080/test/role-management/2/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "roleName": "API更新的角色",
    "description": "通过测试接口更新的角色"
}

### 52. 测试接口 - 删除角色
DELETE http://localhost:8080/test/role-management/3/delete
Authorization: {{admin_token}}

### 53. 测试接口 - 为角色分配权限
POST http://localhost:8080/test/role-management/2/assignPermissions
Authorization: {{admin_token}}
Content-Type: application/json

[1, 2]

### 54. 测试接口 - 获取角色权限
GET http://localhost:8081/test/role-management/2/permissions
Authorization: {{admin_token}}

### 55. 测试接口 - 简单更新角色（只更新名称）
PUT http://localhost:8081/test/role-management/2/simpleUpdate?roleName=简单测试更新
Authorization: {{admin_token}}

### 56. 测试昵称功能 - 查询用户列表（按昵称筛选）
GET http://localhost:8081/admin/users/list?page=1&size=10&nickName=管理员
Authorization: {{admin_token}}

### 57. 测试昵称功能 - 只更新用户昵称
PUT http://localhost:8081/admin/users/2/update
Authorization: {{admin_token}}
Content-Type: application/json

{
    "nickName": "新的昵称测试"
}

### 58. 测试昵称功能 - 登录后查看昵称信息
POST http://localhost:8081/auth/login
Content-Type: application/json

{
    "userName": "admin",
    "password": "123456admin"
}

### ==================== 用户详情功能测试 ====================

### 59. 获取用户详情信息
GET http://localhost:8081/user/1/profile
Authorization: {{admin_token}}

### 60. 获取当前用户详情信息
GET http://localhost:8081/user/profile
Authorization: {{admin_token}}

### 61. 分页查询用户发布的文章
GET http://localhost:8081/user/1/articles?page=1&size=10&status=1&orderBy=publish_time&orderDirection=desc
Authorization: {{admin_token}}

### 62. 获取用户互动数据
GET http://localhost:8081/user/1/interactions
Authorization: {{admin_token}}

### 63. 关注用户
POST http://localhost:8081/user/2/follow?isFollow=true
Authorization: {{admin_token}}

### 64. 取消关注用户
POST http://localhost:8081/user/2/follow?isFollow=false
Authorization: {{admin_token}}

### 65. 测试接口 - 初始化用户统计数据
POST http://localhost:8081/test/user-profile/1/init-stats
Authorization: {{admin_token}}

### 66. 测试接口 - 重新计算用户统计数据
POST http://localhost:8081/test/user-profile/1/recalculate-stats
Authorization: {{admin_token}}

### 67. 测试接口 - 关注用户
POST http://localhost:8081/test/user-profile/2/follow?isFollow=true
Authorization: {{admin_token}}

### ==================== 后台管理用户详情功能测试 ====================

### 68. 后台管理 - 获取用户详情信息
GET http://localhost:8081/admin/user-profile/1
Authorization: {{admin_token}}

### 69. 后台管理 - 分页查询用户发布的文章
GET http://localhost:8081/admin/user-profile/1/articles?page=1&size=10&status=1
Authorization: {{admin_token}}

### 70. 后台管理 - 获取用户互动数据
GET http://localhost:8081/admin/user-profile/1/interactions
Authorization: {{admin_token}}

### 71. 后台管理 - 获取用户内容发布统计
GET http://localhost:8081/admin/user-profile/1/content-stats
Authorization: {{admin_token}}

### 72. 后台管理 - 获取用户活动统计
GET http://localhost:8081/admin/user-profile/1/activity-stats?days=30
Authorization: {{admin_token}}

### 73. 后台管理 - 重新计算用户统计数据
POST http://localhost:8081/admin/user-profile/1/recalculate-stats
Authorization: {{admin_token}}

### 74. 后台管理 - 清理过期活动记录
POST http://localhost:8081/admin/user-profile/clean-expired-activities?days=90
Authorization: {{admin_token}}

### 75. 测试接口 - 记录用户活动
POST http://localhost:8081/test/user-activity/record?activityType=TEST_ACTIVITY&description=测试活动记录&targetId=1&targetType=test
Authorization: {{admin_token}}

### 76. 测试接口 - 检查user_activity表是否存在
GET http://localhost:8081/test/check-user-activity-table
Authorization: {{admin_token}}

### 77. 测试接口 - 调试用户角色信息
GET http://localhost:8081/test/debug-user-roles/1
Authorization: {{admin_token}}

### 78. 测试接口 - 调试用户角色信息（用户2）
GET http://localhost:8081/test/debug-user-roles/2
Authorization: {{admin_token}}

### ==================== 优化后的用户活动记录功能测试 ====================

### 79. 用户活动记录 - 分页查询所有活动
GET http://localhost:8081/admin/user-activity/list?page=1&size=20
Authorization: {{admin_token}}

### 80. 用户活动记录 - 查询指定用户的活动
GET http://localhost:8081/admin/user-activity/list?userId=1&page=1&size=10
Authorization: {{admin_token}}

### 81. 用户活动记录 - 查询认证相关活动
GET http://localhost:8081/admin/user-activity/list?typeGroup=AUTH&timeRange=LAST_7_DAYS
Authorization: {{admin_token}}

### 82. 用户活动记录 - 查询重要操作
GET http://localhost:8081/admin/user-activity/list?typeGroup=IMPORTANT&timeRange=LAST_30_DAYS
Authorization: {{admin_token}}

### 83. 用户活动记录 - 查询失败的操作
GET http://localhost:8081/admin/user-activity/list?result=FAILED&timeRange=LAST_7_DAYS
Authorization: {{admin_token}}

### 84. 用户活动记录 - 获取最近活动
GET http://localhost:8081/admin/user-activity/recent/1?limit=10
Authorization: {{admin_token}}

### 85. 用户活动记录 - 获取活动统计
GET http://localhost:8081/admin/user-activity/statistics?days=30
Authorization: {{admin_token}}

### 86. 用户活动记录 - 获取指定用户活动统计
GET http://localhost:8081/admin/user-activity/statistics?userId=1&days=7
Authorization: {{admin_token}}

### 87. 用户活动记录 - 获取活动类型分组
GET http://localhost:8081/admin/user-activity/type-groups
Authorization: {{admin_token}}

### 88. 用户活动记录 - 获取时间范围选项
GET http://localhost:8081/admin/user-activity/time-ranges
Authorization: {{admin_token}}

### 89. 用户活动记录 - 清理过期记录
POST http://localhost:8081/admin/user-activity/clean-expired
Authorization: {{admin_token}}

### 90. 用户活动记录 - 测试修复后的分页查询（简单测试）
GET http://localhost:8081/admin/user-activity/list?page=1&size=5&orderBy=create_time&orderDirection=desc
Authorization: {{admin_token}}

### 91. 用户活动记录 - 测试字段名映射
GET http://localhost:8081/admin/user-activity/list?page=1&size=5&orderBy=activityTime&orderDirection=desc
Authorization: {{admin_token}}

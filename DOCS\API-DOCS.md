# API 接口文档

## 概述

本文档描述了 Cmr Personal Blog 博客系统的正式API接口，供前端开发人员对接使用。

## 基础信息

- **Base URL**: `http://localhost:8081`
- **Content-Type**: `application/json`
- **认证方式**: Bearer <PERSON> (Header: `Authorization: Bearer {token}`)

## 统一响应格式

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": 1640995200000
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 参数错误 |
| 401 | 未授权/未登录 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 认证接口

### 1. 用户登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
    "userName": "string",  // 用户名，必填
    "password": "string"   // 密码，必填
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "string",           // 访问令牌
        "userId": 1,                 // 用户ID
        "userName": "string",        // 用户名
        "nickName": "string",        // 用户昵称
        "roles": ["string"],         // 用户角色列表
        "permissions": ["string"],   // 用户权限列表
        "expireTime": 2592000        // 过期时间（秒）
    }
}
```

### 2. 用户注销

**接口地址**: `POST /auth/logout`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "注销成功"
}
```

### 3. 获取当前用户信息

**接口地址**: `GET /auth/userinfo`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "token": "string",
        "userId": 1,
        "userName": "string",
        "roles": ["string"],
        "permissions": ["string"],
        "expireTime": 2592000
    }
}
```

### 4. 检查登录状态

**接口地址**: `GET /auth/check`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true  //true: 已登录, false: 未登录
}
```

### 5. 刷新Token

**接口地址**: `POST /auth/refresh`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "刷新成功",
    "data": {
        "token": "string",
        "userId": 1,
        "userName": "string",
        "roles": ["string"],
        "permissions": ["string"],
        "expireTime": 2592000
    }
}
```

## 用户管理接口

> **权限要求**: 所有用户管理接口都需要管理员权限 (`admin` 角色)

### 1. 分页查询用户列表

**接口地址**: `GET /admin/users/list`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| userName | String | 否 | 用户名（模糊查询） |
| nickName | String | 否 | 昵称（模糊查询） |
| email | String | 否 | 邮箱（模糊查询） |
| phonenumber | String | 否 | 手机号（模糊查询） |
| status | Integer | 否 | 状态（0=禁用，1=启用） |
| roleId | Long | 否 | 角色ID（精确查询） |
| sex | String | 否 | 性别（0=男，1=女，2=未知） |
| orderBy | String | 否 | 排序字段，默认create_time |
| orderDirection | String | 否 | 排序方向（asc/desc），默认desc |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "userId": 1,
                "userName": "admin",
                "nickName": "管理员",
                "phonenumber": "13800138000",
                "email": "<EMAIL>",
                "avatar": "http://example.com/avatar.jpg",
                "sex": "0",
                "sexName": "男",
                "status": 1,
                "statusName": "启用",
                "loginIp": "127.0.0.1",
                "loginDate": "2024-01-01T10:00:00",
                "createBy": "system",
                "createTime": "2024-01-01T10:00:00",
                "updateBy": "admin",
                "updateTime": "2024-01-01T10:00:00",
                "roles": [
                    {
                        "id": 1,
                        "roleKey": "admin",
                        "roleName": "管理员",
                        "description": "超级管理员",
                        "status": "0"
                    }
                ],
                "roleNames": "管理员"
            }
        ],
        "total": 100,
        "current": 1,
        "size": 10,
        "pages": 10,
        "hasPrevious": false,
        "hasNext": true
    }
}
```

### 2. 查询用户详情

**接口地址**: `GET /admin/users/{userId}/detail`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "userId": 1,
        "userName": "admin",
        "phonenumber": "13800138000",
        "email": "<EMAIL>",
        "avatar": "http://example.com/avatar.jpg",
        "sex": "0",
        "sexName": "男",
        "status": 1,
        "statusName": "启用",
        "loginIp": "127.0.0.1",
        "loginDate": "2024-01-01T10:00:00",
        "createBy": "system",
        "createTime": "2024-01-01T10:00:00",
        "updateBy": "admin",
        "updateTime": "2024-01-01T10:00:00",
        "roles": [
            {
                "id": 1,
                "roleKey": "admin",
                "roleName": "管理员",
                "description": "超级管理员",
                "status": "0"
            }
        ],
        "roleNames": "管理员"
    }
}
```

### 3. 创建用户

**接口地址**: `POST /admin/users/create`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "userName": "string",        // 用户名，必填，3-30字符，只能包含字母数字下划线
    "nickName": "string",        // 昵称，选填，1-50字符
    "password": "string",        // 密码，必填，6-20字符
    "phonenumber": "string",     // 手机号，选填，格式：1[3-9]xxxxxxxxx
    "email": "string",           // 邮箱，选填，邮箱格式
    "avatar": "string",          // 头像URL，为文件上传
    "sex": "string",             // 性别，选填，0=男，1=女，2=未知
    "status": 1,                 // 状态，选填，0=禁用，1=启用，默认1
    "roleIds": [1, 2]            // 角色ID列表，选填
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "用户创建成功"
}
```

### 4. 更新用户信息

**接口地址**: `PUT /admin/users/{userId}/update`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**请求参数**:
```json
{
    "nickName": "string",        // 昵称，选填，1-50字符
    "phonenumber": "string",     // 手机号，选填
    "email": "string",           // 邮箱，选填
    "avatar": "string",          // 头像URL，为文件上传
    "sex": "string",             // 性别，选填，0=男，1=女，2=未知
    "status": 1,                 // 状态，选填，0=禁用，1=启用
    "roleIds": [1, 2]            // 角色ID列表，选填
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "用户更新成功"
}
```

### 5. 删除用户

**接口地址**: `DELETE /admin/users/{userId}/delete`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "用户删除成功"
}
```

### 6. 批量删除用户

**接口地址**: `DELETE /admin/users/batchDelete`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
[1, 2, 3]  // 用户ID数组
```

**响应数据**:
```json
{
    "code": 200,
    "message": "用户批量删除成功"
}
```

### 7. 重置用户密码

**接口地址**: `PUT /admin/users/{userId}/resetPassword`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**请求参数**:
```json
{
    "newPassword": "string",      // 新密码，必填，6-20字符
    "confirmPassword": "string"   // 确认密码，必填，需与新密码一致
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "密码重置成功"
}
```

### 8. 启用/禁用用户

**接口地址**: `PUT /admin/users/{userId}/updateStatus`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | Integer | 是 | 状态（0=禁用，1=启用） |

**响应数据**:
```json
{
    "code": 200,
    "message": "用户启用成功"  // 或 "用户禁用成功"
}
```

### 9. 检查用户名是否存在

**接口地址**: `GET /admin/users/checkUserName`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userName | String | 是 | 要检查的用户名 |
| excludeUserId | Long | 否 | 排除的用户ID（编辑时使用） |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true  // true: 已存在, false: 不存在
}
```

### 10. 检查邮箱是否存在

**接口地址**: `GET /admin/users/checkEmail`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | String | 是 | 要检查的邮箱 |
| excludeUserId | Long | 否 | 排除的用户ID（编辑时使用） |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": false  // true: 已存在, false: 不存在
}
```

### 11. 检查手机号是否存在

**接口地址**: `GET /admin/users/checkPhone`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phonenumber | String | 是 | 要检查的手机号 |
| excludeUserId | Long | 否 | 排除的用户ID（编辑时使用） |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": false  // true: 已存在, false: 不存在
}
```

### 12. 获取可用角色列表

**接口地址**: `GET /admin/users/getRoles`

**请求头**: `Authorization: {token}`

**权限要求**: 需要管理员权限

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "roleKey": "admin",
            "roleName": "管理员",
            "description": "超级管理员",
            "status": "0",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        },
        {
            "id": 2,
            "roleKey": "editor",
            "roleName": "编辑员",
            "description": "内容编辑员",
            "status": "0",
            "createBy": "admin",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        }
    ]
}
```

### 13. 为用户分配角色

**接口地址**: `POST /admin/users/{userId}/assignRoles`

**请求头**: `Authorization: {token}`

**权限要求**: 需要管理员权限

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**请求参数**:
```json
[1, 2, 3]  // 角色ID数组
```

**响应数据**:
```json
{
    "code": 200,
    "message": "角色分配成功"
}
```

### 14. 获取用户角色列表

**接口地址**: `GET /admin/users/{userId}/getRoles`

**请求头**: `Authorization: {token}`

**权限要求**: 需要管理员权限

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "roleKey": "admin",
            "roleName": "管理员",
            "description": "超级管理员",
            "status": "0"
        }
    ]
}
```

## 角色管理接口

> **权限要求**: 所有角色管理接口都需要管理员权限 (`admin` 角色)
>
> **安全限制**: 管理员不能修改、删除自己拥有的角色，防止权限问题导致无法操作

### 1. 分页查询角色列表

**接口地址**: `GET /admin/roles/list`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| roleKey | String | 否 | 角色标识（模糊查询） |
| roleName | String | 否 | 角色名称（模糊查询） |
| status | String | 否 | 状态（0正常 1停用） |
| permissionId | Long | 否 | 权限ID（精确查询） |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "roleKey": "admin",
                "roleName": "管理员",
                "description": "超级管理员",
                "status": "0",
                "statusText": "正常",
                "createBy": "system",
                "createTime": "2024-01-01T10:00:00",
                "updateBy": "admin",
                "updateTime": "2024-01-01T10:00:00",
                "permissions": [
                    {
                        "id": 1,
                        "permKey": "user:list",
                        "permName": "查看用户列表",
                        "description": "查看用户列表权限"
                    }
                ],
                "userCount": 1,
                "canDelete": false
            }
        ],
        "total": 10,
        "current": 1,
        "size": 10,
        "pages": 1,
        "hasPrevious": false,
        "hasNext": false
    }
}
```

### 2. 获取角色详情

**接口地址**: `GET /admin/roles/{roleId}/detail`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "roleKey": "admin",
        "roleName": "管理员",
        "description": "超级管理员",
        "status": "0",
        "statusText": "正常",
        "createBy": "system",
        "createTime": "2024-01-01T10:00:00",
        "updateBy": "admin",
        "updateTime": "2024-01-01T10:00:00",
        "permissions": [
            {
                "id": 1,
                "permKey": "user:list",
                "permName": "查看用户列表",
                "description": "查看用户列表权限"
            }
        ],
        "userCount": 1,
        "canDelete": false
    }
}
```

### 3. 创建角色

**接口地址**: `POST /admin/roles/createRole`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
    "roleKey": "string",         // 角色标识，必填，2-50字符，只能包含字母数字下划线
    "roleName": "string",        // 角色名称，必填，2-100字符
    "description": "string",     // 角色描述，选填，最大500字符
    "status": "0",               // 角色状态，选填，0正常 1停用，默认0
    "permissionIds": [1, 2, 3]   // 权限ID列表，选填
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "角色创建成功"
}
```

### 4. 更新角色

**接口地址**: `PUT /admin/roles/{roleId}/update`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**请求参数**:
```json
{
    "roleName": "string",        // 角色名称，选填，2-100字符
    "description": "string",     // 角色描述，选填，最大500字符
    "status": "0",               // 角色状态，选填，0正常 1停用
    "permissionIds": [1, 2, 3]   // 权限ID列表，选填
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "角色更新成功"
}
```

### 5. 删除角色

**接口地址**: `DELETE /admin/roles/{roleId}/delete`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "角色删除成功"
}
```

### 6. 批量删除角色

**接口地址**: `DELETE /admin/roles/batchDelete`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
[1, 2, 3]  // 角色ID数组
```

**响应数据**:
```json
{
    "code": 200,
    "message": "角色批量删除成功"
}
```

### 7. 更新角色状态

**接口地址**: `PUT /admin/roles/{roleId}/updateStatus`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | String | 是 | 状态（0正常 1停用） |

**响应数据**:
```json
{
    "code": 200,
    "message": "角色启用成功"  // 或 "角色停用成功"
}
```

### 8. 检查角色标识是否存在

**接口地址**: `GET /admin/roles/checkRoleKey`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleKey | String | 是 | 要检查的角色标识 |
| excludeId | Long | 否 | 排除的角色ID（编辑时使用） |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true  // true: 可用（不存在）, false: 不可用（已存在）
}
```

### 9. 获取所有角色（下拉选择用）

**接口地址**: `GET /admin/roles/getAllForSelect`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "roleKey": "admin",
            "roleName": "管理员",
            "description": "超级管理员",
            "status": "0",
            "statusText": "正常"
        }
    ]
}
```

## 角色权限管理接口

> **权限要求**: 所有角色权限管理接口都需要管理员权限 (`admin` 角色)
>
> **安全限制**: 管理员不能修改自己拥有的角色权限

### 1. 获取角色权限列表

**接口地址**: `GET /admin/roles/{roleId}/getPermissions`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "permKey": "user:list",
            "permName": "查看用户列表",
            "description": "查看用户列表权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        }
    ]
}
```

### 2. 为角色分配权限

**接口地址**: `POST /admin/roles/{roleId}/assignPermissions`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**请求参数**:
```json
[1, 2, 3]  // 权限ID数组
```

**响应数据**:
```json
{
    "code": 200,
    "message": "权限分配成功"
}
```

### 3. 移除角色所有权限

**接口地址**: `DELETE /admin/roles/{roleId}/removeAllPermissions`

**请求头**: `Authorization: Bearer {token}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "权限移除成功"
}
```

### 4. 获取所有权限列表

**接口地址**: `GET /admin/roles/getAllPermissions`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "permKey": "user:list",
            "permName": "查看用户列表",
            "description": "查看用户列表权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        }
    ]
}
```

## 文件上传接口

> **权限要求**: 用户头像上传需要登录，管理员功能需要管理员权限
>
> **文件访问**: 上传成功后，图片可通过返回的 `fileUrl` 直接访问，无需登录验证
>
> **示例**: `http://localhost:8081/files/UserAvatar/avatar_1_20250715211122_2257b3bc.jpg`

### 1. 上传当前用户头像

**接口地址**: `POST /api/user/uploadAvatar`

**请求头**: `Authorization: Bearer {token}`

**权限要求**: 需要登录

**请求参数**: `multipart/form-data`

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 头像文件（支持jpg、png、gif、webp格式，最大5MB） |

**响应数据**:
```json
{
    "code": 200,
    "message": "头像上传成功",
    "data": {
        "fileName": "avatar_1_20240101120000_abcd1234.jpg",
        "originalFileName": "user_avatar.jpg",
        "fileSize": 102400,
        "contentType": "image/jpeg",
        "relativePath": "/UserAvatar/avatar_1_20240101120000_abcd1234.jpg",
        "fileUrl": "/files/UserAvatar/avatar_1_20240101120000_abcd1234.jpg",
        "uploadTime": 1640995200000
    }
}
```

### 2. 删除当前用户头像

**接口地址**: `DELETE /api/user/deleteAvatar`

**请求头**: `Authorization: Bearer {token}`

**权限要求**: 需要登录

**响应数据**:
```json
{
    "code": 200,
    "message": "头像删除成功"
}
```

### 3. 获取当前用户头像

**接口地址**: `GET /api/user/getAvatar`

**请求头**: `Authorization:  Bearer {token}`

**权限要求**: 需要登录

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": "/files/UserAvatar/avatar_1_20240101120000_abcd1234.jpg"
}
```

### 4. 为指定用户上传头像（管理员）

**接口地址**: `POST /admin/users/{userId}/uploadAvatar`

**请求头**: `Authorization: Bearer {token}`

**权限要求**: 需要管理员权限

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**请求参数**: `multipart/form-data`

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 头像文件 |

**响应数据**:
```json
{
    "code": 200,
    "message": "头像上传成功",
    "data": {
        "fileName": "avatar_1_20240101120000_abcd1234.jpg",
        "originalFileName": "user_avatar.jpg",
        "fileSize": 102400,
        "contentType": "image/jpeg",
        "relativePath": "/UserAvatar/avatar_1_20240101120000_abcd1234.jpg",
        "fileUrl": "/files/UserAvatar/avatar_1_20240101120000_abcd1234.jpg",
        "uploadTime": 1640995200000
    }
}
```

### 5. 删除用户头像（管理员）

**接口地址**: `DELETE /admin/users/{userId}/deleteAvatar`

**请求头**: `Authorization: Bearer {token}`

**权限要求**: 需要管理员权限

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "头像删除成功"
}
```

### 6. 删除文件（管理员）

**接口地址**: `DELETE /api/upload/deleteFile`

**请求头**: `Authorization: Bearer {token}`

**权限要求**: 需要管理员权限

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relativePath | String | 是 | 文件相对路径 |

**响应数据**:
```json
{
    "code": 200,
    "message": "文件删除成功"
}
```

### 7. 验证图片文件

**接口地址**: `POST /api/upload/validateImage`

**请求头**: `Authorization: Bearer {token}`

**权限要求**: 需要登录

**请求参数**: `multipart/form-data`

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 要验证的文件 |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true  // true: 有效图片, false: 无效文件
}
```

## 错误处理

### 未登录错误
```json
{
    "code": 401,
    "message": "当前会话未登录",
    "timestamp": 1640995200000
}
```

### 权限不足错误
```json
{
    "code": 403,
    "message": "权限不足：user:list",
    "timestamp": 1640995200000
}
```

### 参数错误
```json
{
    "code": 400,
    "message": "用户名不能为空; 密码不能为空; ",
    "timestamp": 1640995200000
}
```

## 权限说明

### 角色类型
- `admin`: 超级管理员，拥有所有权限
- `editor`: 编辑员，拥有用户相关权限
- `user`: 普通用户，拥有查看权限

### 权限标识
- `user:list`: 查看用户列表
- `user:add`: 添加用户
- `user:edit`: 编辑用户
- `user:delete`: 删除用户
- `user:manage`: 用户管理（包含所有用户操作）
- `role:list`: 查看角色列表
- `role:add`: 添加角色
- `role:edit`: 编辑角色
- `role:delete`: 删除角色
- `role:manage`: 角色管理（包含所有角色操作）
- `permission:list`: 查看权限列表
- `permission:assign`: 分配权限
- `system:config`: 系统配置

### 用户管理权限说明
- 所有 `/admin/users/*` 接口都需要 `admin` 角色
- 用户管理功能仅限管理员使用
- 管理员可以管理所有用户，包括创建、编辑、删除、重置密码等操作

### 角色管理权限说明
- 所有 `/admin/roles/*` 接口都需要 `admin` 角色
- 角色管理功能仅限管理员使用
- 管理员可以管理所有角色，包括创建、编辑、删除、权限分配等操作
- **安全限制**: 管理员不能修改、删除自己拥有的角色，防止权限问题导致无法操作
- 角色删除前会检查是否有用户正在使用该角色
- 角色标识（roleKey）创建后不可修改，确保系统稳定性

### 文件上传权限说明
- `/api/user/uploadAvatar` 等用户头像接口需要登录，用户只能管理自己的头像
- `/admin/users/{userId}/uploadAvatar` 等管理员接口需要管理员权限，可以管理任意用户头像
- `/api/upload/deleteFile` 和 `/api/upload/validateImage` 接口需要相应权限
- 支持的图片格式：jpg、jpeg、png、gif、webp
- 文件大小限制：最大5MB

## 使用示例

### 文件上传示例

```javascript
// 上传当前用户头像
const uploadMyAvatar = async (token, file) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/user/uploadAvatar', {
        method: 'POST',
        headers: {
            'Authorization': token
        },
        body: formData
    });
    return response.json();
};

// 删除当前用户头像
const deleteMyAvatar = async (token) => {
    const response = await fetch('/api/user/deleteAvatar', {
        method: 'DELETE',
        headers: {
            'Authorization': token
        }
    });
    return response.json();
};

// 获取当前用户头像
const getMyAvatar = async (token) => {
    const response = await fetch('/api/user/getAvatar', {
        headers: {
            'Authorization': token
        }
    });
    return response.json();
};

// 管理员为用户上传头像
const uploadUserAvatar = async (token, userId, file) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`/admin/users/${userId}/uploadAvatar`, {
        method: 'POST',
        headers: {
            'Authorization': token
        },
        body: formData
    });
    return response.json();
};

// 用户头像上传组件（React）
const UserAvatarUploader = ({ token }) => {
    const [avatarUrl, setAvatarUrl] = useState('');

    const handleFileChange = async (e) => {
        const file = e.target.files[0];
        if (!file) return;

        try {
            const result = await uploadMyAvatar(token, file);
            if (result.code === 200) {
                // 上传成功，更新头像显示
                setAvatarUrl(result.data.fileUrl);
            } else {
                // 处理错误
                alert(result.message);
            }
        } catch (error) {
            console.error('上传失败', error);
        }
    };

    const handleDeleteAvatar = async () => {
        try {
            const result = await deleteMyAvatar(token);
            if (result.code === 200) {
                setAvatarUrl('');
            }
        } catch (error) {
            console.error('删除失败', error);
        }
    };

    return (
        <div>
            {avatarUrl && <img src={avatarUrl} alt="头像" />}
            <input type="file" accept="image/*" onChange={handleFileChange} />
            <button onClick={handleDeleteAvatar}>删除头像</button>
        </div>
    );
};

// 获取可用角色列表
const getAvailableRoles = async (token) => {
    const response = await fetch('/admin/users/getRoles', {
        headers: {
            'Authorization': token
        }
    });
    return response.json();
};

// 为用户分配角色
const assignRolesToUser = async (token, userId, roleIds) => {
    const response = await fetch(`/admin/users/${userId}/assignRoles`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': token
        },
        body: JSON.stringify(roleIds)
    });
    return response.json();
};

// 获取用户角色列表
const getUserRoles = async (token, userId) => {
    const response = await fetch(`/admin/users/${userId}/getRoles`, {
        headers: {
            'Authorization': token
        }
    });
    return response.json();
};

// 用户角色管理组件示例（React）
const UserRoleManager = ({ token, userId }) => {
    const [availableRoles, setAvailableRoles] = useState([]);
    const [userRoles, setUserRoles] = useState([]);
    const [selectedRoles, setSelectedRoles] = useState([]);

    useEffect(() => {
        // 加载可用角色和用户当前角色
        loadData();
    }, [userId]);

    const loadData = async () => {
        try {
            const [rolesResult, userRolesResult] = await Promise.all([
                getAvailableRoles(token),
                getUserRoles(token, userId)
            ]);

            if (rolesResult.code === 200) {
                setAvailableRoles(rolesResult.data);
            }

            if (userRolesResult.code === 200) {
                setUserRoles(userRolesResult.data);
                setSelectedRoles(userRolesResult.data.map(role => role.id));
            }
        } catch (error) {
            console.error('加载数据失败', error);
        }
    };

    const handleSaveRoles = async () => {
        try {
            const result = await assignRolesToUser(token, userId, selectedRoles);
            if (result.code === 200) {
                alert('角色分配成功');
                loadData(); // 重新加载数据
            } else {
                alert(result.message);
            }
        } catch (error) {
            console.error('保存失败', error);
        }
    };

    return (
        <div>
            <h3>角色分配</h3>
            {availableRoles.map(role => (
                <label key={role.id}>
                    <input
                        type="checkbox"
                        checked={selectedRoles.includes(role.id)}
                        onChange={(e) => {
                            if (e.target.checked) {
                                setSelectedRoles([...selectedRoles, role.id]);
                            } else {
                                setSelectedRoles(selectedRoles.filter(id => id !== role.id));
                            }
                        }}
                    />
                    {role.roleName} ({role.description})
                </label>
            ))}
            <button onClick={handleSaveRoles}>保存角色</button>
        </div>
    );
};
```

### JavaScript 示例

## 注意事项

1. **Token管理**: Token有效期为30天，建议前端做好Token的存储和自动刷新
2. **错误处理**: 请根据返回的状态码做相应的错误处理
3. **权限控制**: 某些接口需要特定权限，请确保用户有相应权限后再调用
4. **请求频率**: 建议对API请求做适当的频率控制
5. **HTTPS**: 生产环境建议使用HTTPS协议
6. **用户管理权限**: 用户管理接口仅限管理员使用，普通用户无法访问
7. **数据验证**: 创建和更新用户时，请确保数据格式正确，特别是邮箱和手机号格式
8. **密码安全**: 密码使用SHA-256+盐值加密，前端无需加密，直接传输明文密码
9. **唯一性检查**: 建议在用户输入时实时调用检查接口，提升用户体验
10. **批量操作**: 批量删除用户时请谨慎操作，删除后无法恢复
11. **文件上传**: 头像上传支持jpg、png、gif、webp格式，最大5MB
12. **文件存储**: 当前文件存储在本地E盘，后期可能迁移到OSS
13. **文件访问**: 上传的文件通过 `/files/*` 路径访问，**无需登录验证**
14. **头像管理**: 上传新头像时会自动删除旧头像文件
15. **静态资源**: 图片等静态文件可以直接通过URL访问，不需要Token
16. **角色管理**: 管理员可以为用户分配多个角色，角色决定用户的权限
17. **角色验证**: 创建和编辑用户时会验证角色ID的有效性

## 常见错误码

### 用户管理相关错误
- `400`: 参数验证失败（用户名格式错误、邮箱格式错误等）
- `401`: 未登录或Token过期
- `403`: 权限不足（非管理员用户）
- `409`: 数据冲突（用户名已存在、邮箱已存在等）
- `404`: 用户不存在
- `500`: 服务器内部错误

### 角色管理相关错误
- `400`: 参数验证失败（角色标识格式错误、角色名称长度错误等）
- `401`: 未登录或Token过期
- `403`: 权限不足（非管理员用户）或安全限制（不能修改自己的角色）
- `409`: 数据冲突（角色标识已存在、角色正在被用户使用等）
- `404`: 角色不存在、权限不存在
- `500`: 服务器内部错误

### 文件上传相关错误
- `400`: 文件格式不支持、文件大小超限等
- `401`: 未登录或Token过期
- `403`: 权限不足（非管理员用户）
- `413`: 文件过大
- `415`: 不支持的文件类型
- `500`: 文件保存失败、目录创建失败等

### 错误示例
```json
{
    "code": 409,
    "message": "用户名已存在",
    "timestamp": 1640995200000
}
```

```json
{
    "code": 400,
    "message": "用户名长度必须在3-30个字符之间; 邮箱格式不正确; ",
    "timestamp": 1640995200000
}
```

```json
{
    "code": 415,
    "message": "不支持的文件类型",
    "timestamp": 1640995200000
}
```

```json
{
    "code": 413,
    "message": "文件大小超过限制",
    "timestamp": 1640995200000
}
```

package com.blog.cmrpersonalblog.dto;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 角色更新请求DTO
 */
@Data
public class RoleUpdateRequest {

    /**
     * 角色ID（通过路径参数传递，不需要在请求体中提供）
     */
    private Long roleId;

    /**
     * 角色标识（不允许修改，仅用于显示）
     */
    private String roleKey;

    /**
     * 角色名称（如：管理员、普通用户）
     */
    @Size(min = 2, max = 100, message = "角色名称长度必须在2-100个字符之间")
    private String roleName;

    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;

    /**
     * 角色状态（0正常 1停用）
     */
    @Pattern(regexp = "^[01]$", message = "角色状态值必须为0或1")
    private String status;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 更新人
     */
    private String updateBy;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.UserFollowMapper">

    <!-- 关注用户信息结果映射 -->
    <resultMap id="FollowingUserInfoMap" type="com.blog.cmrpersonalblog.dto.UserInteractionResponse$FollowingUserInfo">
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="avatar" property="avatar" />
        <result column="article_count" property="articleCount" />
        <result column="follower_count" property="followerCount" />
        <result column="follow_time" property="followTime" />
    </resultMap>

    <!-- 粉丝用户信息结果映射 -->
    <resultMap id="FollowerUserInfoMap" type="com.blog.cmrpersonalblog.dto.UserInteractionResponse$FollowerUserInfo">
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="avatar" property="avatar" />
        <result column="article_count" property="articleCount" />
        <result column="follower_count" property="followerCount" />
        <result column="follow_time" property="followTime" />
        <result column="is_follow_back" property="isFollowBack" />
    </resultMap>

    <!-- 获取用户关注列表 -->
    <select id="selectFollowingUsers" resultMap="FollowingUserInfoMap">
        SELECT 
            u.user_id,
            u.user_name,
            u.nick_name,
            u.avatar,
            COALESCE(us.article_count, 0) as article_count,
            COALESCE(us.follower_count, 0) as follower_count,
            uf.create_time as follow_time
        FROM user_follow uf
        INNER JOIN sys_user u ON uf.following_id = u.user_id
        LEFT JOIN user_stats us ON u.user_id = us.user_id
        WHERE uf.follower_id = #{userId} AND uf.status = 1
        ORDER BY uf.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户粉丝列表 -->
    <select id="selectFollowers" resultMap="FollowerUserInfoMap">
        SELECT 
            u.user_id,
            u.user_name,
            u.nick_name,
            u.avatar,
            COALESCE(us.article_count, 0) as article_count,
            COALESCE(us.follower_count, 0) as follower_count,
            uf.create_time as follow_time,
            CASE 
                WHEN #{currentUserId} IS NOT NULL AND EXISTS (
                    SELECT 1 FROM user_follow uf2 
                    WHERE uf2.follower_id = #{currentUserId} 
                    AND uf2.following_id = u.user_id 
                    AND uf2.status = 1
                ) THEN 1 
                ELSE 0 
            END as is_follow_back
        FROM user_follow uf
        INNER JOIN sys_user u ON uf.follower_id = u.user_id
        LEFT JOIN user_stats us ON u.user_id = us.user_id
        WHERE uf.following_id = #{userId} AND uf.status = 1
        ORDER BY uf.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 检查用户是否关注了目标用户 -->
    <select id="checkIsFollowing" resultType="Boolean">
        SELECT COUNT(*) > 0
        FROM user_follow 
        WHERE follower_id = #{followerId} 
        AND following_id = #{followingId} 
        AND status = 1
    </select>

    <!-- 获取用户关注数 -->
    <select id="countFollowing" resultType="Integer">
        SELECT COUNT(*)
        FROM user_follow 
        WHERE follower_id = #{userId} AND status = 1
    </select>

    <!-- 获取用户粉丝数 -->
    <select id="countFollowers" resultType="Integer">
        SELECT COUNT(*)
        FROM user_follow 
        WHERE following_id = #{userId} AND status = 1
    </select>

</mapper>

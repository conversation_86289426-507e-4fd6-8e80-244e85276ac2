package com.blog.cmrpersonalblog.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评论实体类
 */
@Data
@TableName("comment")
public class Comment {

    /**
     * 评论ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文章ID
     */
    @TableField("article_id")
    private Long articleId;

    /**
     * 评论用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 父评论ID，0表示顶级评论
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 回复的用户ID
     */
    @TableField("reply_to_user_id")
    private Long replyToUserId;

    /**
     * 评论内容
     */
    @TableField("content")
    private String content;

    /**
     * 原始内容（未过滤敏感词）
     */
    @TableField("original_content")
    private String originalContent;

    /**
     * 根评论ID，用于构建评论树
     */
    @TableField("root_id")
    private Long rootId;

    /**
     * 评论层级
     */
    @TableField("level")
    private Integer level;

    /**
     * 评论路径，用于树形结构
     */
    @TableField("path")
    private String path;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 回复数
     */
    @TableField("reply_count")
    private Integer replyCount;

    /**
     * 状态 0-待审核 1-已通过 2-已拒绝 3-已删除
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否敏感评论 0-否 1-是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;

    /**
     * 敏感词类型
     */
    @TableField("sensitive_type")
    private String sensitiveType;

    /**
     * 敏感词列表（JSON格式）
     */
    @TableField("sensitive_words")
    private String sensitiveWords;

    /**
     * 敏感度评分（0-100）
     */
    @TableField("sensitive_score")
    private Integer sensitiveScore;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 地理位置
     */
    @TableField("location")
    private String location;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    @TableField("audit_user_id")
    private Long auditUserId;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 是否为顶级评论
     */
    public boolean isTopLevel() {
        return parentId == null || parentId == 0;
    }

    /**
     * 是否需要审核
     */
    public boolean needsAudit() {
        return status != null && status == 0;
    }

    /**
     * 是否已通过审核
     */
    public boolean isApproved() {
        return status != null && status == 1;
    }

    /**
     * 是否被拒绝
     */
    public boolean isRejected() {
        return status != null && status == 2;
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return status != null && status == 3;
    }

    /**
     * 是否为敏感评论
     */
    public boolean isSensitiveComment() {
        return isSensitive != null && isSensitive == 1;
    }
}

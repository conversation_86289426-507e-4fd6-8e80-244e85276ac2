package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.entity.UserBan;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户禁言Mapper接口
 */
public interface UserBanMapper extends BaseMapper<UserBan> {

    /**
     * 检查用户是否被禁言
     * @param userId 用户ID
     * @param banType 禁言类型
     * @return 生效中的禁言记录
     */
    UserBan selectActiveBan(@Param("userId") Long userId, @Param("banType") String banType);

    /**
     * 获取用户所有生效中的禁言记录
     * @param userId 用户ID
     * @return 禁言记录列表
     */
    List<UserBan> selectActiveBansByUserId(@Param("userId") Long userId);

    /**
     * 分页查询禁言记录
     * @param page 分页对象
     * @param userId 用户ID（可选）
     * @param banType 禁言类型（可选）
     * @param status 状态（可选）
     * @return 禁言记录列表
     */
    IPage<UserBan> selectBanRecordsPage(Page<UserBan> page,
                                       @Param("userId") Long userId,
                                       @Param("banType") String banType,
                                       @Param("status") Integer status);

    /**
     * 解除用户禁言
     * @param userId 用户ID
     * @param banType 禁言类型
     * @param operatorId 操作人ID
     * @return 更新数量
     */
    int unbanUser(@Param("userId") Long userId, 
                  @Param("banType") String banType, 
                  @Param("operatorId") Long operatorId);

    /**
     * 批量解除禁言
     * @param userIds 用户ID列表
     * @param banType 禁言类型
     * @param operatorId 操作人ID
     * @return 更新数量
     */
    int batchUnbanUsers(@Param("userIds") List<Long> userIds,
                       @Param("banType") String banType,
                       @Param("operatorId") Long operatorId);

    /**
     * 自动解除过期禁言
     * @return 解除数量
     */
    int autoUnbanExpiredBans();

    /**
     * 获取即将到期的禁言记录
     * @param beforeMinutes 多少分钟内到期
     * @return 禁言记录列表
     */
    List<UserBan> selectExpiringBans(@Param("beforeMinutes") Integer beforeMinutes);

    /**
     * 获取禁言统计信息
     * @return 统计信息
     */
    Map<String, Object> selectBanStatistics();

    /**
     * 获取禁言类型分布
     * @return 类型分布
     */
    List<Map<String, Object>> selectBanTypeDistribution();

    /**
     * 获取最近禁言记录
     * @param limit 数量限制
     * @return 禁言记录列表
     */
    List<UserBan> selectRecentBans(@Param("limit") Integer limit);

    /**
     * 检查用户是否有禁言历史
     * @param userId 用户ID
     * @return 禁言次数
     */
    int countUserBanHistory(@Param("userId") Long userId);

    /**
     * 获取用户禁言历史
     * @param userId 用户ID
     * @return 禁言记录列表
     */
    List<UserBan> selectUserBanHistory(@Param("userId") Long userId);

    /**
     * 延长禁言时间
     * @param banId 禁言记录ID
     * @param newEndTime 新的结束时间
     * @param operatorId 操作人ID
     * @return 更新数量
     */
    int extendBanTime(@Param("banId") Long banId,
                     @Param("newEndTime") LocalDateTime newEndTime,
                     @Param("operatorId") Long operatorId);

    /**
     * 获取操作人的禁言操作记录
     * @param operatorId 操作人ID
     * @param limit 数量限制
     * @return 操作记录列表
     */
    List<UserBan> selectBanOperationsByOperator(@Param("operatorId") Long operatorId,
                                               @Param("limit") Integer limit);
}

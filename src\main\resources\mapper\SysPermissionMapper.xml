<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.SysPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.blog.cmrpersonalblog.entity.SysPermission">
        <id column="id" property="id" />
        <result column="perm_key" property="permKey" />
        <result column="perm_name" property="permName" />
        <result column="description" property="description" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据权限key查询权限信息 -->
    <select id="selectPermissionByPermKey" parameterType="String" resultMap="BaseResultMap">
        SELECT id, perm_key, perm_name, description, 
               create_by, create_time, update_by, update_time
        FROM sys_permission 
        WHERE perm_key = #{permKey}
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="selectPermissionsByRoleId" parameterType="Long" resultMap="BaseResultMap">
        SELECT sp.id, sp.perm_key, sp.perm_name, sp.description,
               sp.create_by, sp.create_time, sp.update_by, sp.update_time
        FROM sys_permission sp
        INNER JOIN sys_role_permission srp ON sp.id = srp.perm_id
        WHERE srp.role_id = #{roleId}
        ORDER BY sp.id
    </select>

    <!-- 根据用户ID查询权限列表（通过角色关联） -->
    <select id="selectPermissionsByUserId" parameterType="Long" resultMap="BaseResultMap">
        SELECT DISTINCT sp.id, sp.perm_key, sp.perm_name, sp.description,
               sp.create_by, sp.create_time, sp.update_by, sp.update_time
        FROM sys_permission sp
        INNER JOIN sys_role_permission srp ON sp.id = srp.perm_id
        INNER JOIN sys_user_role sur ON srp.role_id = sur.role_id
        INNER JOIN sys_role sr ON sur.role_id = sr.id
        WHERE sur.user_id = #{userId} AND sr.status = '0'
        ORDER BY sp.id
    </select>

    <!-- 查询所有权限 -->
    <select id="selectAllPermissions" resultMap="BaseResultMap">
        SELECT id, perm_key, perm_name, description, 
               create_by, create_time, update_by, update_time
        FROM sys_permission 
        ORDER BY id
    </select>

    <!-- 检查权限是否被角色使用 -->
    <select id="countRolesByPermId" parameterType="Long" resultType="int">
        SELECT COUNT(*)
        FROM sys_role_permission 
        WHERE perm_id = #{permId}
    </select>

    <!-- 批量查询权限信息 -->
    <select id="selectPermissionsByPermKeys" resultMap="BaseResultMap">
        SELECT id, perm_key, perm_name, description, 
               create_by, create_time, update_by, update_time
        FROM sys_permission 
        WHERE perm_key IN
        <foreach collection="permKeys" item="permKey" open="(" separator="," close=")">
            #{permKey}
        </foreach>
        ORDER BY id
    </select>

</mapper>

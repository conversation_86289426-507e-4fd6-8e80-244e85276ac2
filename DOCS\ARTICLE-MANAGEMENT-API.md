# 文章管理 API 文档

## 概述

文章管理系统提供了完整的文章管理功能，包括：
- 文章列表查询（支持多维度筛选）
- 内容审核流程（待审/已发布/已下架）
- 批量操作（通过/拒绝/置顶/删除）
- Markdown内容预览和转换

## 权限要求

- **管理员权限**: 所有文章管理接口需要 `admin` 角色
- **登录权限**: Markdown预览功能需要登录即可使用

## API 接口

### 1. 文章列表管理

#### 1.1 分页查询文章列表
```http
GET /admin/articles/list
Authorization: Bearer {admin_token}
```

**查询参数：**
```json
{
  "current": 1,
  "size": 10,
  "title": "文章标题关键词",
  "authorId": 1,
  "authorName": "作者用户名",
  "categoryId": 1,
  "categoryName": "分类名称",
  "status": 1,
  "isTop": 0,
  "isOriginal": 1,
  "tags": "标签关键词",
  "keyword": "全文搜索关键词",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59",
  "sortField": "publish_time",
  "sortOrder": "desc",
  "minViewCount": 100,
  "maxViewCount": 10000,
  "minLikeCount": 10,
  "maxLikeCount": 1000
}
```

**状态说明：**
- `0`: 草稿
- `1`: 已发布
- `2`: 已删除
- `3`: 待审核
- `4`: 审核拒绝
- `5`: 已下架

#### 1.2 获取文章详情
```http
GET /admin/articles/{articleId}
Authorization: Bearer {admin_token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "title": "文章标题",
    "summary": "文章摘要",
    "content": "Markdown内容",
    "htmlContent": "HTML内容",
    "authorName": "作者用户名",
    "categoryName": "分类名称",
    "status": 1,
    "statusText": "已发布",
    "wordCount": 1500,
    "readingTime": 8,
    "publishTime": "2024-01-01T10:00:00"
  }
}
```

### 2. 内容审核流程

#### 2.1 审核文章
```http
POST /admin/articles/audit
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**请求体：**
```json
{
  "articleId": 1,
  "auditResult": 1,
  "auditRemark": "审核通过",
  "auditReason": "内容符合规范",
  "sendNotification": true,
  "suggestions": "建议优化标题"
}
```

**审核结果：**
- `1`: 审核通过
- `4`: 审核拒绝

#### 2.2 获取待审核文章数量
```http
GET /admin/articles/pending-audit-count
Authorization: Bearer {admin_token}
```

#### 2.3 获取文章状态分布
```http
GET /admin/articles/status-distribution
Authorization: Bearer {admin_token}
```

### 3. 批量操作

#### 3.1 批量操作文章
```http
POST /admin/articles/batch
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**请求体：**
```json
{
  "articleIds": [1, 2, 3, 4, 5],
  "operationType": "APPROVE",
  "remark": "批量审核通过",
  "reason": "内容质量良好",
  "sendNotification": true
}
```

**操作类型：**
- `APPROVE`: 审核通过
- `REJECT`: 审核拒绝
- `PUBLISH`: 发布
- `UNPUBLISH`: 下架
- `TOP`: 置顶
- `UNTOP`: 取消置顶
- `DELETE`: 删除
- `RESTORE`: 恢复

### 4. 单个文章操作

#### 4.1 发布文章
```http
POST /admin/articles/{articleId}/publish
Authorization: Bearer {admin_token}
```

#### 4.2 下架文章
```http
POST /admin/articles/{articleId}/unpublish
Authorization: Bearer {admin_token}
```

**查询参数：**
- `reason`: 下架原因（可选）

#### 4.3 置顶/取消置顶
```http
POST /admin/articles/{articleId}/top
Authorization: Bearer {admin_token}
```

**查询参数：**
- `isTop`: true=置顶, false=取消置顶

#### 4.4 删除文章
```http
DELETE /admin/articles/{articleId}
Authorization: Bearer {admin_token}
```

#### 4.5 恢复文章
```http
POST /admin/articles/{articleId}/restore
Authorization: Bearer {admin_token}
```

### 5. 统计和查询

#### 5.1 获取文章统计信息
```http
GET /admin/articles/statistics
Authorization: Bearer {admin_token}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "total_count": 1000,
    "published_count": 800,
    "draft_count": 50,
    "pending_count": 30,
    "rejected_count": 20,
    "deleted_count": 100,
    "top_count": 10,
    "total_views": 50000,
    "total_likes": 5000,
    "total_comments": 2000
  }
}
```

#### 5.2 获取热门文章
```http
GET /admin/articles/popular?limit=10
Authorization: Bearer {admin_token}
```

#### 5.3 获取最新文章
```http
GET /admin/articles/latest?limit=10
Authorization: Bearer {admin_token}
```

#### 5.4 搜索文章
```http
GET /admin/articles/search?keyword=关键词&current=1&size=10
Authorization: Bearer {admin_token}
```

#### 5.5 获取用户文章
```http
GET /admin/articles/user/{userId}?current=1&size=10
Authorization: Bearer {admin_token}
```

#### 5.6 获取分类文章
```http
GET /admin/articles/category/{categoryId}?current=1&size=10
Authorization: Bearer {admin_token}
```

### 6. Markdown 预览功能

#### 6.1 Markdown预览
```http
POST /api/markdown/preview
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体：**
```json
{
  "content": "# 标题\n\n这是一段**粗体**文本。\n\n```java\nSystem.out.println(\"Hello World\");\n```",
  "enableCodeHighlight": true,
  "enableMath": false,
  "enableTable": true,
  "enableTaskList": true,
  "enableStrikethrough": true,
  "enableAutolink": true,
  "theme": "default"
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "markdownContent": "# 标题\n\n这是一段**粗体**文本。",
    "htmlContent": "<h1>标题</h1>\n<p>这是一段<strong>粗体</strong>文本。</p>",
    "wordCount": 15,
    "readingTime": 1,
    "tableOfContents": "[{\"level\":1,\"title\":\"标题\",\"anchor\":\"标题\"}]",
    "hasCodeBlock": true,
    "hasImage": false,
    "hasTable": false,
    "hasMath": false,
    "processTime": 25
  }
}
```

#### 6.2 Markdown转HTML
```http
POST /api/markdown/convert
Authorization: Bearer {token}
Content-Type: text/plain
```

**请求体：** 直接发送Markdown文本

#### 6.3 提取目录
```http
POST /api/markdown/toc
Authorization: Bearer {token}
Content-Type: text/plain
```

#### 6.4 计算字数
```http
POST /api/markdown/word-count
Authorization: Bearer {token}
Content-Type: text/plain
```

#### 6.5 生成摘要
```http
POST /api/markdown/summary?maxLength=200
Authorization: Bearer {token}
Content-Type: text/plain
```

#### 6.6 批量处理
```http
POST /api/markdown/batch-process
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体：**
```json
{
  "markdownContent": "# 标题\n\n内容...",
  "convertToHtml": true,
  "extractToc": true,
  "calculateWordCount": true,
  "generateSummary": true,
  "summaryMaxLength": 200
}
```

## 使用示例

### 1. 审核工作流程

```bash
# 1. 查看待审核文章
curl -X GET "http://localhost:8081/admin/articles/list?status=3" \
  -H "Authorization: Bearer {admin_token}"

# 2. 查看文章详情
curl -X GET "http://localhost:8081/admin/articles/1" \
  -H "Authorization: Bearer {admin_token}"

# 3. 审核通过
curl -X POST "http://localhost:8081/admin/articles/audit" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "articleId": 1,
    "auditResult": 1,
    "auditRemark": "内容质量良好，审核通过"
  }'
```

### 2. 批量操作示例

```bash
# 批量审核通过
curl -X POST "http://localhost:8081/admin/articles/batch" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "articleIds": [1, 2, 3],
    "operationType": "APPROVE",
    "remark": "批量审核通过"
  }'

# 批量置顶
curl -X POST "http://localhost:8081/admin/articles/batch" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "articleIds": [1, 2],
    "operationType": "TOP",
    "remark": "优质内容置顶"
  }'
```

### 3. Markdown预览示例

```bash
# Markdown预览
curl -X POST "http://localhost:8081/api/markdown/preview" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "# 我的文章\n\n这是一篇**测试**文章。\n\n- 列表项1\n- 列表项2",
    "enableCodeHighlight": true,
    "theme": "github"
  }'
```

## 错误处理

所有接口都使用统一的错误响应格式：

```json
{
  "code": 500,
  "message": "错误信息",
  "timestamp": 1640995200000
}
```

**常见错误码：**
- `400`: 请求参数错误
- `401`: 未登录或token无效
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项

1. **权限控制**: 文章管理功能需要管理员权限
2. **数据安全**: 删除操作为软删除，可以恢复
3. **审核流程**: 文章状态变更会记录操作日志
4. **性能优化**: 大批量操作建议分批处理
5. **Markdown支持**: 支持标准Markdown语法和扩展功能

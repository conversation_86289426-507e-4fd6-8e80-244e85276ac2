package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.cmrpersonalblog.entity.SysRole;
import com.blog.cmrpersonalblog.entity.SysPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统角色Mapper接口
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 根据角色key查询角色信息
     * @param roleKey 角色标识
     * @return 角色信息
     */
    SysRole selectRoleByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 根据角色ID查询角色拥有的权限
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询所有启用的角色
     * @return 角色列表
     */
    List<SysRole> selectEnabledRoles();

    /**
     * 根据用户ID查询用户拥有的角色（带权限信息）
     * @param userId 用户ID
     * @return 角色列表（包含权限）
     */
    List<SysRole> selectRolesByUserIdWithPermissions(@Param("userId") Long userId);

    /**
     * 检查角色是否被用户使用
     * @param roleId 角色ID
     * @return 使用该角色的用户数量
     */
    int countUsersByRoleId(@Param("roleId") Long roleId);
}

-- 评论管理系统数据库初始化脚本
-- 确保数据库存在
CREATE DATABASE IF NOT EXISTS `cmr_personal_blog` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `cmr_personal_blog`;

-- 检查并创建评论表（如果不存在）
CREATE TABLE IF NOT EXISTS `comment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '评论ID',
    `article_id` BIGINT NOT NULL COMMENT '文章ID',
    `user_id` BIGINT NOT NULL COMMENT '评论用户ID',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父评论ID，0表示顶级评论',
    `reply_to_user_id` BIGINT COMMENT '回复的用户ID',
    `content` TEXT NOT NULL COMMENT '评论内容',
    `original_content` TEXT NULL COMMENT '原始内容（未过滤敏感词）',
    `root_id` BIGINT DEFAULT 0 COMMENT '根评论ID，用于构建评论树',
    `level` INT DEFAULT 1 COMMENT '评论层级',
    `path` VARCHAR(500) NULL COMMENT '评论路径，用于树形结构',
    `like_count` INT DEFAULT 0 COMMENT '点赞数',
    `reply_count` INT DEFAULT 0 COMMENT '回复数',
    `status` TINYINT DEFAULT 0 COMMENT '状态 0-待审核 1-已通过 2-已拒绝 3-已删除',
    `is_sensitive` TINYINT DEFAULT 0 COMMENT '是否敏感评论 0-否 1-是',
    `sensitive_type` VARCHAR(50) NULL COMMENT '敏感词类型',
    `sensitive_words` TEXT NULL COMMENT '敏感词列表（JSON格式）',
    `sensitive_score` INT DEFAULT 0 COMMENT '敏感度评分（0-100）',
    `ip_address` VARCHAR(128) NULL COMMENT 'IP地址',
    `location` VARCHAR(200) NULL COMMENT '地理位置',
    `user_agent` TEXT NULL COMMENT '用户代理',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `audit_time` DATETIME NULL COMMENT '审核时间',
    `audit_user_id` BIGINT NULL COMMENT '审核人ID',
    `audit_remark` VARCHAR(500) NULL COMMENT '审核备注',
    PRIMARY KEY (`id`),
    INDEX `idx_article_id` (`article_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_root_id` (`root_id`),
    INDEX `idx_level` (`level`),
    INDEX `idx_status` (`status`),
    INDEX `idx_is_sensitive` (`is_sensitive`),
    INDEX `idx_sensitive_type` (`sensitive_type`),
    INDEX `idx_audit_user_id` (`audit_user_id`),
    INDEX `idx_audit_time` (`audit_time`),
    INDEX `idx_ip_address` (`ip_address`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 检查并创建用户禁言表（如果不存在）
CREATE TABLE IF NOT EXISTS `user_ban` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '禁言记录ID',
    `user_id` BIGINT NOT NULL COMMENT '被禁言用户ID',
    `ban_type` VARCHAR(20) NOT NULL COMMENT '禁言类型（COMMENT-禁止评论 LOGIN-禁止登录 ALL-全站禁言）',
    `ban_reason` VARCHAR(500) NULL COMMENT '禁言原因',
    `ban_start_time` DATETIME NOT NULL COMMENT '禁言开始时间',
    `ban_end_time` DATETIME NULL COMMENT '禁言结束时间（NULL表示永久禁言）',
    `is_permanent` TINYINT DEFAULT 0 COMMENT '是否永久禁言 0-否 1-是',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-已解除 1-生效中',
    `operator_id` BIGINT NOT NULL COMMENT '操作人ID',
    `related_comment_id` BIGINT NULL COMMENT '相关评论ID（如果是因为评论被禁言）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_ban_type` (`ban_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_ban_end_time` (`ban_end_time`),
    INDEX `idx_operator_id` (`operator_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户禁言表';

-- 检查并创建敏感词表（如果不存在）
CREATE TABLE IF NOT EXISTS `sensitive_word` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
    `word` VARCHAR(100) NOT NULL COMMENT '敏感词内容',
    `type` VARCHAR(50) NOT NULL COMMENT '敏感词类型（profanity-脏话 politics-政治 advertisement-广告等）',
    `severity` INT DEFAULT 1 COMMENT '严重程度（1-5）',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_word` (`word`),
    INDEX `idx_type` (`type`),
    INDEX `idx_severity` (`severity`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- 确保文章表存在（评论需要关联文章）
CREATE TABLE IF NOT EXISTS `article` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '文章ID',
    `title` VARCHAR(200) NOT NULL COMMENT '文章标题',
    `content` LONGTEXT COMMENT '文章内容',
    `summary` VARCHAR(500) COMMENT '文章摘要',
    `category_id` BIGINT COMMENT '分类ID',
    `tags` VARCHAR(200) COMMENT '标签',
    `cover_image` VARCHAR(500) COMMENT '封面图片',
    `author_id` BIGINT NOT NULL COMMENT '作者ID',
    `view_count` INT DEFAULT 0 COMMENT '浏览数',
    `like_count` INT DEFAULT 0 COMMENT '点赞数',
    `comment_count` INT DEFAULT 0 COMMENT '评论数',
    `collect_count` INT DEFAULT 0 COMMENT '收藏数',
    `share_count` INT DEFAULT 0 COMMENT '分享数',
    `is_original` TINYINT DEFAULT 1 COMMENT '是否原创 0-转载 1-原创',
    `is_top` TINYINT DEFAULT 0 COMMENT '是否置顶 0-否 1-是',
    `status` TINYINT DEFAULT 0 COMMENT '状态 0-草稿 1-已发布 2-已删除',
    `publish_time` DATETIME COMMENT '发布时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_author_id` (`author_id`),
    INDEX `idx_category_id` (`category_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 确保用户表存在（评论需要关联用户）
CREATE TABLE IF NOT EXISTS `sys_user` (
    `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `user_name` VARCHAR(30) NOT NULL COMMENT '用户账号',
    `nick_name` VARCHAR(30) NOT NULL COMMENT '用户昵称',
    `email` VARCHAR(50) COMMENT '用户邮箱',
    `phone_number` VARCHAR(11) COMMENT '手机号码',
    `sex` CHAR(1) COMMENT '用户性别（0男 1女 2未知）',
    `avatar` VARCHAR(100) COMMENT '头像地址',
    `password` VARCHAR(100) COMMENT '密码',
    `status` CHAR(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
    `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `login_ip` VARCHAR(128) COMMENT '最后登录IP',
    `login_date` DATETIME COMMENT '最后登录时间',
    `create_by` VARCHAR(64) COMMENT '创建者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(64) COMMENT '更新者',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark` VARCHAR(500) COMMENT '备注',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_user_name` (`user_name`),
    INDEX `idx_status` (`status`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 确保用户点赞表存在（评论点赞功能需要）
CREATE TABLE IF NOT EXISTS `user_like` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `target_id` BIGINT NOT NULL COMMENT '目标ID（文章ID或评论ID）',
    `target_type` TINYINT NOT NULL COMMENT '目标类型 1-文章 2-评论',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-取消点赞 1-点赞',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_target` (`user_id`, `target_id`, `target_type`),
    INDEX `idx_target` (`target_id`, `target_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';

-- 插入默认敏感词数据（如果表为空）
INSERT IGNORE INTO `sensitive_word` (`word`, `type`, `severity`) VALUES
-- 脏话类
('傻逼', 'profanity', 4),
('草泥马', 'profanity', 4),
('去死', 'profanity', 4),
('滚蛋', 'profanity', 3),
('白痴', 'profanity', 3),
('智障', 'profanity', 3),
('废物', 'profanity', 3),
('垃圾', 'profanity', 2),
('操你', 'profanity', 4),
('妈的', 'profanity', 3),
('狗屎', 'profanity', 3),
('混蛋', 'profanity', 3),
('王八蛋', 'profanity', 3),
('贱人', 'profanity', 4),
('婊子', 'profanity', 4),

-- 政治敏感词
('法轮功', 'politics', 5),
('六四', 'politics', 5),
('天安门', 'politics', 5),
('达赖', 'politics', 5),
('台独', 'politics', 5),
('藏独', 'politics', 5),
('疆独', 'politics', 5),
('反政府', 'politics', 5),
('颠覆国家', 'politics', 5),
('煽动分裂', 'politics', 5),

-- 广告词
('代开发票', 'advertisement', 3),
('办证', 'advertisement', 3),
('刻章', 'advertisement', 3),
('贷款', 'advertisement', 2),
('股票推荐', 'advertisement', 3),
('彩票', 'advertisement', 3),
('博彩', 'advertisement', 4),
('赌博', 'advertisement', 4),
('色情', 'advertisement', 4),
('成人用品', 'advertisement', 3),
('微信号', 'advertisement', 2),
('QQ群', 'advertisement', 2),

-- 暴力词汇
('杀死', 'violence', 4),
('自杀', 'violence', 4),
('爆炸', 'violence', 4),
('恐怖袭击', 'violence', 5),
('血腥', 'violence', 3),
('暴力', 'violence', 3),
('砍死', 'violence', 4),
('毒死', 'violence', 4),
('勒死', 'violence', 4),
('枪杀', 'violence', 4),

-- 违法内容
('毒品', 'illegal', 5),
('海洛因', 'illegal', 5),
('冰毒', 'illegal', 5),
('摇头丸', 'illegal', 5),
('大麻', 'illegal', 5),
('制毒', 'illegal', 5),
('贩毒', 'illegal', 5),
('洗钱', 'illegal', 5),
('诈骗', 'illegal', 4),
('传销', 'illegal', 4),
('非法集资', 'illegal', 4);

-- 显示初始化结果
SELECT '=== 评论管理系统数据库初始化完成 ===' as message;

-- 显示表结构信息
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '记录数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'cmr_personal_blog' 
AND TABLE_NAME IN ('comment', 'user_ban', 'sensitive_word', 'article', 'sys_user', 'user_like')
ORDER BY TABLE_NAME;

-- 显示敏感词统计
SELECT 
    type as '敏感词类型',
    COUNT(*) as '数量',
    AVG(severity) as '平均严重程度'
FROM sensitive_word 
WHERE status = 1 
GROUP BY type 
ORDER BY COUNT(*) DESC;

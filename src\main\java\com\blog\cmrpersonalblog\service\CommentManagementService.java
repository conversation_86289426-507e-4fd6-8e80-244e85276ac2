package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.cmrpersonalblog.dto.*;

import java.util.List;
import java.util.Map;
import com.blog.cmrpersonalblog.entity.Comment;

import java.util.List;
import java.util.Map;

/**
 * 评论管理服务接口
 */
public interface CommentManagementService {

    /**
     * 分页查询评论列表
     * @param queryRequest 查询条件
     * @return 评论分页列表
     */
    IPage<CommentManagementResponse> getCommentList(CommentQueryRequest queryRequest);

    /**
     * 获取评论详情
     * @param commentId 评论ID
     * @return 评论详情
     */
    CommentManagementResponse getCommentDetail(Long commentId);

    /**
     * 获取评论树形结构
     * @param articleId 文章ID
     * @param rootId 根评论ID（可选）
     * @return 评论树
     */
    List<CommentManagementResponse> getCommentTree(Long articleId, Long rootId);

    /**
     * 审核评论
     * @param auditRequest 审核请求
     * @param auditorId 审核人ID
     * @return 审核结果
     */
    boolean auditComment(CommentAuditRequest auditRequest, Long auditorId);

    /**
     * 批量操作评论
     * @param batchRequest 批量操作请求
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Map<String, Object> batchOperateComments(CommentBatchOperationRequest batchRequest, Long operatorId);

    /**
     * 删除评论
     * @param commentId 评论ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean deleteComment(Long commentId, Long operatorId);

    /**
     * 恢复评论
     * @param commentId 评论ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean restoreComment(Long commentId, Long operatorId);

    /**
     * 标记敏感评论
     * @param commentId 评论ID
     * @param sensitiveType 敏感类型
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean markSensitive(Long commentId, String sensitiveType, Long operatorId);

    /**
     * 取消敏感标记
     * @param commentId 评论ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean unmarkSensitive(Long commentId, Long operatorId);

    /**
     * 检测评论敏感词
     * @param content 评论内容
     * @return 检测结果
     */
    SensitiveWordCheckResult checkCommentSensitiveWords(String content);

    /**
     * 自动审核评论（基于敏感词检测）
     * @param comment 评论对象
     * @return 审核结果
     */
    boolean autoAuditComment(Comment comment);

    /**
     * 获取评论统计信息
     * @return 统计信息
     */
    Map<String, Object> getCommentStatistics();

    /**
     * 获取待审核评论数量
     * @return 待审核数量
     */
    Long getPendingAuditCount();

    /**
     * 获取敏感评论数量
     * @return 敏感评论数量
     */
    Long getSensitiveCommentCount();

    /**
     * 获取评论状态分布
     * @return 状态分布
     */
    Map<String, Long> getCommentStatusDistribution();

    /**
     * 获取敏感词类型分布
     * @return 敏感词类型分布
     */
    Map<String, Long> getSensitiveTypeDistribution();

    /**
     * 分页获取最新评论列表
     * @param request 查询请求
     * @return 最新评论分页列表
     */
    IPage<CommentManagementResponse> getLatestComments(LatestCommentsRequest request);

    /**
     * 分页获取热门评论列表
     * @param request 查询请求
     * @return 热门评论分页列表
     */
    IPage<CommentManagementResponse> getPopularComments(PopularCommentsRequest request);

    /**
     * 搜索评论
     * @param keyword 关键词
     * @param current 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<CommentManagementResponse> searchComments(String keyword, Long current, Long size);

    /**
     * 获取用户评论列表
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 用户评论列表
     */
    IPage<CommentManagementResponse> getUserComments(Long userId, Long current, Long size);

    /**
     * 获取文章评论列表
     * @param articleId 文章ID
     * @param current 页码
     * @param size 每页大小
     * @return 文章评论列表
     */
    IPage<CommentManagementResponse> getArticleComments(Long articleId, Long current, Long size);

    /**
     * 更新评论统计数据
     * @param commentId 评论ID
     * @param field 字段名（like_count, reply_count等）
     * @param increment 增量
     * @return 是否成功
     */
    boolean updateCommentStats(Long commentId, String field, Integer increment);

    /**
     * 重新计算评论统计数据
     * @param commentId 评论ID
     * @return 是否成功
     */
    boolean recalculateCommentStats(Long commentId);

    /**
     * 构建评论路径
     * @param comment 评论对象
     * @return 评论路径
     */
    String buildCommentPath(Comment comment);

    /**
     * 获取评论的所有子评论
     * @param commentId 评论ID
     * @return 子评论ID列表
     */
    List<Long> getAllChildCommentIds(Long commentId);

    /**
     * 根据IP地址获取评论列表
     * @param ipAddress IP地址
     * @param current 页码
     * @param size 每页大小
     * @return 评论列表
     */
    IPage<CommentManagementResponse> getCommentsByIpAddress(String ipAddress, Long current, Long size);
}

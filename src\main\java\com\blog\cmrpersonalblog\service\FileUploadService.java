package com.blog.cmrpersonalblog.service;

import com.blog.cmrpersonalblog.dto.FileUploadResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传服务接口
 */
public interface FileUploadService {

    /**
     * 上传用户头像
     * @param file 上传的文件
     * @param userId 用户ID（可选，用于文件命名）
     * @return 上传结果
     */
    FileUploadResponse uploadAvatar(MultipartFile file, Long userId);

    /**
     * 删除文件
     * @param relativePath 文件相对路径
     * @return 是否删除成功
     */
    boolean deleteFile(String relativePath);

    /**
     * 验证文件是否为有效的图片
     * @param file 上传的文件
     * @return 是否有效
     */
    boolean isValidImage(MultipartFile file);

    /**
     * 生成唯一文件名
     * @param originalFileName 原始文件名
     * @param userId 用户ID
     * @return 新文件名
     */
    String generateFileName(String originalFileName, Long userId);

    /**
     * 获取文件扩展名
     * @param fileName 文件名
     * @return 扩展名
     */
    String getFileExtension(String fileName);
}

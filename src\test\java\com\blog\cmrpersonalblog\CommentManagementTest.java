package com.blog.cmrpersonalblog;

import com.blog.cmrpersonalblog.dto.*;
import com.blog.cmrpersonalblog.entity.Comment;
import com.blog.cmrpersonalblog.entity.UserBan;
import com.blog.cmrpersonalblog.service.CommentManagementService;
import com.blog.cmrpersonalblog.service.SensitiveWordService;
import com.blog.cmrpersonalblog.service.UserBanService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 评论管理系统测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class CommentManagementTest {

    @Autowired
    private CommentManagementService commentManagementService;

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Autowired
    private UserBanService userBanService;

    /**
     * 测试敏感词检测功能
     */
    @Test
    public void testSensitiveWordDetection() {
        System.out.println("=== 测试敏感词检测功能 ===");
        
        // 测试正常文本
        String normalText = "这是一条正常的评论内容";
        SensitiveWordCheckResult normalResult = sensitiveWordService.checkSensitiveWords(normalText);
        System.out.println("正常文本检测结果:");
        System.out.println("- 是否包含敏感词: " + normalResult.isHasSensitiveWords());
        System.out.println("- 敏感度评分: " + normalResult.getSensitiveScore());
        System.out.println("- 建议操作: " + normalResult.getSuggestedAction());
        
        // 测试包含敏感词的文本
        String sensitiveText = "这个作者真是个傻逼，写的都是垃圾内容";
        SensitiveWordCheckResult sensitiveResult = sensitiveWordService.checkSensitiveWords(sensitiveText);
        System.out.println("\n敏感文本检测结果:");
        System.out.println("- 是否包含敏感词: " + sensitiveResult.isHasSensitiveWords());
        System.out.println("- 敏感度评分: " + sensitiveResult.getSensitiveScore());
        System.out.println("- 建议操作: " + sensitiveResult.getSuggestedAction());
        System.out.println("- 过滤后内容: " + sensitiveResult.getFilteredContent());
        if (sensitiveResult.getSensitiveWords() != null) {
            System.out.println("- 检测到的敏感词:");
            for (SensitiveWordCheckResult.SensitiveWord word : sensitiveResult.getSensitiveWords()) {
                System.out.println("  * " + word.getWord() + " (类型: " + word.getType() + ", 严重程度: " + word.getSeverity() + ")");
            }
        }
        
        // 测试政治敏感词
        String politicalText = "六四事件是一个敏感话题";
        SensitiveWordCheckResult politicalResult = sensitiveWordService.checkSensitiveWords(politicalText);
        System.out.println("\n政治敏感文本检测结果:");
        System.out.println("- 是否包含敏感词: " + politicalResult.isHasSensitiveWords());
        System.out.println("- 敏感度评分: " + politicalResult.getSensitiveScore());
        System.out.println("- 建议操作: " + politicalResult.getSuggestedAction());
        System.out.println("- 过滤后内容: " + politicalResult.getFilteredContent());
    }

    /**
     * 测试评论管理功能
     */
    @Test
    public void testCommentManagement() {
        System.out.println("\n=== 测试评论管理功能 ===");
        
        try {
            // 测试获取评论统计信息
            Map<String, Object> statistics = commentManagementService.getCommentStatistics();
            System.out.println("评论统计信息:");
            statistics.forEach((key, value) -> 
                System.out.println("- " + key + ": " + value));
            
            // 测试获取待审核评论数量
            Long pendingCount = commentManagementService.getPendingAuditCount();
            System.out.println("\n待审核评论数量: " + pendingCount);
            
            // 测试获取敏感评论数量
            Long sensitiveCount = commentManagementService.getSensitiveCommentCount();
            System.out.println("敏感评论数量: " + sensitiveCount);
            
            // 测试获取评论状态分布
            Map<String, Long> statusDistribution = commentManagementService.getCommentStatusDistribution();
            System.out.println("\n评论状态分布:");
            statusDistribution.forEach((status, count) -> 
                System.out.println("- " + status + ": " + count));
            
            // 测试获取敏感词类型分布
            Map<String, Long> typeDistribution = commentManagementService.getSensitiveTypeDistribution();
            System.out.println("\n敏感词类型分布:");
            typeDistribution.forEach((type, count) -> 
                System.out.println("- " + type + ": " + count));
            
        } catch (Exception e) {
            System.err.println("评论管理功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试评论查询功能
     */
    @Test
    public void testCommentQuery() {
        System.out.println("\n=== 测试评论查询功能 ===");
        
        try {
            // 创建查询请求
            CommentQueryRequest queryRequest = new CommentQueryRequest();
            queryRequest.setCurrent(1L);
            queryRequest.setSize(5L);
            queryRequest.setStatus(1); // 查询已通过的评论
            
            // 执行查询
            var pageResult = commentManagementService.getCommentList(queryRequest);
            System.out.println("查询结果:");
            System.out.println("- 总记录数: " + pageResult.getTotal());
            System.out.println("- 当前页: " + pageResult.getCurrent());
            System.out.println("- 每页大小: " + pageResult.getSize());
            System.out.println("- 总页数: " + pageResult.getPages());
            
            if (pageResult.getRecords() != null && !pageResult.getRecords().isEmpty()) {
                System.out.println("- 评论列表:");
                for (CommentManagementResponse comment : pageResult.getRecords()) {
                    System.out.println("  * ID: " + comment.getId() + 
                                     ", 用户: " + comment.getUserName() + 
                                     ", 内容: " + comment.getContent().substring(0, Math.min(20, comment.getContent().length())) + "..." +
                                     ", 状态: " + comment.getStatusText() +
                                     ", 敏感: " + (comment.getIsSensitive() == 1 ? "是" : "否"));
                }
            }
            
        } catch (Exception e) {
            System.err.println("评论查询功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试用户禁言功能
     */
    @Test
    public void testUserBanManagement() {
        System.out.println("\n=== 测试用户禁言功能 ===");
        
        try {
            // 测试获取禁言统计信息
            Map<String, Object> banStatistics = userBanService.getBanStatistics();
            System.out.println("禁言统计信息:");
            banStatistics.forEach((key, value) -> 
                System.out.println("- " + key + ": " + value));
            
            // 测试获取禁言类型分布
            Map<String, Long> typeDistribution = userBanService.getBanTypeDistribution();
            System.out.println("\n禁言类型分布:");
            typeDistribution.forEach((type, count) -> 
                System.out.println("- " + type + ": " + count));
            
            // 测试检查用户禁言状态
            Long testUserId = 5L; // 测试数据中的被禁用户
            boolean isBanned = userBanService.isUserBanned(testUserId, "COMMENT");
            System.out.println("\n用户 " + testUserId + " 评论禁言状态: " + (isBanned ? "已禁言" : "正常"));
            
            // 测试获取用户禁言状态详情
            Map<String, Object> userBanStatus = userBanService.getUserBanStatus(testUserId);
            System.out.println("用户禁言详情:");
            userBanStatus.forEach((key, value) -> {
                if (!"activeBans".equals(key)) { // 跳过复杂对象的打印
                    System.out.println("- " + key + ": " + value);
                }
            });
            
            // 测试获取最近禁言记录
            List<UserBan> recentBans = userBanService.getRecentBans(3);
            System.out.println("\n最近禁言记录 (最多3条):");
            for (UserBan ban : recentBans) {
                System.out.println("- 用户ID: " + ban.getUserId() + 
                                 ", 类型: " + ban.getBanType() + 
                                 ", 原因: " + ban.getBanReason() + 
                                 ", 状态: " + (ban.getStatus() == 1 ? "生效中" : "已解除"));
            }
            
        } catch (Exception e) {
            System.err.println("用户禁言功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试敏感词管理功能
     */
    @Test
    public void testSensitiveWordManagement() {
        System.out.println("\n=== 测试敏感词管理功能 ===");
        
        try {
            // 测试获取敏感词统计
            Map<String, Object> statistics = sensitiveWordService.getSensitiveWordStatistics();
            System.out.println("敏感词统计信息:");
            statistics.forEach((key, value) -> 
                System.out.println("- " + key + ": " + value));
            
            // 测试获取所有敏感词
            var allWords = sensitiveWordService.getAllSensitiveWords();
            System.out.println("\n敏感词库大小: " + allWords.size());
            
            // 测试按类型获取敏感词
            var profanityWords = sensitiveWordService.getSensitiveWordsByType("profanity");
            System.out.println("脏话类敏感词数量: " + profanityWords.size());
            
            var politicsWords = sensitiveWordService.getSensitiveWordsByType("politics");
            System.out.println("政治类敏感词数量: " + politicsWords.size());
            
            var adWords = sensitiveWordService.getSensitiveWordsByType("advertisement");
            System.out.println("广告类敏感词数量: " + adWords.size());
            
            // 测试验证敏感词配置
            boolean isValid = sensitiveWordService.validateSensitiveWordConfig();
            System.out.println("\n敏感词配置验证: " + (isValid ? "有效" : "无效"));
            
        } catch (Exception e) {
            System.err.println("敏感词管理功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 综合功能测试
     */
    @Test
    public void testIntegratedFunctionality() {
        System.out.println("\n=== 综合功能测试 ===");
        
        try {
            // 模拟评论发布和自动审核流程
            Comment testComment = new Comment();
            testComment.setId(999L);
            testComment.setArticleId(1L);
            testComment.setUserId(2L);
            testComment.setContent("这是一条包含傻逼的测试评论");
            testComment.setParentId(0L);
            testComment.setLevel(1);
            testComment.setLikeCount(0);
            testComment.setReplyCount(0);
            testComment.setStatus(0); // 待审核
            
            System.out.println("模拟评论自动审核流程:");
            System.out.println("原始评论内容: " + testComment.getContent());
            
            // 执行自动审核
            boolean auditResult = commentManagementService.autoAuditComment(testComment);
            System.out.println("自动审核结果: " + (auditResult ? "成功" : "失败"));
            System.out.println("审核后状态: " + testComment.getStatus() + 
                             " (" + getStatusText(testComment.getStatus()) + ")");
            System.out.println("过滤后内容: " + testComment.getContent());
            System.out.println("是否敏感: " + (testComment.getIsSensitive() == 1 ? "是" : "否"));
            if (testComment.getIsSensitive() == 1) {
                System.out.println("敏感类型: " + testComment.getSensitiveType());
                System.out.println("敏感度评分: " + testComment.getSensitiveScore());
            }
            
        } catch (Exception e) {
            System.err.println("综合功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取状态文本描述
     */
    private String getStatusText(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待审核";
            case 1: return "已通过";
            case 2: return "已拒绝";
            case 3: return "已删除";
            default: return "未知";
        }
    }
}

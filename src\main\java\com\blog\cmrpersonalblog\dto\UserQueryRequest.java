package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 用户查询请求DTO
 */
@Data
public class UserQueryRequest {

    /**
     * 用户名（模糊查询）
     */
    private String userName;

    /**
     * 用户昵称（模糊查询）
     */
    private String nickName;

    /**
     * 邮箱（模糊查询）
     */
    private String email;

    /**
     * 手机号码（模糊查询）
     */
    private String phonenumber;

    /**
     * 状态（0=禁用，1=启用）
     */
    private Integer status;

    /**
     * 角色ID（精确查询）
     */
    private Long roleId;

    /**
     * 性别
     */
    private String sex;

    /**
     * 当前页码（默认1）
     */
    private Integer pageNum = 1;

    /**
     * 每页大小（默认10）
     */
    private Integer pageSize = 10;

    /**
     * 排序字段（默认按创建时间）
     */
    private String orderBy = "create_time";

    /**
     * 排序方向（asc/desc，默认desc）
     */
    private String orderDirection = "desc";
}

package com.blog.cmrpersonalblog.dto;

import com.blog.cmrpersonalblog.entity.SysPermission;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色响应DTO
 */
@Data
public class RoleResponse {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色标识（如：admin、editor）
     */
    private String roleKey;

    /**
     * 角色名称（如：管理员、普通用户）
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 角色状态（0正常 1停用）
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 角色权限列表
     */
    private List<SysPermission> permissions;

    /**
     * 使用该角色的用户数量
     */
    private Integer userCount;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 获取状态描述
     */
    public String getStatusText() {
        if ("0".equals(status)) {
            return "正常";
        } else if ("1".equals(status)) {
            return "停用";
        }
        return "未知";
    }
}

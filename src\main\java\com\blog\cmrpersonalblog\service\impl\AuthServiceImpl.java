package com.blog.cmrpersonalblog.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.blog.cmrpersonalblog.dto.LoginRequest;
import com.blog.cmrpersonalblog.dto.LoginResponse;
import com.blog.cmrpersonalblog.entity.SysUser;
import com.blog.cmrpersonalblog.service.AuthService;
import com.blog.cmrpersonalblog.service.SysUserService;
import com.blog.cmrpersonalblog.service.UserActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 认证服务实现类
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private UserActivityService userActivityService;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        String clientIp = getClientIp();
        String userAgent = request.getHeader("User-Agent");

        // 1. 验证用户名和密码
        SysUser user = sysUserService.getUserByUserName(loginRequest.getUserName());
        if (user == null) {
            // 记录登录失败（用户不存在）
            userActivityService.recordLoginActivity(null, false, clientIp, userAgent, "用户不存在");
            throw new RuntimeException("用户名或密码错误");
        }

        if (user.getStatus() != 1) {
            // 记录登录失败（用户被禁用）
            userActivityService.recordLoginActivity(user.getUserId(), false, clientIp, userAgent, "用户已被禁用");
            throw new RuntimeException("用户已被禁用");
        }

        if (!sysUserService.verifyPassword(user, loginRequest.getPassword())) {
            // 记录登录失败（密码错误）
            userActivityService.recordLoginActivity(user.getUserId(), false, clientIp, userAgent, "密码错误");
            throw new RuntimeException("用户名或密码错误");
        }

        // 2. 更新用户登录信息
        user.setLoginIp(clientIp);
        user.setLoginDate(LocalDateTime.now());
        sysUserService.updateUserLoginInfo(user);

        // 3. 执行登录操作
        StpUtil.login(user.getUserId());

        // 4. 记录登录成功
        userActivityService.recordLoginActivity(user.getUserId(), true, clientIp, userAgent, null);

        // 5. 获取用户角色和权限
        List<String> roles = sysUserService.getUserRoles(user.getUserId());
        List<String> permissions = sysUserService.getUserPermissions(user.getUserId());

        // 6. 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(StpUtil.getTokenValue());
        response.setUserId(user.getUserId());
        response.setUserName(user.getUserName());
        response.setNickName(user.getNickName());
        response.setRoles(roles);
        response.setPermissions(permissions);
        response.setExpireTime(StpUtil.getTokenTimeout());

        return response;
    }

    @Override
    public boolean logout() {
        try {
            StpUtil.logout();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public LoginResponse refreshToken() {
        // 检查是否已登录
        if (!StpUtil.isLogin()) {
            throw new RuntimeException("用户未登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();
        SysUser user = sysUserService.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 获取用户角色和权限
        List<String> roles = sysUserService.getUserRoles(userId);
        List<String> permissions = sysUserService.getUserPermissions(userId);

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(StpUtil.getTokenValue());
        response.setUserId(userId);
        response.setUserName(user.getUserName());
        response.setNickName(user.getNickName());
        response.setRoles(roles);
        response.setPermissions(permissions);
        response.setExpireTime(StpUtil.getTokenTimeout());

        return response;
    }

    @Override
    public LoginResponse getCurrentUser() {
        return refreshToken();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp() {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        if (xip != null && !xip.isEmpty() && !"unknown".equalsIgnoreCase(xip)) {
            return xip;
        }
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            return xfor;
        }
        return request.getRemoteAddr();
    }
}

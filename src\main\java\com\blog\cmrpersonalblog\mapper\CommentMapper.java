package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.dto.CommentQueryRequest;
import com.blog.cmrpersonalblog.dto.CommentManagementResponse;
import com.blog.cmrpersonalblog.dto.LatestCommentsRequest;
import com.blog.cmrpersonalblog.dto.PopularCommentsRequest;
import com.blog.cmrpersonalblog.entity.Comment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 评论Mapper接口
 */
public interface CommentMapper extends BaseMapper<Comment> {

    /**
     * 分页查询评论管理列表
     * @param page 分页对象
     * @param query 查询条件
     * @return 评论管理列表
     */
    IPage<CommentManagementResponse> selectCommentManagementPage(Page<CommentManagementResponse> page, 
                                                                 @Param("query") CommentQueryRequest query);

    /**
     * 获取评论详情（包含用户和文章信息）
     * @param commentId 评论ID
     * @return 评论详情
     */
    CommentManagementResponse selectCommentDetailById(@Param("commentId") Long commentId);

    /**
     * 获取评论树形结构
     * @param articleId 文章ID
     * @param rootId 根评论ID（可选）
     * @return 评论树
     */
    List<CommentManagementResponse> selectCommentTree(@Param("articleId") Long articleId, 
                                                      @Param("rootId") Long rootId);

    /**
     * 获取子评论列表
     * @param parentId 父评论ID
     * @return 子评论列表
     */
    List<CommentManagementResponse> selectChildComments(@Param("parentId") Long parentId);

    /**
     * 批量更新评论状态
     * @param commentIds 评论ID列表
     * @param status 目标状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 更新数量
     */
    int batchUpdateCommentStatus(@Param("commentIds") List<Long> commentIds, 
                                @Param("status") Integer status, 
                                @Param("auditUserId") Long auditUserId,
                                @Param("auditRemark") String auditRemark);

    /**
     * 批量标记敏感评论
     * @param commentIds 评论ID列表
     * @param isSensitive 是否敏感
     * @param sensitiveType 敏感类型
     * @return 更新数量
     */
    int batchUpdateSensitiveStatus(@Param("commentIds") List<Long> commentIds, 
                                  @Param("isSensitive") Integer isSensitive,
                                  @Param("sensitiveType") String sensitiveType);

    /**
     * 批量删除评论
     * @param commentIds 评论ID列表
     * @return 删除数量
     */
    int batchDeleteComments(@Param("commentIds") List<Long> commentIds);

    /**
     * 更新评论审核信息
     * @param commentId 评论ID
     * @param status 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 更新数量
     */
    int updateCommentAuditInfo(@Param("commentId") Long commentId,
                              @Param("status") Integer status,
                              @Param("auditUserId") Long auditUserId,
                              @Param("auditRemark") String auditRemark);

    /**
     * 获取评论统计信息
     * @return 统计信息
     */
    Map<String, Object> selectCommentStatistics();

    /**
     * 获取待审核评论数量
     * @return 待审核数量
     */
    Long selectPendingAuditCount();

    /**
     * 获取敏感评论数量
     * @return 敏感评论数量
     */
    Long selectSensitiveCommentCount();

    /**
     * 获取评论状态分布
     * @return 状态分布
     */
    List<Map<String, Object>> selectCommentStatusDistribution();

    /**
     * 获取敏感词类型分布
     * @return 敏感词类型分布
     */
    List<Map<String, Object>> selectSensitiveTypeDistribution();

    /**
     * 分页获取最新评论列表
     * @param page 分页对象
     * @param request 查询条件
     * @return 最新评论分页列表
     */
    IPage<CommentManagementResponse> selectLatestCommentsPage(Page<CommentManagementResponse> page,
                                                              @Param("request") LatestCommentsRequest request);

    /**
     * 分页获取热门评论列表
     * @param page 分页对象
     * @param request 查询条件
     * @return 热门评论分页列表
     */
    IPage<CommentManagementResponse> selectPopularCommentsPage(Page<CommentManagementResponse> page,
                                                               @Param("request") PopularCommentsRequest request);

    /**
     * 搜索评论
     * @param page 分页对象
     * @param keyword 关键词
     * @return 搜索结果
     */
    IPage<CommentManagementResponse> searchComments(Page<CommentManagementResponse> page, 
                                                   @Param("keyword") String keyword);

    /**
     * 获取用户评论管理列表
     * @param page 分页对象
     * @param userId 用户ID
     * @return 用户评论列表
     */
    IPage<CommentManagementResponse> selectUserCommentManagementPage(Page<CommentManagementResponse> page, 
                                                                     @Param("userId") Long userId);

    /**
     * 获取文章评论管理列表
     * @param page 分页对象
     * @param articleId 文章ID
     * @return 文章评论列表
     */
    IPage<CommentManagementResponse> selectArticleCommentManagementPage(Page<CommentManagementResponse> page, 
                                                                        @Param("articleId") Long articleId);

    /**
     * 更新评论统计数据
     * @param commentId 评论ID
     * @param field 字段名
     * @param increment 增量
     */
    void updateCommentStats(@Param("commentId") Long commentId, 
                           @Param("field") String field, 
                           @Param("increment") Integer increment);

    /**
     * 重新计算评论统计数据
     * @param commentId 评论ID
     * @return 更新数量
     */
    int recalculateCommentStats(@Param("commentId") Long commentId);

    /**
     * 获取评论回复路径
     * @param commentId 评论ID
     * @return 回复路径
     */
    String selectCommentPath(@Param("commentId") Long commentId);

    /**
     * 更新评论路径
     * @param commentId 评论ID
     * @param path 路径
     * @return 更新数量
     */
    int updateCommentPath(@Param("commentId") Long commentId, @Param("path") String path);

    /**
     * 获取评论的所有子评论ID
     * @param commentId 评论ID
     * @return 子评论ID列表
     */
    List<Long> selectAllChildCommentIds(@Param("commentId") Long commentId);

    /**
     * 根据IP地址获取评论列表
     * @param page 分页对象
     * @param ipAddress IP地址
     * @return 评论列表
     */
    IPage<CommentManagementResponse> selectCommentsByIpAddress(Page<CommentManagementResponse> page, 
                                                              @Param("ipAddress") String ipAddress);
}

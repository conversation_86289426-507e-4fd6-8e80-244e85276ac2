package com.blog.cmrpersonalblog.dto;

import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 评论批量操作请求DTO
 */
@Data
public class CommentBatchOperationRequest {

    /**
     * 评论ID列表
     */
    @NotEmpty(message = "评论ID列表不能为空")
    private List<Long> commentIds;

    /**
     * 操作类型
     * APPROVE - 审核通过
     * REJECT - 审核拒绝
     * DELETE - 删除
     * RESTORE - 恢复
     * MARK_SENSITIVE - 标记为敏感
     * UNMARK_SENSITIVE - 取消敏感标记
     */
    @NotNull(message = "操作类型不能为空")
    private String operationType;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 拒绝原因（审核拒绝时使用）
     */
    private String rejectReason;

    /**
     * 敏感词类型（标记敏感时使用）
     */
    private String sensitiveType;

    /**
     * 是否发送通知给评论者
     */
    private Boolean sendNotification = true;

    /**
     * 是否同时禁言相关用户
     */
    private Boolean banUsers = false;

    /**
     * 禁言时长（分钟，如果选择禁言用户）
     */
    private Integer banDuration;

    /**
     * 批量操作类型枚举
     */
    public enum OperationType {
        APPROVE("APPROVE", "审核通过"),
        REJECT("REJECT", "审核拒绝"),
        DELETE("DELETE", "删除"),
        RESTORE("RESTORE", "恢复"),
        MARK_SENSITIVE("MARK_SENSITIVE", "标记为敏感"),
        UNMARK_SENSITIVE("UNMARK_SENSITIVE", "取消敏感标记");

        private final String code;
        private final String description;

        OperationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static OperationType fromCode(String code) {
            for (OperationType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 验证操作类型是否有效
     */
    public boolean isValidOperationType() {
        return OperationType.fromCode(this.operationType) != null;
    }

    /**
     * 获取操作类型枚举
     */
    public OperationType getOperationTypeEnum() {
        return OperationType.fromCode(this.operationType);
    }
}

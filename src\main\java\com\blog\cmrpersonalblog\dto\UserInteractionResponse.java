package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户互动数据响应DTO
 */
@Data
public class UserInteractionResponse {

    /**
     * 点赞的文章列表
     */
    private List<LikedArticleInfo> likedArticles;

    /**
     * 收藏的文章列表
     */
    private List<CollectedArticleInfo> collectedArticles;

    /**
     * 关注的用户列表
     */
    private List<FollowingUserInfo> followingUsers;

    /**
     * 粉丝列表
     */
    private List<FollowerUserInfo> followers;

    /**
     * 点赞的文章信息
     */
    @Data
    public static class LikedArticleInfo {
        private Long articleId;
        private String title;
        private String summary;
        private String authorName;
        private String authorAvatar;
        private Integer viewCount;
        private Integer likeCount;
        private LocalDateTime likeTime;
        private LocalDateTime publishTime;
    }

    /**
     * 收藏的文章信息
     */
    @Data
    public static class CollectedArticleInfo {
        private Long articleId;
        private String title;
        private String summary;
        private String coverImage;
        private String authorName;
        private String authorAvatar;
        private Integer viewCount;
        private Integer likeCount;
        private Integer collectCount;
        private LocalDateTime collectTime;
        private LocalDateTime publishTime;
    }

    /**
     * 关注的用户信息
     */
    @Data
    public static class FollowingUserInfo {
        private Long userId;
        private String userName;
        private String nickName;
        private String avatar;
        private Integer articleCount;
        private Integer followerCount;
        private LocalDateTime followTime;
    }

    /**
     * 粉丝用户信息
     */
    @Data
    public static class FollowerUserInfo {
        private Long userId;
        private String userName;
        private String nickName;
        private String avatar;
        private Integer articleCount;
        private Integer followerCount;
        private LocalDateTime followTime;
        private Boolean isFollowBack; // 是否回关
    }
}

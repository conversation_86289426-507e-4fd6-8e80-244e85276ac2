<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.CommentMapper">

    <!-- 评论管理响应结果映射 -->
    <resultMap id="CommentManagementResponseMap" type="com.blog.cmrpersonalblog.dto.CommentManagementResponse">
        <id column="id" property="id"/>
        <result column="article_id" property="articleId"/>
        <result column="article_title" property="articleTitle"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_nick_name" property="userNickName"/>
        <result column="user_avatar" property="userAvatar"/>
        <result column="content" property="content"/>
        <result column="original_content" property="originalContent"/>
        <result column="parent_id" property="parentId"/>
        <result column="root_id" property="rootId"/>
        <result column="level" property="level"/>
        <result column="path" property="path"/>
        <result column="like_count" property="likeCount"/>
        <result column="reply_count" property="replyCount"/>
        <result column="status" property="status"/>
        <result column="is_sensitive" property="isSensitive"/>
        <result column="sensitive_type" property="sensitiveType"/>
        <result column="sensitive_words" property="sensitiveWords"/>
        <result column="sensitive_score" property="sensitiveScore"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="location" property="location"/>
        <result column="user_agent" property="userAgent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="audit_user_name" property="auditUserName"/>
        <result column="audit_remark" property="auditRemark"/>
    </resultMap>

    <!-- 分页查询评论管理列表 -->
    <select id="selectCommentManagementPage" resultMap="CommentManagementResponseMap">
        SELECT 
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.original_content,
            c.parent_id,
            c.root_id,
            c.level,
            c.path,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.sensitive_words,
            c.sensitive_score,
            c.ip_address,
            c.location,
            c.user_agent,
            c.create_time,
            c.update_time,
            c.audit_time,
            c.audit_user_id,
            au.user_name as audit_user_name,
            c.audit_remark
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        LEFT JOIN sys_user au ON c.audit_user_id = au.user_id
        <where>
            <if test="query.articleId != null">
                AND c.article_id = #{query.articleId}
            </if>
            <if test="query.userId != null">
                AND c.user_id = #{query.userId}
            </if>
            <if test="query.userName != null and query.userName != ''">
                AND u.user_name LIKE CONCAT('%', #{query.userName}, '%')
            </if>
            <if test="query.content != null and query.content != ''">
                AND c.content LIKE CONCAT('%', #{query.content}, '%')
            </if>
            <if test="query.status != null">
                AND c.status = #{query.status}
            </if>
            <if test="query.isSensitive != null">
                AND c.is_sensitive = #{query.isSensitive}
            </if>
            <if test="query.sensitiveType != null and query.sensitiveType != ''">
                AND c.sensitive_type = #{query.sensitiveType}
            </if>
            <if test="query.parentId != null">
                AND c.parent_id = #{query.parentId}
            </if>
            <if test="query.rootId != null">
                AND c.root_id = #{query.rootId}
            </if>
            <if test="query.ipAddress != null and query.ipAddress != ''">
                AND c.ip_address = #{query.ipAddress}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND c.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND c.create_time &lt;= #{query.endTime}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (c.content LIKE CONCAT('%', #{query.keyword}, '%') 
                     OR u.user_name LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.onlyTopLevel != null and query.onlyTopLevel">
                AND (c.parent_id IS NULL OR c.parent_id = 0)
            </if>
            <if test="query.minLikeCount != null">
                AND c.like_count >= #{query.minLikeCount}
            </if>
            <if test="query.maxLikeCount != null">
                AND c.like_count &lt;= #{query.maxLikeCount}
            </if>
            <if test="query.minReplyCount != null">
                AND c.reply_count >= #{query.minReplyCount}
            </if>
            <if test="query.maxReplyCount != null">
                AND c.reply_count &lt;= #{query.maxReplyCount}
            </if>
        </where>
        <choose>
            <when test="query.sortField != null and query.sortField != ''">
                ORDER BY c.${query.sortField}
                <if test="query.sortOrder != null and query.sortOrder != ''">
                    ${query.sortOrder}
                </if>
                <!-- 添加ID作为二级排序，确保结果稳定 -->
                <if test="query.sortField != 'id'">
                    , c.id ASC
                </if>
            </when>
            <otherwise>
                ORDER BY c.id ASC
            </otherwise>
        </choose>
    </select>

    <!-- 获取评论详情 -->
    <select id="selectCommentDetailById" resultMap="CommentManagementResponseMap">
        SELECT 
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.original_content,
            c.parent_id,
            c.root_id,
            c.level,
            c.path,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.sensitive_words,
            c.sensitive_score,
            c.ip_address,
            c.location,
            c.user_agent,
            c.create_time,
            c.update_time,
            c.audit_time,
            c.audit_user_id,
            au.user_name as audit_user_name,
            c.audit_remark
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        LEFT JOIN sys_user au ON c.audit_user_id = au.user_id
        WHERE c.id = #{commentId}
    </select>

    <!-- 获取评论树形结构 -->
    <select id="selectCommentTree" resultMap="CommentManagementResponseMap">
        SELECT 
            c.id,
            c.article_id,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.parent_id,
            c.root_id,
            c.level,
            c.path,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.create_time
        FROM comment c
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        WHERE c.article_id = #{articleId}
        <if test="rootId != null">
            AND c.root_id = #{rootId}
        </if>
        AND c.status = 1
        ORDER BY c.root_id, c.level, c.create_time, c.id ASC
    </select>

    <!-- 获取子评论列表 -->
    <select id="selectChildComments" resultMap="CommentManagementResponseMap">
        SELECT 
            c.id,
            c.article_id,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.parent_id,
            c.root_id,
            c.level,
            c.like_count,
            c.reply_count,
            c.status,
            c.create_time
        FROM comment c
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        WHERE c.parent_id = #{parentId}
        AND c.status = 1
        ORDER BY c.create_time
    </select>

    <!-- 批量更新评论状态 -->
    <update id="batchUpdateCommentStatus">
        UPDATE comment 
        SET status = #{status}, 
            update_time = NOW()
            <if test="auditUserId != null">
                , audit_user_id = #{auditUserId}, 
                audit_time = NOW()
            </if>
            <if test="auditRemark != null">
                , audit_remark = #{auditRemark}
            </if>
        WHERE id IN
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量标记敏感评论 -->
    <update id="batchUpdateSensitiveStatus">
        UPDATE comment 
        SET is_sensitive = #{isSensitive}, 
            update_time = NOW()
            <if test="sensitiveType != null">
                , sensitive_type = #{sensitiveType}
            </if>
        WHERE id IN
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量删除评论 -->
    <update id="batchDeleteComments">
        UPDATE comment 
        SET status = 3, update_time = NOW()
        WHERE id IN
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新评论审核信息 -->
    <update id="updateCommentAuditInfo">
        UPDATE comment 
        SET status = #{status},
            audit_user_id = #{auditUserId},
            audit_time = NOW(),
            audit_remark = #{auditRemark},
            update_time = NOW()
        WHERE id = #{commentId}
    </update>

    <!-- 获取评论统计信息 -->
    <select id="selectCommentStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_comments,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_audit,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_comments,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected_comments,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as deleted_comments,
            SUM(CASE WHEN is_sensitive = 1 THEN 1 ELSE 0 END) as sensitive_comments,
            SUM(CASE WHEN parent_id IS NOT NULL AND parent_id > 0 THEN 1 ELSE 0 END) as total_replies,
            SUM(like_count) as total_likes,
            SUM(reply_count) as total_reply_count
        FROM comment
    </select>

    <!-- 获取待审核评论数量 -->
    <select id="selectPendingAuditCount" resultType="long">
        SELECT COUNT(*) FROM comment WHERE status = 0
    </select>

    <!-- 获取敏感评论数量 -->
    <select id="selectSensitiveCommentCount" resultType="long">
        SELECT COUNT(*) FROM comment WHERE is_sensitive = 1
    </select>

    <!-- 获取评论状态分布 -->
    <select id="selectCommentStatusDistribution" resultType="map">
        SELECT 
            status,
            COUNT(*) as count,
            CASE 
                WHEN status = 0 THEN '待审核'
                WHEN status = 1 THEN '已通过'
                WHEN status = 2 THEN '已拒绝'
                WHEN status = 3 THEN '已删除'
                ELSE '未知'
            END as status_name
        FROM comment
        GROUP BY status
        ORDER BY status
    </select>

    <!-- 获取敏感词类型分布 -->
    <select id="selectSensitiveTypeDistribution" resultType="map">
        SELECT 
            sensitive_type,
            COUNT(*) as count,
            CASE 
                WHEN sensitive_type = 'profanity' THEN '脏话'
                WHEN sensitive_type = 'politics' THEN '政治敏感'
                WHEN sensitive_type = 'advertisement' THEN '广告'
                WHEN sensitive_type = 'spam' THEN '垃圾信息'
                WHEN sensitive_type = 'violence' THEN '暴力内容'
                WHEN sensitive_type = 'illegal' THEN '违法内容'
                ELSE sensitive_type
            END as type_name
        FROM comment
        WHERE is_sensitive = 1 AND sensitive_type IS NOT NULL
        GROUP BY sensitive_type
        ORDER BY count DESC
    </select>

    <!-- 分页获取最新评论列表 -->
    <select id="selectLatestCommentsPage" resultMap="CommentManagementResponseMap">
        SELECT
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.original_content,
            c.parent_id,
            c.root_id,
            c.level,
            c.path,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.sensitive_words,
            c.sensitive_score,
            c.ip_address,
            c.location,
            c.user_agent,
            c.create_time,
            c.update_time,
            c.audit_time,
            c.audit_user_id,
            au.user_name as audit_user_name,
            c.audit_remark
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        LEFT JOIN sys_user au ON c.audit_user_id = au.user_id
        <where>
            <if test="request.status != null">
                AND c.status = #{request.status}
            </if>
            <if test="request.articleId != null">
                AND c.article_id = #{request.articleId}
            </if>
            <if test="request.userId != null">
                AND c.user_id = #{request.userId}
            </if>
            <if test="request.includeSensitive != null and !request.includeSensitive">
                AND c.is_sensitive = 0
            </if>
            <if test="request.startTime != null and request.startTime != ''">
                AND c.create_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null and request.endTime != ''">
                AND c.create_time &lt;= #{request.endTime}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (c.content LIKE CONCAT('%', #{request.keyword}, '%')
                     OR u.user_name LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>
            <if test="request.onlyTopLevel != null and request.onlyTopLevel">
                AND (c.parent_id IS NULL OR c.parent_id = 0)
            </if>
            <if test="request.minLikeCount != null">
                AND c.like_count >= #{request.minLikeCount}
            </if>
            <if test="request.minReplyCount != null">
                AND c.reply_count >= #{request.minReplyCount}
            </if>
        </where>
        ORDER BY c.create_time DESC, c.id ASC
    </select>

    <!-- 分页获取热门评论列表 -->
    <select id="selectPopularCommentsPage" resultMap="CommentManagementResponseMap">
        SELECT
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.original_content,
            c.parent_id,
            c.root_id,
            c.level,
            c.path,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.sensitive_words,
            c.sensitive_score,
            c.ip_address,
            c.location,
            c.user_agent,
            c.create_time,
            c.update_time,
            c.audit_time,
            c.audit_user_id,
            au.user_name as audit_user_name,
            c.audit_remark,
            (c.like_count * #{request.likeWeight} + c.reply_count * #{request.replyWeight}) as hot_score
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        LEFT JOIN sys_user au ON c.audit_user_id = au.user_id
        <where>
            <if test="request.status != null">
                AND c.status = #{request.status}
            </if>
            <if test="request.articleId != null">
                AND c.article_id = #{request.articleId}
            </if>
            <if test="request.userId != null">
                AND c.user_id = #{request.userId}
            </if>
            <if test="request.includeSensitive != null and !request.includeSensitive">
                AND c.is_sensitive = 0
            </if>
            <if test="request.startTime != null and request.startTime != ''">
                AND c.create_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null and request.endTime != ''">
                AND c.create_time &lt;= #{request.endTime}
            </if>
            <if test="request.timeRange != null and request.timeRange != 'all'">
                <choose>
                    <when test="request.timeRange == 'today'">
                        AND c.create_time >= CURDATE()
                    </when>
                    <when test="request.timeRange == 'week'">
                        AND c.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                    </when>
                    <when test="request.timeRange == 'month'">
                        AND c.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    </when>
                </choose>
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (c.content LIKE CONCAT('%', #{request.keyword}, '%')
                     OR u.user_name LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>
            <if test="request.onlyTopLevel != null and request.onlyTopLevel">
                AND (c.parent_id IS NULL OR c.parent_id = 0)
            </if>
            <if test="request.minLikeCount != null">
                AND c.like_count >= #{request.minLikeCount}
            </if>
            <if test="request.minReplyCount != null">
                AND c.reply_count >= #{request.minReplyCount}
            </if>
            <if test="request.minHotScore != null">
                AND (c.like_count * #{request.likeWeight} + c.reply_count * #{request.replyWeight}) >= #{request.minHotScore}
            </if>
        </where>
        ORDER BY (c.like_count * #{request.likeWeight} + c.reply_count * #{request.replyWeight}) DESC, c.create_time DESC, c.id ASC
    </select>

    <!-- 搜索评论 -->
    <select id="searchComments" resultMap="CommentManagementResponseMap">
        SELECT
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.parent_id,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.create_time
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        WHERE (c.content LIKE CONCAT('%', #{keyword}, '%')
               OR u.user_name LIKE CONCAT('%', #{keyword}, '%')
               OR a.title LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY c.create_time DESC, c.id ASC
    </select>

    <!-- 获取用户评论管理列表 -->
    <select id="selectUserCommentManagementPage" resultMap="CommentManagementResponseMap">
        SELECT
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.parent_id,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.create_time
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        WHERE c.user_id = #{userId}
        ORDER BY c.create_time DESC, c.id ASC
    </select>

    <!-- 获取文章评论管理列表 -->
    <select id="selectArticleCommentManagementPage" resultMap="CommentManagementResponseMap">
        SELECT
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.parent_id,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.create_time
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        WHERE c.article_id = #{articleId}
        ORDER BY c.create_time DESC, c.id ASC
    </select>

    <!-- 更新评论统计数据 -->
    <update id="updateCommentStats">
        UPDATE comment
        SET ${field} = ${field} + #{increment},
            update_time = NOW()
        WHERE id = #{commentId}
    </update>

    <!-- 重新计算评论统计数据 -->
    <update id="recalculateCommentStats">
        UPDATE comment c
        LEFT JOIN (
            SELECT target_id, COUNT(*) as like_count
            FROM user_like
            WHERE target_type = 2 AND status = 1
            GROUP BY target_id
        ) ul ON c.id = ul.target_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as reply_count
            FROM comment
            WHERE status = 1
            GROUP BY parent_id
        ) cc ON c.id = cc.parent_id
        SET
            c.like_count = COALESCE(ul.like_count, 0),
            c.reply_count = COALESCE(cc.reply_count, 0)
        WHERE c.id = #{commentId}
    </update>

    <!-- 获取评论回复路径 -->
    <select id="selectCommentPath" resultType="string">
        SELECT path FROM comment WHERE id = #{commentId}
    </select>

    <!-- 更新评论路径 -->
    <update id="updateCommentPath">
        UPDATE comment
        SET path = #{path}, update_time = NOW()
        WHERE id = #{commentId}
    </update>

    <!-- 获取评论的所有子评论ID -->
    <select id="selectAllChildCommentIds" resultType="long">
        SELECT id FROM comment
        WHERE path LIKE CONCAT((SELECT path FROM comment WHERE id = #{commentId}), '/%')
        OR parent_id = #{commentId}
    </select>

    <!-- 根据IP地址获取评论列表 -->
    <select id="selectCommentsByIpAddress" resultMap="CommentManagementResponseMap">
        SELECT
            c.id,
            c.article_id,
            a.title as article_title,
            c.user_id,
            u.user_name,
            u.nick_name as user_nick_name,
            u.avatar as user_avatar,
            c.content,
            c.parent_id,
            c.like_count,
            c.reply_count,
            c.status,
            c.is_sensitive,
            c.sensitive_type,
            c.ip_address,
            c.location,
            c.create_time
        FROM comment c
        LEFT JOIN article a ON c.article_id = a.id
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        WHERE c.ip_address = #{ipAddress}
        ORDER BY c.create_time DESC, c.id ASC
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.blog</groupId>
	<artifactId>Cmr-Personal-Blog</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>Cmr-Personal-Blog</name>
	<description>Cmr-Personal-Blog</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
	</properties>
	<dependencies>
		<!-- Spring Boot Web Starter -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<!-- Spring Context -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<!-- MySQL Driver -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- MyBatis-Plus for Spring Boot 3 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
			<version>3.5.5</version>
		</dependency>

		<!-- Sa-Token 权限认证 -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-spring-boot3-starter</artifactId>
			<version>1.37.0</version>
		</dependency>

		<!-- Sa-Token 整合 Redis -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-redis-jackson</artifactId>
			<version>1.37.0</version>
		</dependency>

		<!-- Redis -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<!-- Jedis -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>

		<!-- JSON处理 - Jackson (Spring Boot默认) -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<!-- Jackson时间处理模块 -->
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
		</dependency>

		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- Validation -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- Markdown解析器 -->
		<dependency>
			<groupId>com.vladsch.flexmark</groupId>
			<artifactId>flexmark-all</artifactId>
			<version>0.64.8</version>
		</dependency>

		<!-- Test -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- SpringBlade 核心依赖 -->
<!--		<dependency>-->
<!--			<groupId>org.springblade</groupId>-->
<!--			<artifactId>blade-core</artifactId>-->
<!--		</dependency>-->
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
	<repositories>
<!--		<repository>-->
<!--			<id>aliyun-central</id>-->
<!--			<name>aliyun maven</name>-->
<!--			<url>https://maven.aliyun.com/nexus/content/groups/public/</url>-->
<!--			<layout>default</layout>-->
<!--			<releases>-->
<!--				<enabled>true</enabled>-->
<!--			</releases>-->
<!--			<snapshots>-->
<!--				<enabled>true</enabled>-->
<!--			</snapshots>-->
<!--		</repository>-->
		<repository>
			<id>aliyun-central</id>
			<name>aliyun maven</name>
			<url>https://maven.aliyun.com/repository/public</url>
		</repository>
	</repositories>
</project>

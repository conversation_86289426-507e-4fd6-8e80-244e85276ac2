package com.blog.cmrpersonalblog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.dto.*;
import com.blog.cmrpersonalblog.entity.Comment;
import com.blog.cmrpersonalblog.mapper.CommentMapper;
import com.blog.cmrpersonalblog.service.CommentManagementService;
import com.blog.cmrpersonalblog.service.SensitiveWordService;
import com.blog.cmrpersonalblog.service.UserActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 评论管理服务实现类
 */
@Slf4j
@Service
public class CommentManagementServiceImpl implements CommentManagementService {

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Autowired
    private UserActivityService userActivityService;

    @Override
    public IPage<CommentManagementResponse> getCommentList(CommentQueryRequest queryRequest) {
        Page<CommentManagementResponse> page = new Page<>(queryRequest.getCurrent(), queryRequest.getSize());
        return commentMapper.selectCommentManagementPage(page, queryRequest);
    }

    @Override
    public CommentManagementResponse getCommentDetail(Long commentId) {
        if (commentId == null) {
            return null;
        }
        return commentMapper.selectCommentDetailById(commentId);
    }

    @Override
    public List<CommentManagementResponse> getCommentTree(Long articleId, Long rootId) {
        if (articleId == null) {
            return new ArrayList<>();
        }
        
        List<CommentManagementResponse> comments = commentMapper.selectCommentTree(articleId, rootId);
        return buildCommentTree(comments);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditComment(CommentAuditRequest auditRequest, Long auditorId) {
        try {
            if (!auditRequest.isValidAuditResult()) {
                throw new RuntimeException("无效的审核结果");
            }

            // 检查评论是否存在且状态为待审核
            Comment comment = commentMapper.selectById(auditRequest.getCommentId());
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }
            if (comment.getStatus() != 0) {
                throw new RuntimeException("评论状态不是待审核，无法审核");
            }

            // 更新审核信息
            int result = commentMapper.updateCommentAuditInfo(
                auditRequest.getCommentId(),
                auditRequest.getAuditResult(),
                auditorId,
                auditRequest.getAuditRemark()
            );

            if (result > 0) {
                // 记录审核活动
                String activityType = auditRequest.isApprove() ? "COMMENT_APPROVED" : "COMMENT_REJECTED";
                String description = auditRequest.isApprove() ? "审核通过评论" : "审核拒绝评论";
                
                userActivityService.recordActivity(auditorId, activityType, description,
                    auditRequest.getCommentId(), "comment", "SUCCESS", null);

                log.info("评论审核成功: commentId={}, result={}, auditorId={}", 
                    auditRequest.getCommentId(), auditRequest.getAuditResult(), auditorId);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("评论审核失败", e);
            throw new RuntimeException("评论审核失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchOperateComments(CommentBatchOperationRequest batchRequest, Long operatorId) {
        Map<String, Object> result = new HashMap<>();
        List<String> successIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        
        try {
            if (!batchRequest.isValidOperationType()) {
                throw new RuntimeException("无效的操作类型");
            }

            CommentBatchOperationRequest.OperationType operationType = batchRequest.getOperationTypeEnum();
            int affectedRows = 0;

            switch (operationType) {
                case APPROVE:
                    affectedRows = commentMapper.batchUpdateCommentStatus(
                        batchRequest.getCommentIds(), 1, operatorId, batchRequest.getRemark());
                    break;
                case REJECT:
                    affectedRows = commentMapper.batchUpdateCommentStatus(
                        batchRequest.getCommentIds(), 2, operatorId, batchRequest.getRejectReason());
                    break;
                case DELETE:
                    affectedRows = commentMapper.batchDeleteComments(batchRequest.getCommentIds());
                    break;
                case MARK_SENSITIVE:
                    affectedRows = commentMapper.batchUpdateSensitiveStatus(
                        batchRequest.getCommentIds(), 1, batchRequest.getSensitiveType());
                    break;
                case UNMARK_SENSITIVE:
                    affectedRows = commentMapper.batchUpdateSensitiveStatus(
                        batchRequest.getCommentIds(), 0, null);
                    break;
                default:
                    throw new RuntimeException("不支持的操作类型: " + operationType.getDescription());
            }

            // 记录操作结果
            if (affectedRows > 0) {
                for (Long commentId : batchRequest.getCommentIds()) {
                    successIds.add(commentId.toString());
                }
                
                // 记录批量操作活动
                userActivityService.recordActivity(operatorId, "BATCH_COMMENT_OPERATION", 
                    "批量操作评论: " + operationType.getDescription(),
                    null, "comment", "SUCCESS", 
                    Map.of("operation", operationType.getCode(), "count", affectedRows));
                
                log.info("批量操作评论成功: operation={}, count={}, operatorId={}", 
                    operationType.getCode(), affectedRows, operatorId);
            }

            result.put("success", true);
            result.put("message", "批量操作完成");
            result.put("affectedRows", affectedRows);
            result.put("successIds", successIds);
            result.put("failedIds", failedIds);
            
        } catch (Exception e) {
            log.error("批量操作评论失败", e);
            result.put("success", false);
            result.put("message", "批量操作失败: " + e.getMessage());
            result.put("affectedRows", 0);
            result.put("successIds", successIds);

            // 避免使用方法引用，改为传统的循环方式
            List<String> failedIdStrings = new ArrayList<>();
            for (Long commentId : batchRequest.getCommentIds()) {
                failedIdStrings.add(commentId.toString());
            }
            result.put("failedIds", failedIdStrings);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComment(Long commentId, Long operatorId) {
        try {
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }

            // 更新评论状态为已删除
            comment.setStatus(3);
            comment.setUpdateTime(LocalDateTime.now());
            
            int result = commentMapper.updateById(comment);
            if (result > 0) {
                // 记录删除活动
                userActivityService.recordActivity(operatorId, "COMMENT_DELETED", "删除评论",
                    commentId, "comment", "SUCCESS", null);
                
                log.info("评论删除成功: commentId={}, operatorId={}", commentId, operatorId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("评论删除失败", e);
            throw new RuntimeException("评论删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean restoreComment(Long commentId, Long operatorId) {
        try {
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }
            if (comment.getStatus() != 3) {
                throw new RuntimeException("只能恢复已删除的评论");
            }

            // 恢复评论状态为待审核
            comment.setStatus(0);
            comment.setUpdateTime(LocalDateTime.now());
            
            int result = commentMapper.updateById(comment);
            if (result > 0) {
                // 记录恢复活动
                userActivityService.recordActivity(operatorId, "COMMENT_RESTORED", "恢复评论",
                    commentId, "comment", "SUCCESS", null);
                
                log.info("评论恢复成功: commentId={}, operatorId={}", commentId, operatorId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("评论恢复失败", e);
            throw new RuntimeException("评论恢复失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markSensitive(Long commentId, String sensitiveType, Long operatorId) {
        try {
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }

            // 标记为敏感评论
            comment.setIsSensitive(1);
            comment.setSensitiveType(sensitiveType);
            comment.setUpdateTime(LocalDateTime.now());
            
            int result = commentMapper.updateById(comment);
            if (result > 0) {
                // 记录标记活动
                userActivityService.recordActivity(operatorId, "COMMENT_MARKED_SENSITIVE", "标记敏感评论",
                    commentId, "comment", "SUCCESS", Map.of("sensitiveType", sensitiveType));
                
                log.info("评论标记敏感成功: commentId={}, type={}, operatorId={}", commentId, sensitiveType, operatorId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("评论标记敏感失败", e);
            throw new RuntimeException("评论标记敏感失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unmarkSensitive(Long commentId, Long operatorId) {
        try {
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                throw new RuntimeException("评论不存在");
            }

            // 取消敏感标记
            comment.setIsSensitive(0);
            comment.setSensitiveType(null);
            comment.setSensitiveWords(null);
            comment.setSensitiveScore(0);
            comment.setUpdateTime(LocalDateTime.now());
            
            int result = commentMapper.updateById(comment);
            if (result > 0) {
                // 记录取消标记活动
                userActivityService.recordActivity(operatorId, "COMMENT_UNMARKED_SENSITIVE", "取消敏感标记",
                    commentId, "comment", "SUCCESS", null);
                
                log.info("评论取消敏感标记成功: commentId={}, operatorId={}", commentId, operatorId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("评论取消敏感标记失败", e);
            throw new RuntimeException("评论取消敏感标记失败: " + e.getMessage());
        }
    }

    @Override
    public SensitiveWordCheckResult checkCommentSensitiveWords(String content) {
        return sensitiveWordService.checkSensitiveWords(content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoAuditComment(Comment comment) {
        try {
            // 检测敏感词
            SensitiveWordCheckResult checkResult = sensitiveWordService.checkSensitiveWords(comment.getContent());
            
            // 更新评论的敏感词信息
            comment.setOriginalContent(comment.getContent());
            comment.setIsSensitive(checkResult.isHasSensitiveWords() ? 1 : 0);
            comment.setSensitiveScore(checkResult.getSensitiveScore());
            
            if (checkResult.isHasSensitiveWords()) {
                comment.setSensitiveType(checkResult.getPrimaryType());
                // 将敏感词列表转换为JSON字符串
                if (checkResult.getSensitiveWords() != null) {
                    StringBuilder wordsJson = new StringBuilder("[");
                    for (int i = 0; i < checkResult.getSensitiveWords().size(); i++) {
                        if (i > 0) wordsJson.append(",");
                        SensitiveWordCheckResult.SensitiveWord word = checkResult.getSensitiveWords().get(i);
                        wordsJson.append("{\"word\":\"").append(word.getWord())
                                .append("\",\"type\":\"").append(word.getType())
                                .append("\",\"severity\":").append(word.getSeverity()).append("}");
                    }
                    wordsJson.append("]");
                    comment.setSensitiveWords(wordsJson.toString());
                }
                
                // 过滤敏感词
                comment.setContent(checkResult.getFilteredContent());
            }
            
            // 根据检测结果决定审核状态
            if (checkResult.shouldBlock()) {
                comment.setStatus(2); // 直接拒绝
            } else if (checkResult.needsManualReview()) {
                comment.setStatus(0); // 待审核
            } else {
                comment.setStatus(1); // 自动通过
            }
            
            return commentMapper.updateById(comment) > 0;
        } catch (Exception e) {
            log.error("自动审核评论失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getCommentStatistics() {
        return commentMapper.selectCommentStatistics();
    }

    @Override
    public Long getPendingAuditCount() {
        return commentMapper.selectPendingAuditCount();
    }

    @Override
    public Long getSensitiveCommentCount() {
        return commentMapper.selectSensitiveCommentCount();
    }

    @Override
    public Map<String, Long> getCommentStatusDistribution() {
        List<Map<String, Object>> distribution = commentMapper.selectCommentStatusDistribution();
        Map<String, Long> result = new HashMap<>();
        
        for (Map<String, Object> item : distribution) {
            String statusName = (String) item.get("status_name");
            Long count = ((Number) item.get("count")).longValue();
            result.put(statusName, count);
        }
        
        return result;
    }

    @Override
    public Map<String, Long> getSensitiveTypeDistribution() {
        List<Map<String, Object>> distribution = commentMapper.selectSensitiveTypeDistribution();
        Map<String, Long> result = new HashMap<>();
        
        for (Map<String, Object> item : distribution) {
            String typeName = (String) item.get("type_name");
            Long count = ((Number) item.get("count")).longValue();
            result.put(typeName, count);
        }
        
        return result;
    }

    @Override
    public IPage<CommentManagementResponse> getLatestComments(LatestCommentsRequest request) {
        Page<CommentManagementResponse> page = new Page<>(request.getCurrent(), request.getSize());
        return commentMapper.selectLatestCommentsPage(page, request);
    }

    @Override
    public IPage<CommentManagementResponse> getPopularComments(PopularCommentsRequest request) {
        Page<CommentManagementResponse> page = new Page<>(request.getCurrent(), request.getSize());
        return commentMapper.selectPopularCommentsPage(page, request);
    }

    @Override
    public IPage<CommentManagementResponse> searchComments(String keyword, Long current, Long size) {
        Page<CommentManagementResponse> page = new Page<>(current != null ? current : 1L, size != null ? size : 10L);
        return commentMapper.searchComments(page, keyword);
    }

    @Override
    public IPage<CommentManagementResponse> getUserComments(Long userId, Long current, Long size) {
        Page<CommentManagementResponse> page = new Page<>(current != null ? current : 1L, size != null ? size : 10L);
        return commentMapper.selectUserCommentManagementPage(page, userId);
    }

    @Override
    public IPage<CommentManagementResponse> getArticleComments(Long articleId, Long current, Long size) {
        Page<CommentManagementResponse> page = new Page<>(current != null ? current : 1L, size != null ? size : 10L);
        return commentMapper.selectArticleCommentManagementPage(page, articleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCommentStats(Long commentId, String field, Integer increment) {
        try {
            commentMapper.updateCommentStats(commentId, field, increment);
            return true;
        } catch (Exception e) {
            log.error("更新评论统计数据失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateCommentStats(Long commentId) {
        try {
            int result = commentMapper.recalculateCommentStats(commentId);
            return result > 0;
        } catch (Exception e) {
            log.error("重新计算评论统计数据失败", e);
            return false;
        }
    }

    @Override
    public String buildCommentPath(Comment comment) {
        if (comment.isTopLevel()) {
            return comment.getId().toString();
        }
        
        String parentPath = commentMapper.selectCommentPath(comment.getParentId());
        return parentPath != null ? parentPath + "/" + comment.getId() : comment.getId().toString();
    }

    @Override
    public List<Long> getAllChildCommentIds(Long commentId) {
        return commentMapper.selectAllChildCommentIds(commentId);
    }

    @Override
    public IPage<CommentManagementResponse> getCommentsByIpAddress(String ipAddress, Long current, Long size) {
        Page<CommentManagementResponse> page = new Page<>(current != null ? current : 1L, size != null ? size : 10L);
        return commentMapper.selectCommentsByIpAddress(page, ipAddress);
    }

    /**
     * 构建评论树形结构
     */
    private List<CommentManagementResponse> buildCommentTree(List<CommentManagementResponse> comments) {
        if (comments == null || comments.isEmpty()) {
            return new ArrayList<>();
        }

        // 按ID分组，便于查找 - 避免使用方法引用
        Map<Long, CommentManagementResponse> commentMap = new HashMap<>();
        for (CommentManagementResponse comment : comments) {
            commentMap.put(comment.getId(), comment);
        }

        List<CommentManagementResponse> rootComments = new ArrayList<>();

        for (CommentManagementResponse comment : comments) {
            if (comment.isTopLevel()) {
                rootComments.add(comment);
            } else {
                CommentManagementResponse parent = commentMap.get(comment.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(comment);
                    comment.setParent(parent);
                }
            }
        }

        return rootComments;
    }
}

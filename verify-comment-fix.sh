#!/bin/bash

# 验证评论管理修复的脚本

echo "=== 评论管理系统修复验证脚本 ==="

# 检查Java版本
echo "1. 检查Java版本..."
java -version
echo ""

# 检查Maven版本
echo "2. 检查Maven版本..."
mvn -version
echo ""

# 清理编译缓存
echo "3. 清理编译缓存..."
mvn clean
echo ""

# 重新编译项目
echo "4. 重新编译项目..."
mvn compile
if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败"
    exit 1
fi
echo ""

# 检查编译后的类文件
echo "5. 检查编译后的类文件..."
if [ -f "target/classes/com/blog/cmrpersonalblog/service/impl/CommentManagementServiceImpl.class" ]; then
    echo "✅ CommentManagementServiceImpl.class 存在"
else
    echo "❌ CommentManagementServiceImpl.class 不存在"
fi

# 检查是否有内部类文件
echo "6. 检查内部类文件..."
ls -la target/classes/com/blog/cmrpersonalblog/service/impl/CommentManagementServiceImpl*.class 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 找到相关类文件"
else
    echo "⚠️  未找到内部类文件（这是正常的，因为我们移除了Lambda表达式）"
fi
echo ""

# 打包项目
echo "7. 打包项目..."
mvn package -DskipTests
if [ $? -eq 0 ]; then
    echo "✅ 打包成功"
else
    echo "❌ 打包失败"
    exit 1
fi
echo ""

echo "=== 修复验证完成 ==="
echo ""
echo "📋 下一步操作："
echo "1. 重启Spring Boot应用: mvn spring-boot:run"
echo "2. 执行测试数据: mysql -u root -p < test-data-comments-safe.sql"
echo "3. 测试评论删除功能"
echo ""
echo "🔍 如果仍有问题，请检查："
echo "- 应用启动日志"
echo "- JVM参数设置"
echo "- 依赖版本冲突"

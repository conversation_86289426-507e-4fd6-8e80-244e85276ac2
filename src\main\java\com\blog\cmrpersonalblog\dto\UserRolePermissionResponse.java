package com.blog.cmrpersonalblog.dto;

import com.blog.cmrpersonalblog.entity.SysRole;
import com.blog.cmrpersonalblog.entity.SysPermission;
import lombok.Data;

import java.util.List;

/**
 * 用户角色权限响应DTO
 */
@Data
public class UserRolePermissionResponse {

    /**
     * 用户角色列表
     */
    private List<SysRole> roles;

    /**
     * 用户权限列表
     */
    private List<SysPermission> permissions;

    public UserRolePermissionResponse(List<SysRole> roles, List<SysPermission> permissions) {
        this.roles = roles;
        this.permissions = permissions;
    }
}

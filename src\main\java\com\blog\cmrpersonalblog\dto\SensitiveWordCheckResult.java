package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 敏感词检测结果DTO
 */
@Data
public class SensitiveWordCheckResult {

    /**
     * 是否包含敏感词
     */
    private boolean hasSensitiveWords;

    /**
     * 敏感度评分（0-100）
     */
    private int sensitiveScore;

    /**
     * 检测到的敏感词列表
     */
    private List<SensitiveWord> sensitiveWords;

    /**
     * 过滤后的内容
     */
    private String filteredContent;

    /**
     * 原始内容
     */
    private String originalContent;

    /**
     * 主要敏感词类型
     */
    private String primaryType;

    /**
     * 建议操作
     */
    private String suggestedAction;

    /**
     * 检测详情
     */
    private Map<String, Object> details;

    /**
     * 敏感词信息
     */
    @Data
    public static class SensitiveWord {
        /**
         * 敏感词内容
         */
        private String word;

        /**
         * 敏感词类型
         */
        private String type;

        /**
         * 严重程度（1-5）
         */
        private int severity;

        /**
         * 在文本中的位置
         */
        private int position;

        /**
         * 替换建议
         */
        private String replacement;

        /**
         * 类型描述
         */
        private String typeDescription;

        public SensitiveWord() {}

        public SensitiveWord(String word, String type, int severity, int position) {
            this.word = word;
            this.type = type;
            this.severity = severity;
            this.position = position;
            this.replacement = "*".repeat(word.length());
            this.typeDescription = getTypeDescription(type);
        }

        private String getTypeDescription(String type) {
            switch (type) {
                case "profanity":
                    return "脏话";
                case "politics":
                    return "政治敏感";
                case "advertisement":
                    return "广告";
                case "spam":
                    return "垃圾信息";
                case "violence":
                    return "暴力内容";
                case "illegal":
                    return "违法内容";
                case "pornography":
                    return "色情内容";
                case "gambling":
                    return "赌博内容";
                default:
                    return "敏感内容";
            }
        }
    }

    /**
     * 构造函数
     */
    public SensitiveWordCheckResult() {
        this.hasSensitiveWords = false;
        this.sensitiveScore = 0;
        this.suggestedAction = "PASS";
    }

    public SensitiveWordCheckResult(String originalContent) {
        this();
        this.originalContent = originalContent;
        this.filteredContent = originalContent;
    }

    /**
     * 添加敏感词
     */
    public void addSensitiveWord(String word, String type, int severity, int position) {
        if (sensitiveWords == null) {
            sensitiveWords = new java.util.ArrayList<>();
        }
        sensitiveWords.add(new SensitiveWord(word, type, severity, position));
        this.hasSensitiveWords = true;
        
        // 更新敏感度评分
        updateSensitiveScore(severity);
        
        // 更新主要类型
        updatePrimaryType(type, severity);
        
        // 更新建议操作
        updateSuggestedAction();
    }

    /**
     * 更新敏感度评分
     */
    private void updateSensitiveScore(int severity) {
        // 根据严重程度增加评分
        int scoreIncrease = severity * 10;
        this.sensitiveScore = Math.min(100, this.sensitiveScore + scoreIncrease);
    }

    /**
     * 更新主要敏感词类型
     */
    private void updatePrimaryType(String type, int severity) {
        if (this.primaryType == null || severity >= 4) {
            this.primaryType = type;
        }
    }

    /**
     * 更新建议操作
     */
    private void updateSuggestedAction() {
        if (sensitiveScore >= 80) {
            this.suggestedAction = "BLOCK";
        } else if (sensitiveScore >= 50) {
            this.suggestedAction = "REVIEW";
        } else if (sensitiveScore >= 20) {
            this.suggestedAction = "FILTER";
        } else {
            this.suggestedAction = "PASS";
        }
    }

    /**
     * 获取建议操作描述
     */
    public String getSuggestedActionText() {
        switch (suggestedAction) {
            case "BLOCK":
                return "直接拒绝";
            case "REVIEW":
                return "人工审核";
            case "FILTER":
                return "过滤发布";
            case "PASS":
                return "直接通过";
            default:
                return "未知";
        }
    }

    /**
     * 是否需要人工审核
     */
    public boolean needsManualReview() {
        return "REVIEW".equals(suggestedAction) || "BLOCK".equals(suggestedAction);
    }

    /**
     * 是否应该直接拒绝
     */
    public boolean shouldBlock() {
        return "BLOCK".equals(suggestedAction);
    }

    /**
     * 获取敏感词数量
     */
    public int getSensitiveWordCount() {
        return sensitiveWords != null ? sensitiveWords.size() : 0;
    }
}

-- 数据库迁移脚本：从MD5升级到SHA-256 + 用户名盐值（无需添加字段）

-- 说明：
-- 新的加密方式使用 SHA-256 + 用户名作为盐值
-- 不需要在数据库中存储盐值，盐值基于用户名动态生成
-- 格式：SHA-256(固定前缀 + 用户名 + 原始密码)

-- 1. 为现有用户生成新的SHA-256密码
-- 注意：这里需要手动执行，因为需要知道原始密码

-- 使用测试接口生成密码：
-- GET /test/generate-password/{userName}/{password}

-- 示例：为admin用户生成密码（原密码：123456）
-- 访问：GET /test/generate-password/admin/123456
-- 获取加密后的密码，然后执行更新语句

-- 2. 更新用户密码（使用基于用户名的SHA-256加密）

-- admin用户 (密码: 123456)
-- 使用接口生成的密码替换下面的示例值
UPDATE `sys_user` SET
    `password` = 'SHA-256加密后的密码'
WHERE `user_name` = 'admin';

-- editor用户 (密码: 123456)
UPDATE `sys_user` SET
    `password` = 'SHA-256加密后的密码'
WHERE `user_name` = 'editor';

-- user用户 (密码: 123456)
UPDATE `sys_user` SET
    `password` = 'SHA-256加密后的密码'
WHERE `user_name` = 'user';

-- 3. 验证更新结果
SELECT user_name, password, LENGTH(password) as password_length FROM `sys_user`;

-- 4. 密码格式说明：
-- MD5密码：32位十六进制字符 (例如: e10adc3949ba59abbe56e057f20f883e)
-- SHA-256密码：44位Base64字符 (例如: jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=)

-- 5. 系统会自动识别密码格式：
-- - 32位十六进制：使用MD5验证
-- - 44位Base64：使用SHA-256 + 用户名盐值验证

-- 注意事项：
-- 1. 执行此脚本前请备份数据库
-- 2. 使用测试接口生成正确的SHA-256密码
-- 3. 系统支持新旧密码格式的自动识别和验证
-- 4. 建议用户在迁移后重新设置密码

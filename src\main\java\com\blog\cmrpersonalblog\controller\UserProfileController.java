package com.blog.cmrpersonalblog.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.cmrpersonalblog.common.Result;
import com.blog.cmrpersonalblog.dto.*;
import com.blog.cmrpersonalblog.service.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户详情控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    /**
     * 获取用户详情信息
     */
    @GetMapping("/{userId}/profile")
    public Result<UserProfileResponse> getUserProfile(@PathVariable Long userId) {
        try {
            Long currentUserId = null;
            if (StpUtil.isLogin()) {
                currentUserId = StpUtil.getLoginIdAsLong();
            }
            
            UserProfileResponse profile = userProfileService.getUserProfile(userId, currentUserId);
            if (profile == null) {
                return Result.error("用户不存在");
            }
            
            return Result.success(profile);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询用户发布的文章
     */
    @GetMapping("/{userId}/articles")
    public Result<IPage<UserProfileResponse.ArticleInfo>> getUserArticles(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String tagName,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "publish_time") String orderBy,
            @RequestParam(defaultValue = "desc") String orderDirection) {
        try {
            UserArticleQueryRequest query = new UserArticleQueryRequest();
            query.setUserId(userId);
            query.setPage(page);
            query.setSize(size);
            query.setStatus(status);
            query.setCategoryId(categoryId);
            query.setTagName(tagName);
            query.setKeyword(keyword);
            query.setOrderBy(orderBy);
            query.setOrderDirection(orderDirection);
            
            IPage<UserProfileResponse.ArticleInfo> articles = userProfileService.getUserArticles(query);
            return Result.success(articles);
        } catch (Exception e) {
            log.error("查询用户文章失败", e);
            return Result.error("查询用户文章失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户互动数据
     */
    @GetMapping("/{userId}/interactions")
    public Result<UserInteractionResponse> getUserInteractions(@PathVariable Long userId) {
        try {
            Long currentUserId = null;
            if (StpUtil.isLogin()) {
                currentUserId = StpUtil.getLoginIdAsLong();
            }
            
            UserInteractionResponse interactions = userProfileService.getUserInteractions(userId, currentUserId);
            return Result.success(interactions);
        } catch (Exception e) {
            log.error("获取用户互动数据失败", e);
            return Result.error("获取用户互动数据失败：" + e.getMessage());
        }
    }

    /**
     * 关注/取消关注用户
     */
    @PostMapping("/{userId}/follow")
    @SaCheckLogin
    public Result<String> followUser(@PathVariable Long userId, @RequestParam boolean isFollow) {
        try {
            Long currentUserId = StpUtil.getLoginIdAsLong();
            
            if (currentUserId.equals(userId)) {
                return Result.error("不能关注自己");
            }
            
            boolean success = userProfileService.followUser(currentUserId, userId, isFollow);
            if (success) {
                return Result.success(isFollow ? "关注成功" : "取消关注成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("关注/取消关注用户失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户的详情信息
     */
    @GetMapping("/profile")
    @SaCheckLogin
    public Result<UserProfileResponse> getCurrentUserProfile() {
        try {
            Long currentUserId = StpUtil.getLoginIdAsLong();
            UserProfileResponse profile = userProfileService.getUserProfile(currentUserId, currentUserId);
            return Result.success(profile);
        } catch (Exception e) {
            log.error("获取当前用户详情失败", e);
            return Result.error("获取用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算用户统计数据（管理员功能）
     */
    @PostMapping("/{userId}/recalculate-stats")
    @SaCheckLogin
    public Result<String> recalculateUserStats(@PathVariable Long userId) {
        try {
            // TODO: 添加管理员权限检查
            userProfileService.recalculateUserStats(userId);
            return Result.success("重新计算统计数据成功");
        } catch (Exception e) {
            log.error("重新计算用户统计数据失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }
}

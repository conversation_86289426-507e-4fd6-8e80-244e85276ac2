package com.blog.cmrpersonalblog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.dto.*;
import com.blog.cmrpersonalblog.entity.SysUser;
import com.blog.cmrpersonalblog.entity.UserActivity;
import com.blog.cmrpersonalblog.entity.UserFollow;
import com.blog.cmrpersonalblog.entity.UserStats;
import com.blog.cmrpersonalblog.mapper.ArticleMapper;
import com.blog.cmrpersonalblog.mapper.UserActivityMapper;
import com.blog.cmrpersonalblog.mapper.UserFollowMapper;
import com.blog.cmrpersonalblog.mapper.UserStatsMapper;
import com.blog.cmrpersonalblog.service.SysUserService;
import com.blog.cmrpersonalblog.service.UserActivityService;
import com.blog.cmrpersonalblog.service.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户详情服务实现类
 */
@Slf4j
@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private UserStatsMapper userStatsMapper;

    @Autowired
    private UserFollowMapper userFollowMapper;

    @Autowired
    private UserActivityMapper userActivityMapper;

    @Autowired
    private UserActivityService userActivityService;

    @Override
    public UserProfileResponse getUserProfile(Long userId, Long currentUserId) {
        try {
            // 获取用户基本信息
            UserResponse userInfo = sysUserService.getUserDetail(userId);
            if (userInfo == null) {
                return null;
            }

            UserProfileResponse profile = new UserProfileResponse();
            BeanUtils.copyProperties(userInfo, profile);

            // 获取用户统计数据
            UserStats userStats = userStatsMapper.selectOne(
                new LambdaQueryWrapper<UserStats>().eq(UserStats::getUserId, userId)
            );
            
            UserProfileResponse.UserStatsInfo statsInfo = new UserProfileResponse.UserStatsInfo();
            if (userStats != null) {
                BeanUtils.copyProperties(userStats, statsInfo);
            } else {
                // 如果没有统计数据，初始化为0
                statsInfo.setArticleCount(0);
                statsInfo.setTotalViewCount(0L);
                statsInfo.setTotalLikeCount(0);
                statsInfo.setTotalCommentCount(0);
                statsInfo.setTotalCollectCount(0);
                statsInfo.setFollowerCount(0);
                statsInfo.setFollowingCount(0);
            }
            profile.setStats(statsInfo);

            // 获取最近发布的文章
            List<UserProfileResponse.ArticleInfo> recentArticles = 
                articleMapper.selectRecentArticlesByUserId(userId, 5);
            profile.setRecentArticles(recentArticles);

            // 检查是否被当前用户关注
            if (currentUserId != null && !currentUserId.equals(userId)) {
                Boolean isFollowed = userFollowMapper.checkIsFollowing(currentUserId, userId);
                profile.setIsFollowed(isFollowed != null && isFollowed);
            } else {
                profile.setIsFollowed(false);
            }

            return profile;
        } catch (Exception e) {
            log.error("获取用户详情失败: userId={}", userId, e);
            throw new RuntimeException("获取用户详情失败", e);
        }
    }

    @Override
    public IPage<UserProfileResponse.ArticleInfo> getUserArticles(UserArticleQueryRequest query) {
        try {
            Page<UserProfileResponse.ArticleInfo> page = new Page<>(query.getPage(), query.getSize());
            return articleMapper.selectUserArticlePage(page, query);
        } catch (Exception e) {
            log.error("查询用户文章失败: query={}", query, e);
            throw new RuntimeException("查询用户文章失败", e);
        }
    }

    @Override
    public UserInteractionResponse getUserInteractions(Long userId, Long currentUserId) {
        try {
            UserInteractionResponse interactions = new UserInteractionResponse();

            // 获取关注的用户列表
            List<UserInteractionResponse.FollowingUserInfo> followingUsers = 
                userFollowMapper.selectFollowingUsers(userId, 20);
            interactions.setFollowingUsers(followingUsers);

            // 获取粉丝列表
            List<UserInteractionResponse.FollowerUserInfo> followers = 
                userFollowMapper.selectFollowers(userId, currentUserId, 20);
            interactions.setFollowers(followers);

            // TODO: 获取点赞和收藏的文章列表（需要创建相应的Mapper方法）
            // 暂时设置为空列表
            interactions.setLikedArticles(List.of());
            interactions.setCollectedArticles(List.of());

            return interactions;
        } catch (Exception e) {
            log.error("获取用户互动数据失败: userId={}", userId, e);
            throw new RuntimeException("获取用户互动数据失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followUser(Long followerId, Long followingId, boolean isFollow) {
        try {
            if (followerId.equals(followingId)) {
                throw new RuntimeException("不能关注自己");
            }

            // 查询是否已存在关注记录
            UserFollow existingFollow = userFollowMapper.selectOne(
                new LambdaQueryWrapper<UserFollow>()
                    .eq(UserFollow::getFollowerId, followerId)
                    .eq(UserFollow::getFollowingId, followingId)
            );

            if (existingFollow == null) {
                if (isFollow) {
                    // 创建新的关注记录
                    UserFollow userFollow = new UserFollow();
                    userFollow.setFollowerId(followerId);
                    userFollow.setFollowingId(followingId);
                    userFollow.setStatus(1);
                    userFollow.setCreateTime(LocalDateTime.now());
                    userFollow.setUpdateTime(LocalDateTime.now());
                    userFollowMapper.insert(userFollow);
                }
            } else {
                // 更新关注状态
                existingFollow.setStatus(isFollow ? 1 : 0);
                existingFollow.setUpdateTime(LocalDateTime.now());
                userFollowMapper.updateById(existingFollow);
            }

            // 更新统计数据
            updateUserStats(followingId, "follower_count", isFollow ? 1 : -1);
            updateUserStats(followerId, "following_count", isFollow ? 1 : -1);

            return true;
        } catch (Exception e) {
            log.error("关注/取消关注用户失败: followerId={}, followingId={}, isFollow={}", 
                     followerId, followingId, isFollow, e);
            throw new RuntimeException("关注操作失败", e);
        }
    }

    @Override
    public void initUserStats(Long userId) {
        try {
            userStatsMapper.initUserStats(userId);
        } catch (Exception e) {
            log.error("初始化用户统计数据失败: userId={}", userId, e);
            throw new RuntimeException("初始化用户统计数据失败", e);
        }
    }

    @Override
    public void updateUserStats(Long userId, String field, Integer increment) {
        try {
            userStatsMapper.updateUserStats(userId, field, increment);
        } catch (Exception e) {
            log.error("更新用户统计数据失败: userId={}, field={}, increment={}", 
                     userId, field, increment, e);
            throw new RuntimeException("更新用户统计数据失败", e);
        }
    }

    @Override
    public void recalculateUserStats(Long userId) {
        try {
            userStatsMapper.recalculateUserStats(userId);
        } catch (Exception e) {
            log.error("重新计算用户统计数据失败: userId={}", userId, e);
            throw new RuntimeException("重新计算用户统计数据失败", e);
        }
    }

    // ==================== 后台管理专用方法实现 ====================

    @Override
    public AdminUserProfileResponse getAdminUserProfile(Long userId) {
        try {
            AdminUserProfileResponse response = new AdminUserProfileResponse();

            // 获取用户基本信息
            UserResponse userInfo = sysUserService.getUserDetail(userId);
            if (userInfo == null) {
                return null;
            }

            AdminUserProfileResponse.UserBasicInfo basicInfo = new AdminUserProfileResponse.UserBasicInfo();
            BeanUtils.copyProperties(userInfo, basicInfo);

            // 手动处理角色名称转换（从String转为List<String>）
            if (userInfo.getRoles() != null && !userInfo.getRoles().isEmpty()) {
                List<String> roleNameList = userInfo.getRoles().stream()
                    .map(role -> role.getRoleName())
                    .collect(java.util.stream.Collectors.toList());
                basicInfo.setRoleNames(roleNameList);
            } else {
                basicInfo.setRoleNames(java.util.Collections.emptyList());
            }

            response.setBasicInfo(basicInfo);

            // 获取用户统计数据
            UserStats userStats = userStatsMapper.selectOne(
                new LambdaQueryWrapper<UserStats>().eq(UserStats::getUserId, userId)
            );

            AdminUserProfileResponse.UserStatsInfo statsInfo = new AdminUserProfileResponse.UserStatsInfo();
            if (userStats != null) {
                BeanUtils.copyProperties(userStats, statsInfo);
                // TODO: 添加更多统计数据（草稿数、已删除数等）
            }
            response.setStats(statsInfo);

            // 获取内容发布信息
            AdminUserProfileResponse.ContentInfo contentInfo = getUserContentInfo(userId);
            response.setContentInfo(contentInfo);

            // 获取互动行为数据
            AdminUserProfileResponse.InteractionInfo interactionInfo =
                userActivityService.getUserInteractionInfo(userId, 30);
            response.setInteractionInfo(interactionInfo);

            // 获取安全信息
            AdminUserProfileResponse.SecurityInfo securityInfo =
                userActivityService.getUserSecurityInfo(userId);
            response.setSecurityInfo(securityInfo);

            // 获取最近活动记录
            List<AdminUserProfileResponse.ActivityRecord> recentActivities =
                userActivityService.getRecentActivities(userId, 20);
            response.setRecentActivities(recentActivities);

            return response;
        } catch (Exception e) {
            log.error("获取后台用户详情失败: userId={}", userId, e);
            throw new RuntimeException("获取后台用户详情失败", e);
        }
    }

    @Override
    public AdminUserProfileResponse.ContentInfo getUserContentInfo(Long userId) {
        try {
            AdminUserProfileResponse.ContentInfo contentInfo = new AdminUserProfileResponse.ContentInfo();

            // TODO: 实现内容发布统计逻辑
            // 这里需要查询文章表获取详细的发布统计信息
            contentInfo.setFirstPublishTime(null);
            contentInfo.setLastPublishTime(null);
            contentInfo.setAvgViewsPerArticle(0.0);
            contentInfo.setAvgLikesPerArticle(0.0);
            contentInfo.setCategoryStats(List.of());
            contentInfo.setTopTags(List.of());
            contentInfo.setPublishFrequency(0);

            return contentInfo;
        } catch (Exception e) {
            log.error("获取用户内容信息失败: userId={}", userId, e);
            throw new RuntimeException("获取用户内容信息失败", e);
        }
    }

    @Override
    public void recordUserActivity(Long userId, String activityType, String description,
                                  Long targetId, String targetType, String ipAddress,
                                  String userAgent, String result) {
        // 兼容旧的字符串类型，转换为枚举类型
        try {
            com.blog.cmrpersonalblog.enums.ActivityType type =
                com.blog.cmrpersonalblog.enums.ActivityType.fromCode(activityType);
            if (type != null) {
                userActivityService.recordActivity(userId, type, description,
                                                  targetId, targetType, ipAddress, userAgent, result, null);
            } else {
                log.warn("未知的活动类型: {}", activityType);
            }
        } catch (Exception e) {
            log.warn("记录用户活动失败: userId={}, activityType={}, error={}",
                    userId, activityType, e.getMessage());
        }
    }

    @Override
    public AdminUserProfileResponse.InteractionInfo getUserActivityStats(Long userId, Integer days) {
        try {
            return userActivityMapper.selectUserInteractionInfo(userId);
        } catch (Exception e) {
            log.warn("获取用户活动统计失败，可能是user_activity表不存在: userId={}, days={}, error={}",
                    userId, days, e.getMessage());
            // 返回默认值而不是抛出异常
            AdminUserProfileResponse.InteractionInfo defaultInfo = new AdminUserProfileResponse.InteractionInfo();
            defaultInfo.setLastLoginTime(null);
            defaultInfo.setLastActiveTime(null);
            defaultInfo.setLoginDays(0);
            defaultInfo.setAvgDailyActiveTime(0.0);
            defaultInfo.setTotalSessions(0);
            defaultInfo.setActiveTimeRanges(List.of());
            return defaultInfo;
        }
    }

    @Override
    public Integer cleanExpiredActivities(Integer days) {
        try {
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
            return userActivityMapper.deleteExpiredActivities(beforeTime);
        } catch (Exception e) {
            log.warn("清理过期活动记录失败: days={}, error={}",
                    days, e.getMessage());
            // 返回0而不是抛出异常
            return 0;
        }
    }
}

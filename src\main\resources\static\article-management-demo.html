<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章管理系统演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            display: flex;
            min-height: 600px;
        }
        .editor-panel {
            flex: 1;
            padding: 20px;
            border-right: 1px solid #eee;
        }
        .preview-panel {
            flex: 1;
            padding: 20px;
            background-color: #fafafa;
        }
        textarea {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
            resize: vertical;
        }
        .preview-content {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            background: white;
            min-height: 400px;
            overflow-y: auto;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .stats {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #27ae60;
            background: #f2fdf5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        h1, h2, h3 { color: #333; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 2px; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 16px; color: #666; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 文章管理系统 - Markdown预览演示</h1>
            <p>实时预览Markdown内容，支持代码高亮、表格、任务列表等扩展功能</p>
        </div>
        
        <div class="content">
            <div class="editor-panel">
                <h3>📝 Markdown编辑器</h3>
                <div class="controls">
                    <button onclick="previewMarkdown()">🔄 实时预览</button>
                    <button onclick="loadSample()">📄 加载示例</button>
                    <button onclick="clearEditor()">🗑️ 清空</button>
                </div>
                <textarea id="markdownEditor" placeholder="在这里输入Markdown内容...">
# 欢迎使用文章管理系统

## 功能特性

### ✨ 核心功能
- **文章列表管理**: 支持多维度筛选和排序
- **内容审核流程**: 待审核 → 已发布 → 已下架
- **批量操作**: 审核通过/拒绝、置顶、删除等
- **Markdown预览**: 实时预览和HTML转换

### 🛠️ 技术特点
- Spring Boot 3 + SaToken权限管理
- MyBatis-Plus数据访问
- Flexmark Markdown解析器
- Redis缓存支持

## 代码示例

```java
@RestController
@RequestMapping("/admin/articles")
@SaCheckRole("admin")
public class ArticleManagementController {
    
    @PostMapping("/audit")
    public Result<Void> auditArticle(@RequestBody ArticleAuditRequest request) {
        boolean success = articleManagementService.auditArticle(request, auditorId);
        return success ? Result.success("审核成功") : Result.error("审核失败");
    }
}
```

## 任务列表

- [x] 文章列表查询
- [x] 内容审核流程
- [x] 批量操作功能
- [x] Markdown预览
- [ ] 评论管理
- [ ] 标签管理

## 数据表格

| 功能 | 状态 | 优先级 |
|------|------|--------|
| 文章管理 | ✅ 完成 | 高 |
| 用户管理 | ✅ 完成 | 高 |
| 权限管理 | ✅ 完成 | 高 |
| 文件上传 | ✅ 完成 | 中 |

> **提示**: 这是一个功能完整的文章管理系统，支持RBAC权限控制和丰富的管理功能。

---

**开发团队**: Cmr Personal Blog  
**技术栈**: Spring Boot 3 + SaToken + MyBatis-Plus + Redis
                </textarea>
                
                <div class="stats" id="stats">
                    <strong>📊 统计信息:</strong> 字数: 0 | 预计阅读: 0分钟 | 处理时间: 0ms
                </div>
            </div>
            
            <div class="preview-panel">
                <h3>👀 预览效果</h3>
                <div class="controls">
                    <button onclick="copyHtml()">📋 复制HTML</button>
                    <button onclick="downloadHtml()">💾 下载HTML</button>
                </div>
                <div class="preview-content" id="previewContent">
                    <p style="color: #999; text-align: center; padding: 50px;">
                        点击"实时预览"按钮查看Markdown渲染效果
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentHtmlContent = '';
        
        // 实时预览
        function previewMarkdown() {
            const content = document.getElementById('markdownEditor').value;
            if (!content.trim()) {
                showError('请输入Markdown内容');
                return;
            }
            
            const requestData = {
                content: content,
                enableCodeHighlight: true,
                enableTable: true,
                enableTaskList: true,
                enableStrikethrough: true,
                enableAutolink: true,
                theme: 'default'
            };
            
            // 这里需要替换为实际的API地址和token
            fetch('/api/markdown/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer YOUR_TOKEN_HERE' // 需要替换为实际token
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    const result = data.data;
                    currentHtmlContent = result.htmlContent;
                    document.getElementById('previewContent').innerHTML = result.htmlContent;
                    
                    // 更新统计信息
                    document.getElementById('stats').innerHTML = 
                        `<strong>📊 统计信息:</strong> 字数: ${result.wordCount} | 预计阅读: ${result.readingTime}分钟 | 处理时间: ${result.processTime}ms`;
                    
                    showSuccess('预览更新成功！');
                } else {
                    showError('预览失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('网络请求失败，请检查服务器连接');
                
                // 降级处理：简单的客户端渲染
                fallbackPreview(content);
            });
        }
        
        // 降级预览（简单处理）
        function fallbackPreview(content) {
            let html = content
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
                .replace(/\*(.*)\*/gim, '<em>$1</em>')
                .replace(/```[\s\S]*?```/gim, '<pre><code>$&</code></pre>')
                .replace(/`([^`]*)`/gim, '<code>$1</code>')
                .replace(/^\> (.*$)/gim, '<blockquote>$1</blockquote>')
                .replace(/\n/gim, '<br>');
            
            currentHtmlContent = html;
            document.getElementById('previewContent').innerHTML = html;
            
            const wordCount = content.replace(/[^\u4e00-\u9fff\w]/g, '').length;
            document.getElementById('stats').innerHTML = 
                `<strong>📊 统计信息:</strong> 字数: ${wordCount} | 预计阅读: ${Math.ceil(wordCount/200)}分钟 | 处理时间: <1ms (客户端渲染)`;
            
            showSuccess('使用客户端渲染预览（功能有限）');
        }
        
        // 加载示例内容
        function loadSample() {
            const sampleContent = `# 文章管理系统功能演示

## 🚀 核心功能

### 1. 文章列表管理
支持多维度筛选：
- 按标题、作者、分类筛选
- 按状态筛选（草稿/已发布/待审核等）
- 按时间范围筛选
- 支持关键词全文搜索

### 2. 内容审核流程

\`\`\`mermaid
graph LR
    A[提交文章] --> B[待审核]
    B --> C{审核}
    C -->|通过| D[已发布]
    C -->|拒绝| E[审核拒绝]
    D --> F[已下架]
\`\`\`

### 3. 批量操作功能

支持的批量操作：
- [x] 批量审核通过
- [x] 批量审核拒绝  
- [x] 批量置顶/取消置顶
- [x] 批量删除/恢复
- [x] 批量修改分类
- [x] 批量修改标签

## 📊 统计数据

| 指标 | 数量 | 占比 |
|------|------|------|
| 总文章数 | 1,000 | 100% |
| 已发布 | 800 | 80% |
| 待审核 | 50 | 5% |
| 草稿 | 100 | 10% |
| 已删除 | 50 | 5% |

## 💻 技术实现

### 后端技术栈
- **Spring Boot 3.5.3**: 现代化的Java框架
- **SaToken 1.37.0**: 轻量级权限认证框架
- **MyBatis-Plus 3.5.5**: 强大的ORM框架
- **Redis**: 高性能缓存
- **Flexmark**: Markdown解析器

### 核心代码示例

\`\`\`java
@RestController
@RequestMapping("/admin/articles")
@SaCheckRole("admin")
public class ArticleManagementController {
    
    @PostMapping("/batch")
    public Result<Map<String, Object>> batchOperateArticles(
            @RequestBody @Valid ArticleBatchOperationRequest request) {
        Long operatorId = StpUtil.getLoginIdAsLong();
        Map<String, Object> result = articleManagementService
            .batchOperateArticles(request, operatorId);
        return Result.success("批量操作完成", result);
    }
}
\`\`\`

## 🎯 使用场景

> **内容管理员**: 可以高效地审核和管理大量文章内容
> 
> **系统管理员**: 可以监控文章状态分布和用户活动
> 
> **内容作者**: 可以实时预览Markdown内容效果

## 🔗 相关链接

- [API文档](./ARTICLE-MANAGEMENT-API.md)
- [部署指南](./README.md)
- [开发日志](./DEVELOPMENT-LOG.md)

---

*最后更新: 2024年1月1日*`;
            
            document.getElementById('markdownEditor').value = sampleContent;
            showSuccess('示例内容已加载');
        }
        
        // 清空编辑器
        function clearEditor() {
            document.getElementById('markdownEditor').value = '';
            document.getElementById('previewContent').innerHTML = 
                '<p style="color: #999; text-align: center; padding: 50px;">编辑器已清空</p>';
            document.getElementById('stats').innerHTML = 
                '<strong>📊 统计信息:</strong> 字数: 0 | 预计阅读: 0分钟 | 处理时间: 0ms';
            currentHtmlContent = '';
        }
        
        // 复制HTML
        function copyHtml() {
            if (!currentHtmlContent) {
                showError('请先预览内容');
                return;
            }
            
            navigator.clipboard.writeText(currentHtmlContent).then(() => {
                showSuccess('HTML内容已复制到剪贴板');
            }).catch(() => {
                showError('复制失败，请手动复制');
            });
        }
        
        // 下载HTML
        function downloadHtml() {
            if (!currentHtmlContent) {
                showError('请先预览内容');
                return;
            }
            
            const fullHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown预览</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 2px; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 16px; color: #666; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
${currentHtmlContent}
</body>
</html>`;
            
            const blob = new Blob([fullHtml], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'markdown-preview.html';
            a.click();
            URL.revokeObjectURL(url);
            
            showSuccess('HTML文件下载成功');
        }
        
        // 显示成功消息
        function showSuccess(message) {
            showMessage(message, 'success');
        }
        
        // 显示错误消息
        function showError(message) {
            showMessage(message, 'error');
        }
        
        // 显示消息
        function showMessage(message, type) {
            const existingMsg = document.querySelector('.message');
            if (existingMsg) {
                existingMsg.remove();
            }
            
            const msgDiv = document.createElement('div');
            msgDiv.className = \`message \${type}\`;
            msgDiv.textContent = message;
            msgDiv.style.position = 'fixed';
            msgDiv.style.top = '20px';
            msgDiv.style.right = '20px';
            msgDiv.style.zIndex = '1000';
            msgDiv.style.padding = '10px 20px';
            msgDiv.style.borderRadius = '4px';
            msgDiv.style.color = type === 'success' ? '#27ae60' : '#e74c3c';
            msgDiv.style.backgroundColor = type === 'success' ? '#f2fdf5' : '#fdf2f2';
            msgDiv.style.border = \`1px solid \${type === 'success' ? '#27ae60' : '#e74c3c'}\`;
            
            document.body.appendChild(msgDiv);
            
            setTimeout(() => {
                msgDiv.remove();
            }, 3000);
        }
        
        // 页面加载完成后自动加载示例
        window.onload = function() {
            loadSample();
            setTimeout(previewMarkdown, 500);
        };
    </script>
</body>
</html>

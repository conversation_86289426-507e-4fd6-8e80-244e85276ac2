<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.UserStatsMapper">

    <!-- 初始化用户统计数据 -->
    <insert id="initUserStats">
        INSERT INTO user_stats (
            user_id, 
            article_count, 
            total_view_count, 
            total_like_count, 
            total_comment_count, 
            total_collect_count, 
            follower_count, 
            following_count,
            create_time,
            update_time
        ) VALUES (
            #{userId}, 
            0, 0, 0, 0, 0, 0, 0,
            NOW(),
            NOW()
        ) ON DUPLICATE KEY UPDATE
            update_time = NOW()
    </insert>

    <!-- 更新用户统计数据 -->
    <update id="updateUserStats">
        UPDATE user_stats 
        SET ${field} = GREATEST(0, ${field} + #{increment}),
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 重新计算用户统计数据 -->
    <update id="recalculateUserStats">
        UPDATE user_stats us
        SET 
            article_count = (
                SELECT COUNT(*) 
                FROM article 
                WHERE author_id = #{userId} AND status = 1
            ),
            total_view_count = (
                SELECT COALESCE(SUM(view_count), 0) 
                FROM article 
                WHERE author_id = #{userId} AND status = 1
            ),
            total_like_count = (
                SELECT COALESCE(SUM(like_count), 0) 
                FROM article 
                WHERE author_id = #{userId} AND status = 1
            ),
            total_comment_count = (
                SELECT COALESCE(SUM(comment_count), 0) 
                FROM article 
                WHERE author_id = #{userId} AND status = 1
            ),
            total_collect_count = (
                SELECT COALESCE(SUM(collect_count), 0) 
                FROM article 
                WHERE author_id = #{userId} AND status = 1
            ),
            follower_count = (
                SELECT COUNT(*) 
                FROM user_follow 
                WHERE following_id = #{userId} AND status = 1
            ),
            following_count = (
                SELECT COUNT(*) 
                FROM user_follow 
                WHERE follower_id = #{userId} AND status = 1
            ),
            update_time = NOW()
        WHERE us.user_id = #{userId}
    </update>

</mapper>

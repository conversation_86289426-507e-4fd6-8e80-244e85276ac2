package com.blog.cmrpersonalblog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.blog.cmrpersonalblog.entity.SysRolePermission;
import com.blog.cmrpersonalblog.mapper.SysRolePermissionMapper;
import com.blog.cmrpersonalblog.service.SysRolePermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色权限关联服务实现类
 */
@Service
public class SysRolePermissionServiceImpl extends ServiceImpl<SysRolePermissionMapper, SysRolePermission> implements SysRolePermissionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        try {
            // 先删除角色的所有权限关联
            removeAllPermissionsFromRole(roleId);
            
            // 如果权限列表不为空，则添加新的权限关联
            if (permissionIds != null && !permissionIds.isEmpty()) {
                List<SysRolePermission> rolePermissions = permissionIds.stream()
                        .map(permissionId -> {
                            SysRolePermission rolePermission = new SysRolePermission();
                            rolePermission.setRoleId(roleId);
                            rolePermission.setPermId(permissionId);
                            return rolePermission;
                        })
                        .collect(Collectors.toList());
                
                return baseMapper.insertBatch(rolePermissions) > 0;
            }
            return true;
        } catch (Exception e) {
            throw new RuntimeException("分配权限失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAllPermissionsFromRole(Long roleId) {
        try {
            return baseMapper.deleteByRoleId(roleId) >= 0;
        } catch (Exception e) {
            throw new RuntimeException("移除角色权限失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAllRolesFromPermission(Long permissionId) {
        try {
            return baseMapper.deleteByPermissionId(permissionId) >= 0;
        } catch (Exception e) {
            throw new RuntimeException("移除权限角色失败", e);
        }
    }

    @Override
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        return baseMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    public List<Long> getRoleIdsByPermissionId(Long permissionId) {
        return baseMapper.selectRoleIdsByPermissionId(permissionId);
    }

    @Override
    public boolean hasPermission(Long roleId, Long permissionId) {
        return baseMapper.countByRoleIdAndPermissionId(roleId, permissionId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRolesByPermissionIds(List<Long> permissionIds) {
        try {
            if (permissionIds == null || permissionIds.isEmpty()) {
                return true;
            }
            return baseMapper.deleteByPermissionIds(permissionIds) >= 0;
        } catch (Exception e) {
            throw new RuntimeException("批量移除权限角色失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removePermissionsByRoleIds(List<Long> roleIds) {
        try {
            if (roleIds == null || roleIds.isEmpty()) {
                return true;
            }
            return baseMapper.deleteByRoleIds(roleIds) >= 0;
        } catch (Exception e) {
            throw new RuntimeException("批量移除角色权限失败", e);
        }
    }
}

# 用户活动记录表设置说明

## 问题描述

如果您在使用后台管理用户详情功能时遇到以下错误：

```
java.sql.SQLSyntaxErrorException: Table 'cmr_blog.user_activity' doesn't exist
```

这是因为数据库中还没有创建 `user_activity` 表。

## 解决方案

### 方案一：执行SQL脚本（推荐）

1. 打开您的MySQL数据库管理工具（如Navicat、phpMyAdmin、MySQL Workbench等）
2. 连接到您的 `cmr_blog` 数据库
3. 执行 `create-user-activity-table.sql` 文件中的SQL语句

或者在命令行中执行：

```bash
mysql -u your_username -p cmr_blog < create-user-activity-table.sql
```

### 方案二：手动创建表

在MySQL中执行以下SQL语句：

```sql
USE cmr_blog;

CREATE TABLE IF NOT EXISTS `user_activity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '活动记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `activity_type` VARCHAR(50) NOT NULL COMMENT '活动类型',
    `activity_description` VARCHAR(500) COMMENT '活动描述',
    `target_id` BIGINT COMMENT '目标ID（文章ID、用户ID等）',
    `target_type` VARCHAR(50) COMMENT '目标类型（article、user、comment等）',
    `ip_address` VARCHAR(128) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `result` VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '操作结果（SUCCESS、FAILED）',
    `extra_data` JSON COMMENT '额外数据（JSON格式）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_activity_type` (`activity_type`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_user_activity` (`user_id`, `activity_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动记录表';
```

## 验证安装

### 方法一：使用测试接口

访问以下测试接口来检查表是否创建成功：

```http
GET http://localhost:8081/test/check-user-activity-table
Authorization: Bearer your_admin_token
```

### 方法二：直接查询数据库

在MySQL中执行：

```sql
-- 检查表是否存在
SHOW TABLES LIKE 'user_activity';

-- 查看表结构
DESCRIBE user_activity;

-- 查看表中的记录数
SELECT COUNT(*) FROM user_activity;
```

## 功能说明

创建 `user_activity` 表后，系统将支持以下功能：

### 用户行为追踪
- 登录/登出记录
- 文章发布/编辑/删除记录
- 点赞/评论/关注行为记录
- 个人资料更新记录

### 安全监控
- IP地址追踪
- 设备信息记录
- 可疑登录检测
- 操作审计日志

### 数据分析
- 用户活跃度统计
- 行为模式分析
- 登录频率统计
- 操作成功率分析

## 注意事项

1. **权限要求**：确保您的MySQL用户有创建表的权限
2. **数据库名称**：确保您的数据库名称是 `cmr_blog`，如果不是请修改SQL中的数据库名
3. **字符集**：表使用 `utf8mb4` 字符集，支持emoji和特殊字符
4. **存储引擎**：使用 `InnoDB` 存储引擎，支持事务和外键
5. **索引优化**：已创建必要的索引来优化查询性能

## 兼容性处理

即使没有创建 `user_activity` 表，后台管理用户详情功能也能正常工作：

- 系统会自动检测表是否存在
- 如果表不存在，相关功能会返回默认值
- 不会影响其他功能的正常使用
- 日志中会记录警告信息但不会抛出异常

## 故障排除

### 问题1：权限不足
```
ERROR 1142 (42000): CREATE command denied to user
```
**解决方案**：联系数据库管理员授予CREATE权限

### 问题2：数据库不存在
```
ERROR 1049 (42000): Unknown database 'cmr_blog'
```
**解决方案**：先创建数据库或修改SQL中的数据库名

### 问题3：字符集问题
```
ERROR 1273 (HY000): Unknown collation: 'utf8mb4_unicode_ci'
```
**解决方案**：使用 `utf8mb4_general_ci` 替代或升级MySQL版本

## 联系支持

如果您在设置过程中遇到问题，请：

1. 检查MySQL版本（建议5.7+）
2. 确认数据库连接配置
3. 查看应用日志中的详细错误信息
4. 使用测试接口验证功能状态

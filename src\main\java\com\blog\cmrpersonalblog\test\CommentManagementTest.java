package com.blog.cmrpersonalblog.test;

import com.blog.cmrpersonalblog.service.CommentManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 评论管理测试类
 * 用于验证CommentManagementServiceImpl是否能正常编译和运行
 */
@Component
public class CommentManagementTest {

    @Autowired
    private CommentManagementService commentManagementService;

    /**
     * 测试删除评论功能
     */
    public boolean testDeleteComment(Long commentId, Long operatorId) {
        try {
            return commentManagementService.deleteComment(commentId, operatorId);
        } catch (Exception e) {
            System.err.println("删除评论测试失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 测试获取评论统计
     */
    public void testGetCommentStatistics() {
        try {
            var statistics = commentManagementService.getCommentStatistics();
            System.out.println("评论统计: " + statistics);
        } catch (Exception e) {
            System.err.println("获取评论统计失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取待审核评论数量
     */
    public void testGetPendingAuditCount() {
        try {
            Long count = commentManagementService.getPendingAuditCount();
            System.out.println("待审核评论数量: " + count);
        } catch (Exception e) {
            System.err.println("获取待审核评论数量失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

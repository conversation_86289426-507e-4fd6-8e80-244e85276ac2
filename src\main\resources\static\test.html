<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 接口测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .token-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>API 接口测试页面</h1>
    
    <div class="container">
        <h2>1. 健康检查</h2>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="healthResponse" class="response"></div>
    </div>

    <div class="container">
        <h2>2. 用户登录</h2>
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456" placeholder="请输入密码">
        </div>
        <button onclick="testLogin()">登录</button>
        <div id="loginResponse" class="response"></div>
    </div>

    <div class="container">
        <h2>3. 获取用户信息</h2>
        <div class="token-info">
            <strong>当前Token:</strong> <span id="currentToken">未登录</span>
        </div>
        <button onclick="testUserInfo()">获取用户信息</button>
        <div id="userInfoResponse" class="response"></div>
    </div>

    <div class="container">
        <h2>4. 用户注销</h2>
        <button onclick="testLogout()">注销</button>
        <div id="logoutResponse" class="response"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        let currentToken = '';

        // 更新Token显示
        function updateTokenDisplay() {
            document.getElementById('currentToken').textContent = currentToken || '未登录';
        }

        // 显示响应结果
        function showResponse(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `response ${isSuccess ? 'success' : 'error'}`;
        }

        // 健康检查
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/test/health`);
                const data = await response.json();
                showResponse('healthResponse', data, response.ok);
            } catch (error) {
                showResponse('healthResponse', { error: error.message }, false);
            }
        }

        // 用户登录
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showResponse('loginResponse', { error: '请输入用户名和密码' }, false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userName: username,
                        password: password
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentToken = data.data.token;
                    updateTokenDisplay();
                    showResponse('loginResponse', data, true);
                } else {
                    showResponse('loginResponse', data, false);
                }
            } catch (error) {
                showResponse('loginResponse', { error: error.message }, false);
            }
        }

        // 获取用户信息
        async function testUserInfo() {
            if (!currentToken) {
                showResponse('userInfoResponse', { error: '请先登录' }, false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/userinfo`, {
                    headers: {
                        'Authorization': currentToken
                    }
                });

                const data = await response.json();
                showResponse('userInfoResponse', data, response.ok);
            } catch (error) {
                showResponse('userInfoResponse', { error: error.message }, false);
            }
        }

        // 用户注销
        async function testLogout() {
            if (!currentToken) {
                showResponse('logoutResponse', { error: '请先登录' }, false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': currentToken
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentToken = '';
                    updateTokenDisplay();
                }
                
                showResponse('logoutResponse', data, response.ok);
            } catch (error) {
                showResponse('logoutResponse', { error: error.message }, false);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTokenDisplay();
        });
    </script>
</body>
</html>

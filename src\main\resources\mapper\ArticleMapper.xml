<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.ArticleMapper">

    <!-- 文章信息结果映射 -->
    <resultMap id="ArticleInfoMap" type="com.blog.cmrpersonalblog.dto.UserProfileResponse$ArticleInfo">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="summary" property="summary" />
        <result column="cover_image" property="coverImage" />
        <result column="view_count" property="viewCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="collect_count" property="collectCount" />
        <result column="publish_time" property="publishTime" />
        <result column="category_name" property="categoryName" />
    </resultMap>

    <!-- 用户统计信息结果映射 -->
    <resultMap id="UserStatsInfoMap" type="com.blog.cmrpersonalblog.dto.UserProfileResponse$UserStatsInfo">
        <result column="article_count" property="articleCount" />
        <result column="total_view_count" property="totalViewCount" />
        <result column="total_like_count" property="totalLikeCount" />
        <result column="total_comment_count" property="totalCommentCount" />
        <result column="total_collect_count" property="totalCollectCount" />
        <result column="follower_count" property="followerCount" />
        <result column="following_count" property="followingCount" />
    </resultMap>

    <!-- 分页查询用户文章列表 -->
    <select id="selectUserArticlePage" resultMap="ArticleInfoMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.publish_time,
            ac.name as category_name
        FROM article a
        LEFT JOIN article_category ac ON a.category_id = ac.id
        <where>
            a.author_id = #{query.userId}
            <if test="query.status != null">
                AND a.status = #{query.status}
            </if>
            <if test="query.categoryId != null">
                AND a.category_id = #{query.categoryId}
            </if>
            <if test="query.tagName != null and query.tagName != ''">
                AND FIND_IN_SET(#{query.tagName}, a.tags) > 0
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (a.title LIKE CONCAT('%', #{query.keyword}, '%') 
                     OR a.summary LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY a.${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY a.publish_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 获取用户最近发布的文章 -->
    <select id="selectRecentArticlesByUserId" resultMap="ArticleInfoMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.publish_time,
            ac.name as category_name
        FROM article a
        LEFT JOIN article_category ac ON a.category_id = ac.id
        WHERE a.author_id = #{userId} AND a.status = 1
        ORDER BY a.publish_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户文章数据 -->
    <select id="selectUserArticleStats" resultMap="UserStatsInfoMap">
        SELECT 
            COUNT(*) as article_count,
            COALESCE(SUM(view_count), 0) as total_view_count,
            COALESCE(SUM(like_count), 0) as total_like_count,
            COALESCE(SUM(comment_count), 0) as total_comment_count,
            COALESCE(SUM(collect_count), 0) as total_collect_count,
            0 as follower_count,
            0 as following_count
        FROM article 
        WHERE author_id = #{userId} AND status = 1
    </select>

    <!-- 更新文章统计数据 -->
    <update id="updateArticleStats">
        UPDATE article
        SET ${field} = ${field} + #{increment}
        WHERE id = #{articleId}
    </update>

    <!-- ==================== 文章管理相关SQL ==================== -->

    <!-- 文章管理响应结果映射 -->
    <resultMap id="ArticleManagementResponseMap" type="com.blog.cmrpersonalblog.dto.ArticleManagementResponse">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="summary" property="summary" />
        <result column="cover_image" property="coverImage" />
        <result column="author_id" property="authorId" />
        <result column="author_name" property="authorName" />
        <result column="author_nick_name" property="authorNickName" />
        <result column="author_avatar" property="authorAvatar" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="tags" property="tags" />
        <result column="view_count" property="viewCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="collect_count" property="collectCount" />
        <result column="share_count" property="shareCount" />
        <result column="is_original" property="isOriginal" />
        <result column="is_top" property="isTop" />
        <result column="status" property="status" />
        <result column="publish_time" property="publishTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_user_id" property="auditUserId" />
        <result column="audit_user_name" property="auditUserName" />
        <result column="audit_remark" property="auditRemark" />
        <result column="word_count" property="wordCount" />
        <result column="reading_time" property="readingTime" />
    </resultMap>

    <!-- 文章详情响应结果映射 -->
    <resultMap id="ArticleDetailResponseMap" type="com.blog.cmrpersonalblog.dto.ArticleDetailResponse">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="summary" property="summary" />
        <result column="content" property="content" />
        <result column="cover_image" property="coverImage" />
        <result column="author_id" property="authorId" />
        <result column="author_name" property="authorName" />
        <result column="author_nick_name" property="authorNickName" />
        <result column="author_avatar" property="authorAvatar" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="tags" property="tags" />
        <result column="view_count" property="viewCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="collect_count" property="collectCount" />
        <result column="share_count" property="shareCount" />
        <result column="is_original" property="isOriginal" />
        <result column="is_top" property="isTop" />
        <result column="status" property="status" />
        <result column="publish_time" property="publishTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_user_id" property="auditUserId" />
        <result column="audit_user_name" property="auditUserName" />
        <result column="audit_remark" property="auditRemark" />
        <result column="audit_reason" property="auditReason" />
        <result column="word_count" property="wordCount" />
        <result column="reading_time" property="readingTime" />
    </resultMap>

    <!-- 分页查询文章管理列表 -->
    <select id="selectArticleManagementPage" resultMap="ArticleManagementResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time,
            a.audit_time,
            a.audit_user_id,
            au.user_name as audit_user_name,
            a.audit_remark,
            CHAR_LENGTH(REPLACE(REPLACE(a.content, ' ', ''), '\n', '')) as word_count,
            CEIL(CHAR_LENGTH(REPLACE(REPLACE(a.content, ' ', ''), '\n', '')) / 200) as reading_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        LEFT JOIN sys_user au ON a.audit_user_id = au.user_id
        <where>
            <if test="query.title != null and query.title != ''">
                AND a.title LIKE CONCAT('%', #{query.title}, '%')
            </if>
            <if test="query.authorId != null">
                AND a.author_id = #{query.authorId}
            </if>
            <if test="query.authorName != null and query.authorName != ''">
                AND u.user_name LIKE CONCAT('%', #{query.authorName}, '%')
            </if>
            <if test="query.categoryId != null">
                AND a.category_id = #{query.categoryId}
            </if>
            <if test="query.categoryName != null and query.categoryName != ''">
                AND ac.name LIKE CONCAT('%', #{query.categoryName}, '%')
            </if>
            <if test="query.status != null">
                AND a.status = #{query.status}
            </if>
            <if test="query.isTop != null">
                AND a.is_top = #{query.isTop}
            </if>
            <if test="query.isOriginal != null">
                AND a.is_original = #{query.isOriginal}
            </if>
            <if test="query.tags != null and query.tags != ''">
                AND a.tags LIKE CONCAT('%', #{query.tags}, '%')
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (a.title LIKE CONCAT('%', #{query.keyword}, '%')
                     OR a.content LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND a.publish_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND a.publish_time &lt;= #{query.endTime}
            </if>
            <if test="query.minViewCount != null">
                AND a.view_count >= #{query.minViewCount}
            </if>
            <if test="query.maxViewCount != null">
                AND a.view_count &lt;= #{query.maxViewCount}
            </if>
            <if test="query.minLikeCount != null">
                AND a.like_count >= #{query.minLikeCount}
            </if>
            <if test="query.maxLikeCount != null">
                AND a.like_count &lt;= #{query.maxLikeCount}
            </if>
        </where>
        <choose>
            <when test="query.sortField != null and query.sortField != ''">
                ORDER BY
                <choose>
                    <when test="query.sortField == 'publish_time'">a.publish_time</when>
                    <when test="query.sortField == 'view_count'">a.view_count</when>
                    <when test="query.sortField == 'like_count'">a.like_count</when>
                    <when test="query.sortField == 'create_time'">a.create_time</when>
                    <otherwise>a.create_time</otherwise>
                </choose>
                <if test="query.sortOrder != null and query.sortOrder != ''">
                    ${query.sortOrder}
                </if>
            </when>
            <otherwise>
                ORDER BY a.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 获取文章详情 -->
    <select id="selectArticleDetailById" resultMap="ArticleDetailResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time,
            a.audit_time,
            a.audit_user_id,
            au.user_name as audit_user_name,
            a.audit_remark,
            a.audit_reason,
            CHAR_LENGTH(REPLACE(REPLACE(a.content, ' ', ''), '\n', '')) as word_count,
            CEIL(CHAR_LENGTH(REPLACE(REPLACE(a.content, ' ', ''), '\n', '')) / 200) as reading_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        LEFT JOIN sys_user au ON a.audit_user_id = au.user_id
        WHERE a.id = #{articleId}
    </select>

    <!-- 批量更新文章状态 -->
    <update id="batchUpdateArticleStatus">
        UPDATE article
        SET status = #{status}, update_time = NOW()
        <if test="operatorId != null">
            , audit_user_id = #{operatorId}, audit_time = NOW()
        </if>
        WHERE id IN
        <foreach collection="articleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新文章置顶状态 -->
    <update id="batchUpdateArticleTop">
        UPDATE article
        SET is_top = #{isTop}, update_time = NOW()
        WHERE id IN
        <foreach collection="articleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量删除文章 -->
    <update id="batchDeleteArticles">
        UPDATE article
        SET status = 2, update_time = NOW()
        WHERE id IN
        <foreach collection="articleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新文章审核信息 -->
    <update id="updateArticleAuditInfo">
        UPDATE article
        SET status = #{status},
            audit_user_id = #{auditUserId},
            audit_time = NOW(),
            audit_remark = #{auditRemark},
            audit_reason = #{auditReason},
            update_time = NOW()
        WHERE id = #{articleId}
    </update>

    <!-- 获取文章统计信息 -->
    <select id="selectArticleStatistics" resultType="map">
        SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as draft_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as published_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as deleted_count,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as unpublished_count,
            SUM(CASE WHEN is_top = 1 THEN 1 ELSE 0 END) as top_count,
            SUM(view_count) as total_views,
            SUM(like_count) as total_likes,
            SUM(comment_count) as total_comments,
            SUM(collect_count) as total_collects
        FROM article
    </select>

    <!-- 获取待审核文章数量 -->
    <select id="selectPendingAuditCount" resultType="long">
        SELECT COUNT(*) FROM article WHERE status = 3
    </select>

    <!-- 获取文章状态分布 -->
    <select id="selectArticleStatusDistribution" resultType="map">
        SELECT
            status,
            COUNT(*) as count,
            CASE
                WHEN status = 0 THEN '草稿'
                WHEN status = 1 THEN '已发布'
                WHEN status = 2 THEN '已删除'
                WHEN status = 3 THEN '待审核'
                WHEN status = 4 THEN '审核拒绝'
                WHEN status = 5 THEN '已下架'
                ELSE '未知'
            END as status_name
        FROM article
        GROUP BY status
        ORDER BY status
    </select>

    <!-- 获取热门文章列表 -->
    <select id="selectPopularArticles" resultMap="ArticleManagementResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        WHERE a.status = 1
        ORDER BY (a.view_count * 0.4 + a.like_count * 0.3 + a.comment_count * 0.2 + a.collect_count * 0.1) DESC
        LIMIT #{limit}
    </select>

    <!-- 获取最新文章列表 -->
    <select id="selectLatestArticles" resultMap="ArticleManagementResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        WHERE a.status = 1
        ORDER BY a.publish_time DESC
        LIMIT #{limit}
    </select>

    <!-- 搜索文章 -->
    <select id="searchArticles" resultMap="ArticleManagementResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        WHERE (a.title LIKE CONCAT('%', #{keyword}, '%')
               OR a.content LIKE CONCAT('%', #{keyword}, '%')
               OR a.tags LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY a.publish_time DESC
    </select>

    <!-- 获取用户文章管理列表 -->
    <select id="selectUserArticleManagementPage" resultMap="ArticleManagementResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        WHERE a.author_id = #{userId}
        ORDER BY a.create_time DESC
    </select>

    <!-- 获取分类文章管理列表 -->
    <select id="selectCategoryArticleManagementPage" resultMap="ArticleManagementResponseMap">
        SELECT
            a.id,
            a.title,
            a.summary,
            a.cover_image,
            a.author_id,
            u.user_name as author_name,
            u.nick_name as author_nick_name,
            u.avatar as author_avatar,
            a.category_id,
            ac.name as category_name,
            a.tags,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.collect_count,
            a.share_count,
            a.is_original,
            a.is_top,
            a.status,
            a.publish_time,
            a.create_time,
            a.update_time
        FROM article a
        LEFT JOIN sys_user u ON a.author_id = u.user_id
        LEFT JOIN article_category ac ON a.category_id = ac.id
        WHERE a.category_id = #{categoryId}
        ORDER BY a.publish_time DESC
    </select>

    <!-- 重新计算文章统计数据 -->
    <update id="recalculateArticleStats">
        UPDATE article a
        SET
            view_count = COALESCE((SELECT COUNT(*) FROM article_view av WHERE av.article_id = a.id), 0),
            like_count = COALESCE((SELECT COUNT(*) FROM user_like ul WHERE ul.target_id = a.id AND ul.target_type = 1 AND ul.status = 1), 0),
            comment_count = COALESCE((SELECT COUNT(*) FROM comment c WHERE c.article_id = a.id AND c.status = 1), 0),
            collect_count = COALESCE((SELECT COUNT(*) FROM user_collect uc WHERE uc.article_id = a.id AND uc.status = 1), 0)
        WHERE a.id = #{articleId}
    </update>

</mapper>

package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.cmrpersonalblog.entity.SysPermission;

import java.util.List;

/**
 * 系统权限服务接口
 */
public interface SysPermissionService extends IService<SysPermission> {

    /**
     * 根据权限key查询权限信息
     * @param permKey 权限标识
     * @return 权限信息
     */
    SysPermission getPermissionByPermKey(String permKey);

    /**
     * 根据角色ID查询权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> getPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID查询权限列表（通过角色关联）
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SysPermission> getPermissionsByUserId(Long userId);

    /**
     * 查询所有权限
     * @return 权限列表
     */
    List<SysPermission> getAllPermissions();

    /**
     * 检查权限是否可以删除（是否被角色使用）
     * @param permId 权限ID
     * @return 是否可以删除
     */
    boolean canDeletePermission(Long permId);

    /**
     * 批量查询权限信息
     * @param permKeys 权限标识列表
     * @return 权限列表
     */
    List<SysPermission> getPermissionsByPermKeys(List<String> permKeys);

    /**
     * 检查用户是否拥有指定权限
     * @param userId 用户ID
     * @param permKey 权限标识
     * @return 是否拥有权限
     */
    boolean hasPermission(Long userId, String permKey);

    /**
     * 检查用户是否拥有任意一个权限
     * @param userId 用户ID
     * @param permKeys 权限标识列表
     * @return 是否拥有任意权限
     */
    boolean hasAnyPermission(Long userId, List<String> permKeys);
}

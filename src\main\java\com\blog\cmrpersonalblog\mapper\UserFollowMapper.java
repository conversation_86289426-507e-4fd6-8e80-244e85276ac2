package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.cmrpersonalblog.dto.UserInteractionResponse;
import com.blog.cmrpersonalblog.entity.UserFollow;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户关注Mapper接口
 */
public interface UserFollowMapper extends BaseMapper<UserFollow> {

    /**
     * 获取用户关注列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 关注用户列表
     */
    List<UserInteractionResponse.FollowingUserInfo> selectFollowingUsers(@Param("userId") Long userId, 
                                                                          @Param("limit") Integer limit);

    /**
     * 获取用户粉丝列表
     * @param userId 用户ID
     * @param currentUserId 当前用户ID（用于判断是否回关）
     * @param limit 限制数量
     * @return 粉丝用户列表
     */
    List<UserInteractionResponse.FollowerUserInfo> selectFollowers(@Param("userId") Long userId, 
                                                                    @Param("currentUserId") Long currentUserId,
                                                                    @Param("limit") Integer limit);

    /**
     * 检查用户是否关注了目标用户
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否关注
     */
    Boolean checkIsFollowing(@Param("followerId") Long followerId, 
                            @Param("followingId") Long followingId);

    /**
     * 获取用户关注数
     * @param userId 用户ID
     * @return 关注数
     */
    Integer countFollowing(@Param("userId") Long userId);

    /**
     * 获取用户粉丝数
     * @param userId 用户ID
     * @return 粉丝数
     */
    Integer countFollowers(@Param("userId") Long userId);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.blog.cmrpersonalblog.entity.SysRole">
        <id column="id" property="id" />
        <result column="role_key" property="roleKey" />
        <result column="role_name" property="roleName" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 角色权限映射结果 -->
    <resultMap id="RoleWithPermissionsMap" type="com.blog.cmrpersonalblog.entity.SysRole" extends="BaseResultMap">
        <collection property="permissions" ofType="com.blog.cmrpersonalblog.entity.SysPermission">
            <id column="perm_id" property="id" />
            <result column="perm_key" property="permKey" />
            <result column="perm_name" property="permName" />
            <result column="perm_description" property="description" />
        </collection>
    </resultMap>

    <!-- 根据角色key查询角色信息 -->
    <select id="selectRoleByRoleKey" parameterType="String" resultMap="BaseResultMap">
        SELECT id, role_key, role_name, description, status, 
               create_by, create_time, update_by, update_time
        FROM sys_role 
        WHERE role_key = #{roleKey}
    </select>

    <!-- 根据角色ID查询角色拥有的权限 -->
    <select id="selectPermissionsByRoleId" parameterType="Long" resultType="com.blog.cmrpersonalblog.entity.SysPermission">
        SELECT sp.id, sp.perm_key, sp.perm_name, sp.description,
               sp.create_by, sp.create_time, sp.update_by, sp.update_time
        FROM sys_permission sp
        INNER JOIN sys_role_permission srp ON sp.id = srp.perm_id
        WHERE srp.role_id = #{roleId}
        ORDER BY sp.id
    </select>

    <!-- 查询所有启用的角色 -->
    <select id="selectEnabledRoles" resultMap="BaseResultMap">
        SELECT id, role_key, role_name, description, status, 
               create_by, create_time, update_by, update_time
        FROM sys_role 
        WHERE status = '0'
        ORDER BY id
    </select>

    <!-- 根据用户ID查询用户拥有的角色（带权限信息） -->
    <select id="selectRolesByUserIdWithPermissions" parameterType="Long" resultMap="RoleWithPermissionsMap">
        SELECT sr.id, sr.role_key, sr.role_name, sr.description, sr.status,
               sr.create_by, sr.create_time, sr.update_by, sr.update_time,
               sp.id as perm_id, sp.perm_key, sp.perm_name, sp.description as perm_description
        FROM sys_role sr
        INNER JOIN sys_user_role sur ON sr.id = sur.role_id
        LEFT JOIN sys_role_permission srp ON sr.id = srp.role_id
        LEFT JOIN sys_permission sp ON srp.perm_id = sp.id
        WHERE sur.user_id = #{userId} AND sr.status = '0'
        ORDER BY sr.id, sp.id
    </select>

    <!-- 检查角色是否被用户使用 -->
    <select id="countUsersByRoleId" parameterType="Long" resultType="int">
        SELECT COUNT(*)
        FROM sys_user_role 
        WHERE role_id = #{roleId}
    </select>

</mapper>

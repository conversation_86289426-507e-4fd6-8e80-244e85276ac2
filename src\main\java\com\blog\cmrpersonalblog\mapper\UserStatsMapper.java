package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.blog.cmrpersonalblog.entity.UserStats;
import org.apache.ibatis.annotations.Param;

/**
 * 用户统计Mapper接口
 */
public interface UserStatsMapper extends BaseMapper<UserStats> {

    /**
     * 初始化用户统计数据
     * @param userId 用户ID
     */
    void initUserStats(@Param("userId") Long userId);

    /**
     * 更新用户统计数据
     * @param userId 用户ID
     * @param field 字段名
     * @param increment 增量
     */
    void updateUserStats(@Param("userId") Long userId, 
                        @Param("field") String field, 
                        @Param("increment") Integer increment);

    /**
     * 重新计算用户统计数据
     * @param userId 用户ID
     */
    void recalculateUserStats(@Param("userId") Long userId);
}

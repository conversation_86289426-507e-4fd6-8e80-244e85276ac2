package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.dto.AdminUserProfileResponse;
import com.blog.cmrpersonalblog.dto.UserActivityQueryRequest;
import com.blog.cmrpersonalblog.entity.UserActivity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户活动记录Mapper接口
 */
public interface UserActivityMapper extends BaseMapper<UserActivity> {

    /**
     * 分页查询活动记录
     * @param page 分页对象
     * @param query 查询条件
     * @return 活动记录分页数据
     */
    IPage<AdminUserProfileResponse.ActivityRecord> selectActivityPage(Page<AdminUserProfileResponse.ActivityRecord> page,
                                                                       @Param("query") UserActivityQueryRequest query);

    /**
     * 获取用户最近活动记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 活动记录列表
     */
    List<AdminUserProfileResponse.ActivityRecord> selectRecentActivities(@Param("userId") Long userId,
                                                                          @Param("limit") Integer limit);

    /**
     * 获取用户互动行为统计
     * @param userId 用户ID
     * @return 互动行为信息
     */
    AdminUserProfileResponse.InteractionInfo selectUserInteractionInfo(@Param("userId") Long userId);

    /**
     * 获取用户安全信息
     * @param userId 用户ID
     * @return 安全信息
     */
    AdminUserProfileResponse.SecurityInfo selectUserSecurityInfo(@Param("userId") Long userId);

    /**
     * 获取用户最近登录IP列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return IP地址列表
     */
    List<String> selectRecentLoginIps(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 统计用户登录天数
     * @param userId 用户ID
     * @return 登录天数
     */
    Integer countLoginDays(@Param("userId") Long userId);

    /**
     * 统计用户总会话数
     * @param userId 用户ID
     * @return 会话数
     */
    Integer countTotalSessions(@Param("userId") Long userId);

    /**
     * 统计可疑登录次数
     * @param userId 用户ID
     * @param days 统计天数
     * @return 可疑登录次数
     */
    Integer countSuspiciousLogins(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 记录用户活动
     * @param activity 活动记录
     */
    void recordActivity(UserActivity activity);

    /**
     * 批量删除过期活动记录
     * @param beforeTime 删除此时间之前的记录
     * @return 删除数量
     */
    Integer deleteExpiredActivities(@Param("beforeTime") LocalDateTime beforeTime);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.UserActivityMapper">

    <!-- 活动记录结果映射 -->
    <resultMap id="ActivityRecordMap" type="com.blog.cmrpersonalblog.dto.AdminUserProfileResponse$ActivityRecord">
        <result column="activity_type" property="activityType" />
        <result column="activity_description" property="activityDescription" />
        <result column="create_time" property="activityTime" />
        <result column="ip_address" property="ipAddress" />
        <result column="user_agent" property="userAgent" />
        <result column="result" property="result" />
    </resultMap>

    <!-- 互动行为信息结果映射 -->
    <resultMap id="InteractionInfoMap" type="com.blog.cmrpersonalblog.dto.AdminUserProfileResponse$InteractionInfo">
        <result column="last_login_time" property="lastLoginTime" />
        <result column="last_active_time" property="lastActiveTime" />
        <result column="login_days" property="loginDays" />
        <result column="avg_daily_active_time" property="avgDailyActiveTime" />
        <result column="total_sessions" property="totalSessions" />
    </resultMap>

    <!-- 安全信息结果映射 -->
    <resultMap id="SecurityInfoMap" type="com.blog.cmrpersonalblog.dto.AdminUserProfileResponse$SecurityInfo">
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="last_login_location" property="lastLoginLocation" />
        <result column="last_login_device" property="lastLoginDevice" />
        <result column="suspicious_login_count" property="suspiciousLoginCount" />
        <result column="is_email_verified" property="isEmailVerified" />
        <result column="is_phone_verified" property="isPhoneVerified" />
        <result column="password_last_changed" property="passwordLastChanged" />
    </resultMap>

    <!-- 分页查询活动记录 -->
    <select id="selectActivityPage" resultMap="ActivityRecordMap">
        SELECT
            activity_type,
            activity_description,
            create_time,
            ip_address,
            user_agent,
            result
        FROM user_activity
        <where>
            <if test="query.userId != null">
                AND user_id = #{query.userId}
            </if>
            <if test="query.activityTypes != null and query.activityTypes.size() > 0">
                AND activity_type IN
                <foreach collection="query.activityTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="query.typeGroup != null and query.typeGroup != ''">
                <choose>
                    <when test="query.typeGroup == 'AUTH'">
                        AND activity_type IN ('LOGIN', 'LOGOUT', 'LOGIN_FAILED', 'PASSWORD_CHANGED')
                    </when>
                    <when test="query.typeGroup == 'MANAGEMENT'">
                        AND activity_type IN ('USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'USER_STATUS_CHANGED',
                                             'USER_ROLE_ASSIGNED', 'USER_ROLE_REMOVED', 'ROLE_CREATED', 'ROLE_UPDATED',
                                             'ROLE_DELETED', 'PERMISSION_ASSIGNED', 'PERMISSION_REMOVED')
                    </when>
                    <when test="query.typeGroup == 'CONTENT'">
                        AND activity_type IN ('ARTICLE_PUBLISHED', 'ARTICLE_UPDATED', 'ARTICLE_DELETED', 'ARTICLE_STATUS_CHANGED')
                    </when>
                    <when test="query.typeGroup == 'SOCIAL'">
                        AND activity_type IN ('USER_FOLLOWED', 'USER_UNFOLLOWED', 'ARTICLE_LIKED', 'ARTICLE_COLLECTED', 'COMMENT_POSTED')
                    </when>
                    <when test="query.typeGroup == 'IMPORTANT'">
                        AND activity_type IN ('LOGIN', 'LOGIN_FAILED', 'PASSWORD_CHANGED', 'USER_CREATED', 'USER_UPDATED',
                                             'USER_DELETED', 'USER_STATUS_CHANGED', 'USER_ROLE_ASSIGNED', 'USER_ROLE_REMOVED',
                                             'ROLE_CREATED', 'ROLE_UPDATED', 'ROLE_DELETED', 'PERMISSION_ASSIGNED',
                                             'PERMISSION_REMOVED', 'ARTICLE_DELETED', 'SYSTEM_CONFIG_CHANGED',
                                             'DATA_EXPORT', 'DATA_IMPORT', 'SECURITY_VIOLATION', 'FILE_DELETED')
                    </when>
                </choose>
            </if>
            <if test="query.result != null and query.result != ''">
                AND result = #{query.result}
            </if>
            <if test="query.ipAddress != null and query.ipAddress != ''">
                AND ip_address LIKE CONCAT('%', #{query.ipAddress}, '%')
            </if>
            <if test="query.startTime != null">
                AND create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND create_time &lt;= #{query.endTime}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND activity_description LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.targetType != null and query.targetType != ''">
                AND target_type = #{query.targetType}
            </if>
            <if test="query.targetId != null">
                AND target_id = #{query.targetId}
            </if>
        </where>
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                <choose>
                    <when test="query.orderBy == 'create_time' or query.orderBy == 'activityTime'">
                        ORDER BY create_time
                    </when>
                    <when test="query.orderBy == 'activity_type' or query.orderBy == 'activityType'">
                        ORDER BY activity_type
                    </when>
                    <when test="query.orderBy == 'result'">
                        ORDER BY result
                    </when>
                    <when test="query.orderBy == 'ip_address' or query.orderBy == 'ipAddress'">
                        ORDER BY ip_address
                    </when>
                    <otherwise>
                        ORDER BY create_time
                    </otherwise>
                </choose>
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    <choose>
                        <when test="query.orderDirection == 'asc' or query.orderDirection == 'ASC'">
                            ASC
                        </when>
                        <otherwise>
                            DESC
                        </otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 获取用户最近活动记录 -->
    <select id="selectRecentActivities" resultMap="ActivityRecordMap">
        SELECT
            activity_type,
            activity_description,
            create_time,
            ip_address,
            user_agent,
            result
        FROM user_activity
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户互动行为统计 -->
    <select id="selectUserInteractionInfo" resultMap="InteractionInfoMap">
        SELECT 
            MAX(CASE WHEN activity_type = 'LOGIN' THEN create_time END) as last_login_time,
            MAX(create_time) as last_active_time,
            COUNT(DISTINCT DATE(create_time)) as login_days,
            0 as avg_daily_active_time,
            COUNT(CASE WHEN activity_type = 'LOGIN' THEN 1 END) as total_sessions
        FROM user_activity 
        WHERE user_id = #{userId}
        AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    </select>

    <!-- 获取用户安全信息 -->
    <select id="selectUserSecurityInfo" resultMap="SecurityInfoMap">
        SELECT 
            (SELECT ip_address FROM user_activity 
             WHERE user_id = #{userId} AND activity_type = 'LOGIN' 
             ORDER BY create_time DESC LIMIT 1) as last_login_ip,
            '' as last_login_location,
            '' as last_login_device,
            (SELECT COUNT(*) FROM user_activity 
             WHERE user_id = #{userId} AND activity_type = 'LOGIN' 
             AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as suspicious_login_count,
            0 as is_email_verified,
            0 as is_phone_verified,
            NULL as password_last_changed
    </select>

    <!-- 获取用户最近登录IP列表 -->
    <select id="selectRecentLoginIps" resultType="String">
        SELECT DISTINCT ip_address
        FROM user_activity 
        WHERE user_id = #{userId} 
        AND activity_type = 'LOGIN'
        AND ip_address IS NOT NULL
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户登录天数 -->
    <select id="countLoginDays" resultType="Integer">
        SELECT COUNT(DISTINCT DATE(create_time))
        FROM user_activity 
        WHERE user_id = #{userId} 
        AND activity_type = 'LOGIN'
    </select>

    <!-- 统计用户总会话数 -->
    <select id="countTotalSessions" resultType="Integer">
        SELECT COUNT(*)
        FROM user_activity 
        WHERE user_id = #{userId} 
        AND activity_type = 'LOGIN'
    </select>

    <!-- 统计可疑登录次数 -->
    <select id="countSuspiciousLogins" resultType="Integer">
        SELECT COUNT(DISTINCT ip_address)
        FROM user_activity 
        WHERE user_id = #{userId} 
        AND activity_type = 'LOGIN'
        AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        HAVING COUNT(*) > 10
    </select>

    <!-- 记录用户活动 -->
    <insert id="recordActivity">
        INSERT INTO user_activity (
            user_id, 
            activity_type, 
            activity_description, 
            target_id, 
            target_type, 
            ip_address, 
            user_agent, 
            result, 
            create_time
        ) VALUES (
            #{userId}, 
            #{activityType}, 
            #{activityDescription}, 
            #{targetId}, 
            #{targetType}, 
            #{ipAddress}, 
            #{userAgent}, 
            #{result}, 
            #{createTime}
        )
    </insert>

    <!-- 批量删除过期活动记录 -->
    <delete id="deleteExpiredActivities">
        DELETE FROM user_activity 
        WHERE create_time &lt; #{beforeTime}
    </delete>

</mapper>

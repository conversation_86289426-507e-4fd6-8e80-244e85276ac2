<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.cmrpersonalblog.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.blog.cmrpersonalblog.entity.SysUser">
        <id column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="password" property="password" />
        <result column="phonenumber" property="phonenumber" />
        <result column="email" property="email" />
        <result column="avatar" property="avatar" />
        <result column="sex" property="sex" />
        <result column="status" property="status" />
        <result column="login_ip" property="loginIp" />
        <result column="login_date" property="loginDate" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据用户名查询用户信息 -->
    <select id="selectUserByUserName" parameterType="String" resultMap="BaseResultMap">
        SELECT user_id, user_name, nick_name, password, phonenumber, email, avatar, sex, status,
               login_ip, login_date, create_by, create_time, update_by, update_time
        FROM sys_user
        WHERE user_name = #{userName} AND status = 1
    </select>

    <!-- 根据用户ID查询用户权限 -->
    <select id="selectPermissionsByUserId" parameterType="Long" resultType="String">
        SELECT DISTINCT sp.perm_key
        FROM sys_permission sp
        LEFT JOIN sys_role_permission srp ON sp.id = srp.perm_id
        LEFT JOIN sys_user_role sur ON srp.role_id = sur.role_id
        WHERE sur.user_id = #{userId}
    </select>

    <!-- 根据用户ID查询用户角色 -->
    <select id="selectRolesByUserId" parameterType="Long" resultType="String">
        SELECT DISTINCT sr.role_key
        FROM sys_role sr
        LEFT JOIN sys_user_role sur ON sr.id = sur.role_id
        WHERE sur.user_id = #{userId} AND sr.status = '0'
    </select>

    <!-- 更新用户登录信息 -->
    <update id="updateUserLoginInfo" parameterType="com.blog.cmrpersonalblog.entity.SysUser">
        UPDATE sys_user
        SET login_ip = #{loginIp}, login_date = #{loginDate}
        WHERE user_id = #{userId}
    </update>

    <!-- ==================== 用户管理功能 ==================== -->

    <!-- 用户角色映射结果 -->
    <resultMap id="UserWithRolesMap" type="com.blog.cmrpersonalblog.entity.SysUser" extends="BaseResultMap">
        <collection property="roles" ofType="com.blog.cmrpersonalblog.entity.SysRole">
            <id column="role_id" property="id" />
            <result column="role_key" property="roleKey" />
            <result column="role_name" property="roleName" />
            <result column="role_description" property="description" />
            <result column="role_status" property="status" />
        </collection>
    </resultMap>

    <!-- 分页查询用户列表（带角色信息） -->
    <select id="selectUserPageWithRoles" resultMap="UserWithRolesMap">
        SELECT u.user_id, u.user_name, u.nick_name, u.password, u.phonenumber, u.email, u.avatar, u.sex, u.status,
               u.login_ip, u.login_date, u.create_by, u.create_time, u.update_by, u.update_time,
               r.id as role_id, r.role_key, r.role_name, r.description as role_description, r.status as role_status
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id AND r.status = '0'
        <where>
            <if test="query.userName != null and query.userName != ''">
                AND u.user_name LIKE CONCAT('%', #{query.userName}, '%')
            </if>
            <if test="query.nickName != null and query.nickName != ''">
                AND u.nick_name LIKE CONCAT('%', #{query.nickName}, '%')
            </if>
            <if test="query.email != null and query.email != ''">
                AND u.email LIKE CONCAT('%', #{query.email}, '%')
            </if>
            <if test="query.phonenumber != null and query.phonenumber != ''">
                AND u.phonenumber LIKE CONCAT('%', #{query.phonenumber}, '%')
            </if>
            <if test="query.status != null">
                AND u.status = #{query.status}
            </if>
            <if test="query.sex != null and query.sex != ''">
                AND u.sex = #{query.sex}
            </if>
            <if test="query.roleId != null">
                AND EXISTS (
                    SELECT 1 FROM sys_user_role ur2
                    WHERE ur2.user_id = u.user_id AND ur2.role_id = #{query.roleId}
                )
            </if>
        </where>
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY u.${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY u.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据用户ID查询用户详情（包含角色信息） -->
    <select id="selectUserDetailWithRoles" parameterType="Long" resultMap="UserWithRolesMap">
        SELECT u.user_id, u.user_name, u.nick_name, u.password, u.phonenumber, u.email, u.avatar, u.sex, u.status,
               u.login_ip, u.login_date, u.create_by, u.create_time, u.update_by, u.update_time,
               r.id as role_id, r.role_key, r.role_name, r.description as role_description, r.status as role_status
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id AND r.status = '0'
        WHERE u.user_id = #{userId}
        ORDER BY r.id
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="countByUserName" resultType="int">
        SELECT COUNT(1) FROM sys_user
        WHERE user_name = #{userName}
        <if test="excludeUserId != null">
            AND user_id != #{excludeUserId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="countByEmail" resultType="int">
        SELECT COUNT(1) FROM sys_user
        WHERE email = #{email}
        <if test="excludeUserId != null">
            AND user_id != #{excludeUserId}
        </if>
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="countByPhonenumber" resultType="int">
        SELECT COUNT(1) FROM sys_user
        WHERE phonenumber = #{phonenumber}
        <if test="excludeUserId != null">
            AND user_id != #{excludeUserId}
        </if>
    </select>

    <!-- 批量删除用户 -->
    <delete id="deleteBatchByIds">
        DELETE FROM sys_user WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 更新用户状态 -->
    <update id="updateUserStatus">
        UPDATE sys_user
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE user_id = #{userId}
    </update>

</mapper>

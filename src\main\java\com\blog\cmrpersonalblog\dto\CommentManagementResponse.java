package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论管理响应DTO
 */
@Data
public class CommentManagementResponse {

    /**
     * 评论ID
     */
    private Long id;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 文章标题
     */
    private String articleTitle;

    /**
     * 评论者ID
     */
    private Long userId;

    /**
     * 评论者用户名
     */
    private String userName;

    /**
     * 评论者昵称
     */
    private String userNickName;

    /**
     * 评论者头像
     */
    private String userAvatar;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 原始内容（未过滤敏感词）
     */
    private String originalContent;

    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 根评论ID
     */
    private Long rootId;

    /**
     * 评论层级
     */
    private Integer level;

    /**
     * 评论路径（用于树形结构）
     */
    private String path;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 状态 0-待审核 1-已通过 2-已拒绝 3-已删除
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 是否敏感评论 0-否 1-是
     */
    private Integer isSensitive;

    /**
     * 敏感词类型
     */
    private String sensitiveType;

    /**
     * 敏感词列表（JSON格式）
     */
    private String sensitiveWords;

    /**
     * 敏感度评分（0-100）
     */
    private Integer sensitiveScore;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 地理位置
     */
    private String location;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人用户名
     */
    private String auditUserName;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 子评论列表（树形结构）
     */
    private List<CommentManagementResponse> children;

    /**
     * 父评论信息（用于显示回复上下文）
     */
    private CommentManagementResponse parent;

    /**
     * 获取状态文本描述
     */
    public String getStatusText() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "待审核";
            case 1:
                return "已通过";
            case 2:
                return "已拒绝";
            case 3:
                return "已删除";
            default:
                return "未知";
        }
    }

    /**
     * 是否为顶级评论
     */
    public boolean isTopLevel() {
        return parentId == null || parentId == 0;
    }

    /**
     * 是否有子评论
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 获取敏感词类型描述
     */
    public String getSensitiveTypeText() {
        if (sensitiveType == null) {
            return "";
        }
        switch (sensitiveType) {
            case "profanity":
                return "脏话";
            case "politics":
                return "政治敏感";
            case "advertisement":
                return "广告";
            case "spam":
                return "垃圾信息";
            case "violence":
                return "暴力内容";
            case "illegal":
                return "违法内容";
            default:
                return sensitiveType;
        }
    }

    /**
     * 是否需要人工审核
     */
    public boolean needsManualReview() {
        return isSensitive != null && isSensitive == 1 && status == 0;
    }

    /**
     * 获取评论层级显示前缀
     */
    public String getLevelPrefix() {
        if (level == null || level <= 1) {
            return "";
        }
        StringBuilder prefix = new StringBuilder();
        for (int i = 1; i < level; i++) {
            prefix.append("  ");
        }
        prefix.append("└─ ");
        return prefix.toString();
    }
}

# 用户详情功能 API 文档

## 概述

用户详情功能是仿稀土掘金技术内容分享平台的核心功能之一，提供用户档案查看、发布记录管理、互动数据展示等功能。

## 接口列表

### 1. 获取用户详情信息

**接口地址**: `GET /user/{userId}/profile`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "userId": 1,
        "userName": "admin",
        "nickName": "管理员",
        "email": "<EMAIL>",
        "avatar": "http://example.com/avatar.jpg",
        "sex": "0",
        "sexName": "男",
        "createTime": "2024-01-01T10:00:00",
        "stats": {
            "articleCount": 10,
            "totalViewCount": 5000,
            "totalLikeCount": 200,
            "totalCommentCount": 50,
            "totalCollectCount": 30,
            "followerCount": 100,
            "followingCount": 50
        },
        "recentArticles": [
            {
                "id": 1,
                "title": "Spring Boot 入门教程",
                "summary": "详细介绍Spring Boot的基础使用",
                "coverImage": "http://example.com/cover.jpg",
                "viewCount": 1000,
                "likeCount": 50,
                "commentCount": 10,
                "collectCount": 20,
                "publishTime": "2024-01-01T10:00:00",
                "categoryName": "后端",
                "tagList": ["Spring Boot", "Java"]
            }
        ],
        "isFollowed": false
    }
}
```

### 2. 获取当前用户详情信息

**接口地址**: `GET /user/profile`

**请求头**: `Authorization: Bearer {token}`

**响应数据**: 同上

### 3. 分页查询用户发布的文章

**接口地址**: `GET /user/{userId}/articles`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| status | Integer | 否 | 文章状态 0-草稿 1-已发布 2-已删除 |
| categoryId | Long | 否 | 分类ID |
| tagName | String | 否 | 标签名称 |
| keyword | String | 否 | 关键词搜索（标题、摘要） |
| orderBy | String | 否 | 排序字段，默认publish_time |
| orderDirection | String | 否 | 排序方向（asc/desc），默认desc |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "title": "Spring Boot 入门教程",
                "summary": "详细介绍Spring Boot的基础使用",
                "coverImage": "http://example.com/cover.jpg",
                "viewCount": 1000,
                "likeCount": 50,
                "commentCount": 10,
                "collectCount": 20,
                "publishTime": "2024-01-01T10:00:00",
                "categoryName": "后端",
                "tagList": ["Spring Boot", "Java"]
            }
        ],
        "total": 100,
        "current": 1,
        "size": 10
    }
}
```

### 4. 获取用户互动数据

**接口地址**: `GET /user/{userId}/interactions`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "likedArticles": [],
        "collectedArticles": [],
        "followingUsers": [
            {
                "userId": 2,
                "userName": "user2",
                "nickName": "用户2",
                "avatar": "http://example.com/avatar2.jpg",
                "articleCount": 5,
                "followerCount": 20,
                "followTime": "2024-01-01T10:00:00"
            }
        ],
        "followers": [
            {
                "userId": 3,
                "userName": "user3",
                "nickName": "用户3",
                "avatar": "http://example.com/avatar3.jpg",
                "articleCount": 8,
                "followerCount": 15,
                "followTime": "2024-01-01T10:00:00",
                "isFollowBack": true
            }
        ]
    }
}
```

### 5. 关注/取消关注用户

**接口地址**: `POST /user/{userId}/follow`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 被关注用户ID |
| isFollow | Boolean | 是 | 是否关注 true-关注 false-取消关注 |

**响应数据**:
```json
{
    "code": 200,
    "message": "关注成功",
    "data": null
}
```

## 数据库表结构

### 1. 文章表 (article)
- 存储用户发布的文章信息
- 包含标题、内容、统计数据等

### 2. 用户统计表 (user_stats)
- 存储用户的统计数据
- 包含文章数、浏览量、点赞数、关注数等

### 3. 用户关注表 (user_follow)
- 存储用户之间的关注关系
- 支持关注和取消关注操作

### 4. 文章分类表 (article_category)
- 存储文章分类信息
- 支持按分类筛选文章

### 5. 文章标签表 (article_tag)
- 存储文章标签信息
- 支持按标签筛选文章

## 功能特性

### 1. 用户档案
- ✅ **基本信息展示**：用户名、昵称、头像、邮箱等
- ✅ **统计数据展示**：文章数、浏览量、点赞数、关注数等
- ✅ **最近文章展示**：显示用户最近发布的文章

### 2. 发布记录
- ✅ **分页查询**：支持分页查询用户发布的文章
- ✅ **多维筛选**：支持按状态、分类、标签、关键词筛选
- ✅ **排序功能**：支持按发布时间、浏览量、点赞数等排序

### 3. 互动数据
- ✅ **关注列表**：显示用户关注的其他用户
- ✅ **粉丝列表**：显示关注该用户的其他用户
- 🚧 **点赞记录**：显示用户点赞的文章（待实现）
- 🚧 **收藏记录**：显示用户收藏的文章（待实现）

### 4. 关注系统
- ✅ **关注用户**：支持关注其他用户
- ✅ **取消关注**：支持取消关注
- ✅ **关注状态**：显示当前用户是否关注了目标用户
- ✅ **统计更新**：关注操作会自动更新统计数据

## 后台管理用户详情接口

### 1. 获取后台管理用户详情信息

**接口地址**: `GET /admin/user-profile/{userId}`

**权限要求**: 管理员权限 (`admin` 角色)

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**响应数据**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "basicInfo": {
            "userId": 1,
            "userName": "admin",
            "nickName": "管理员",
            "email": "<EMAIL>",
            "phonenumber": "13800138000",
            "avatar": "http://example.com/avatar.jpg",
            "sex": "0",
            "sexName": "男",
            "status": 1,
            "statusName": "启用",
            "createTime": "2024-01-01T10:00:00",
            "updateTime": "2024-01-01T10:00:00",
            "createBy": "system",
            "updateBy": "admin",
            "roleNames": ["管理员"]
        },
        "stats": {
            "articleCount": 10,
            "draftCount": 2,
            "deletedCount": 1,
            "totalViewCount": 5000,
            "totalLikeCount": 200,
            "totalCommentCount": 50,
            "totalCollectCount": 30,
            "followerCount": 100,
            "followingCount": 50,
            "receivedLikeCount": 200,
            "givenLikeCount": 150,
            "commentGivenCount": 80,
            "commentReceivedCount": 50
        },
        "contentInfo": {
            "firstPublishTime": "2024-01-01T10:00:00",
            "lastPublishTime": "2024-01-15T10:00:00",
            "avgViewsPerArticle": 500.0,
            "avgLikesPerArticle": 20.0,
            "categoryStats": [
                {
                    "categoryName": "后端",
                    "articleCount": 5,
                    "totalViews": 2500,
                    "totalLikes": 100
                }
            ],
            "topTags": ["Spring Boot", "Java", "MySQL"],
            "publishFrequency": 7
        },
        "interactionInfo": {
            "lastLoginTime": "2024-01-15T10:00:00",
            "lastActiveTime": "2024-01-15T11:00:00",
            "loginDays": 30,
            "avgDailyActiveTime": 120.5,
            "totalSessions": 45,
            "activeTimeRanges": ["09:00-12:00", "14:00-18:00"]
        },
        "securityInfo": {
            "lastLoginIp": "*************",
            "lastLoginLocation": "北京市",
            "lastLoginDevice": "Chrome/Windows",
            "recentLoginIps": ["*************", "*************"],
            "suspiciousLoginCount": 0,
            "isEmailVerified": true,
            "isPhoneVerified": true,
            "passwordLastChanged": "2024-01-01T10:00:00"
        },
        "recentActivities": [
            {
                "activityType": "LOGIN",
                "activityDescription": "用户登录",
                "activityTime": "2024-01-15T10:00:00",
                "ipAddress": "*************",
                "userAgent": "Mozilla/5.0...",
                "result": "SUCCESS"
            }
        ]
    }
}
```

### 2. 获取用户内容发布统计

**接口地址**: `GET /admin/user-profile/{userId}/content-stats`

**权限要求**: 管理员权限

### 3. 获取用户活动统计

**接口地址**: `GET /admin/user-profile/{userId}/activity-stats`

**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| days | Integer | 否 | 统计天数，默认30天 |

### 4. 重新计算用户统计数据

**接口地址**: `POST /admin/user-profile/{userId}/recalculate-stats`

**权限要求**: 管理员权限

### 5. 清理过期活动记录

**接口地址**: `POST /admin/user-profile/clean-expired-activities`

**权限要求**: 管理员权限

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| days | Integer | 否 | 保留天数，默认90天 |

## 注意事项

1. **权限控制**：部分接口需要登录后才能访问，后台管理接口需要管理员权限
2. **数据一致性**：统计数据通过定时任务或触发器保持一致
3. **性能优化**：大数据量查询建议添加索引
4. **扩展性**：预留了点赞和收藏功能的接口，后续可扩展实现
5. **活动记录**：系统会自动记录用户的各种操作活动，用于行为分析
6. **数据安全**：后台管理接口会记录管理员的操作日志

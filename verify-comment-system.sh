#!/bin/bash

# 评论管理系统验证脚本
echo "🚀 评论管理系统功能验证"
echo "================================"

# 配置
BASE_URL="http://localhost:8081"
ADMIN_TOKEN="your-admin-token-here"  # 需要替换为实际的管理员token

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 通用请求函数
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local content_type=${4:-"application/json"}
    
    echo -e "${BLUE}📡 ${method} ${endpoint}${NC}"
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\n状态码: %{http_code}\n" \
             -H "Authorization: Bearer ${ADMIN_TOKEN}" \
             "${BASE_URL}${endpoint}"
    elif [ "$method" = "POST" ]; then
        if [ "$content_type" = "text/plain" ]; then
            curl -s -w "\n状态码: %{http_code}\n" \
                 -X POST \
                 -H "Authorization: Bearer ${ADMIN_TOKEN}" \
                 -H "Content-Type: text/plain" \
                 -d "$data" \
                 "${BASE_URL}${endpoint}"
        else
            curl -s -w "\n状态码: %{http_code}\n" \
                 -X POST \
                 -H "Authorization: Bearer ${ADMIN_TOKEN}" \
                 -H "Content-Type: application/json" \
                 -d "$data" \
                 "${BASE_URL}${endpoint}"
        fi
    fi
    echo ""
}

# 检查服务是否启动
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
if curl -s "${BASE_URL}/actuator/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务正在运行${NC}"
else
    echo -e "${RED}❌ 服务未启动或无法访问${NC}"
    echo "请确保："
    echo "1. Spring Boot 应用已启动"
    echo "2. 端口 8081 可访问"
    echo "3. 数据库连接正常"
    exit 1
fi

echo ""

# 1. 测试敏感词检测
echo -e "${YELLOW}🔍 测试敏感词检测功能${NC}"
echo "----------------------------------------"

echo "测试正常文本："
make_request "POST" "/admin/comments/check-sensitive" "这是一条正常的评论内容" "text/plain"

echo "测试敏感文本："
make_request "POST" "/admin/comments/check-sensitive" "这个作者真是个傻逼，写的都是垃圾" "text/plain"

echo "测试政治敏感词："
make_request "POST" "/admin/comments/check-sensitive" "六四事件是一个敏感话题" "text/plain"

echo "测试广告内容："
make_request "POST" "/admin/comments/check-sensitive" "需要代开发票的朋友联系我" "text/plain"

# 2. 测试评论管理功能
echo -e "${YELLOW}📝 测试评论管理功能${NC}"
echo "----------------------------------------"

echo "获取评论统计信息："
make_request "GET" "/admin/comments/statistics"

echo "获取待审核评论数量："
make_request "GET" "/admin/comments/pending-audit-count"

echo "获取敏感评论数量："
make_request "GET" "/admin/comments/sensitive-count"

echo "获取评论状态分布："
make_request "GET" "/admin/comments/status-distribution"

echo "获取敏感词类型分布："
make_request "GET" "/admin/comments/sensitive-type-distribution"

echo "查询评论列表（前5条）："
make_request "GET" "/admin/comments/list?current=1&size=5"

echo "查询待审核评论："
make_request "GET" "/admin/comments/list?current=1&size=3&status=0"

echo "查询敏感评论："
make_request "GET" "/admin/comments/list?current=1&size=3&isSensitive=1"

echo "获取最新评论："
make_request "GET" "/admin/comments/latest?limit=3"

echo "搜索评论："
make_request "GET" "/admin/comments/search?keyword=测试&current=1&size=3"

# 3. 测试用户禁言功能
echo -e "${YELLOW}🚫 测试用户禁言功能${NC}"
echo "----------------------------------------"

echo "获取禁言统计信息："
make_request "GET" "/admin/user-ban/statistics"

echo "获取禁言类型分布："
make_request "GET" "/admin/user-ban/type-distribution"

echo "检查用户禁言状态（用户ID: 5）："
make_request "GET" "/admin/user-ban/status/5"

echo "获取用户禁言历史（用户ID: 5）："
make_request "GET" "/admin/user-ban/history/5"

echo "获取最近禁言记录："
make_request "GET" "/admin/user-ban/recent?limit=3"

echo "查询禁言记录："
make_request "GET" "/admin/user-ban/records?current=1&size=5"

echo "获取即将到期的禁言："
make_request "GET" "/admin/user-ban/expiring?beforeMinutes=1440"

# 4. 测试批量操作（模拟）
echo -e "${YELLOW}📦 测试批量操作功能${NC}"
echo "----------------------------------------"

echo "验证禁言请求："
ban_request='{
    "userId": 6,
    "banType": "COMMENT",
    "banDuration": 60,
    "banReason": "测试禁言功能",
    "sendNotification": false
}'
make_request "POST" "/admin/user-ban/validate" "$ban_request"

# 5. 测试系统健康状态
echo -e "${YELLOW}📊 系统健康检查${NC}"
echo "----------------------------------------"

echo "检查应用健康状态："
curl -s "${BASE_URL}/actuator/health" | python3 -m json.tool 2>/dev/null || echo "健康检查端点可能未启用"

echo ""

# 总结
echo -e "${GREEN}🎉 评论管理系统验证完成！${NC}"
echo "================================"
echo ""
echo -e "${BLUE}📋 验证项目总结：${NC}"
echo "✅ 敏感词检测功能"
echo "✅ 评论管理功能"
echo "✅ 用户禁言功能"
echo "✅ 统计分析功能"
echo "✅ 批量操作功能"
echo ""
echo -e "${YELLOW}💡 使用提示：${NC}"
echo "1. 请替换脚本中的 ADMIN_TOKEN 为实际的管理员令牌"
echo "2. 确保数据库中有测试数据（运行 test-data-comments.sql）"
echo "3. 可以访问演示页面：${BASE_URL}/comment-management-demo.html"
echo "4. 查看API文档：COMMENT-MANAGEMENT-API.md"
echo "5. 查看使用指南：COMMENT-MANAGEMENT-README.md"
echo ""
echo -e "${GREEN}🚀 评论管理系统已准备就绪！${NC}"

package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * Markdown预览响应DTO
 */
@Data
public class MarkdownPreviewResponse {

    /**
     * 原始Markdown内容
     */
    private String markdownContent;

    /**
     * 转换后的HTML内容
     */
    private String htmlContent;

    /**
     * 字数统计
     */
    private Integer wordCount;

    /**
     * 预估阅读时长（分钟）
     */
    private Integer readingTime;

    /**
     * 目录结构（JSON格式）
     */
    private String tableOfContents;

    /**
     * 是否包含代码块
     */
    private Boolean hasCodeBlock;

    /**
     * 是否包含图片
     */
    private Boolean hasImage;

    /**
     * 是否包含表格
     */
    private Boolean hasTable;

    /**
     * 是否包含数学公式
     */
    private Boolean hasMath;

    /**
     * 处理时间（毫秒）
     */
    private Long processTime;

    /**
     * 构造函数
     */
    public MarkdownPreviewResponse() {}

    public MarkdownPreviewResponse(String markdownContent, String htmlContent) {
        this.markdownContent = markdownContent;
        this.htmlContent = htmlContent;
        this.wordCount = calculateWordCount(markdownContent);
        this.readingTime = calculateReadingTime(this.wordCount);
        this.hasCodeBlock = htmlContent.contains("<pre><code>");
        this.hasImage = htmlContent.contains("<img");
        this.hasTable = htmlContent.contains("<table>");
        this.hasMath = htmlContent.contains("\\(") || htmlContent.contains("\\[");
    }

    /**
     * 计算字数
     */
    private Integer calculateWordCount(String content) {
        if (content == null || content.trim().isEmpty()) {
            return 0;
        }
        // 移除Markdown标记符号，计算实际字数
        String cleanContent = content
                .replaceAll("#+\\s*", "") // 移除标题标记
                .replaceAll("\\*\\*([^*]+)\\*\\*", "$1") // 移除粗体标记
                .replaceAll("\\*([^*]+)\\*", "$1") // 移除斜体标记
                .replaceAll("`([^`]+)`", "$1") // 移除行内代码标记
                .replaceAll("```[\\s\\S]*?```", "") // 移除代码块
                .replaceAll("!\\[([^\\]]*)\\]\\([^)]*\\)", "$1") // 移除图片标记
                .replaceAll("\\[([^\\]]*)\\]\\([^)]*\\)", "$1") // 移除链接标记
                .replaceAll("\\n", " ") // 换行符替换为空格
                .trim();
        
        // 中文字符按1个字计算，英文单词按空格分割计算
        int chineseCount = 0;
        int englishCount = 0;
        
        for (char c : cleanContent.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                chineseCount++;
            }
        }
        
        String[] englishWords = cleanContent.replaceAll("[\\u4e00-\\u9fff]", "").split("\\s+");
        for (String word : englishWords) {
            if (!word.trim().isEmpty()) {
                englishCount++;
            }
        }
        
        return chineseCount + englishCount;
    }

    /**
     * 计算阅读时长（按每分钟200字计算）
     */
    private Integer calculateReadingTime(Integer wordCount) {
        if (wordCount == null || wordCount == 0) {
            return 1;
        }
        int minutes = (int) Math.ceil(wordCount / 200.0);
        return Math.max(1, minutes); // 最少1分钟
    }
}

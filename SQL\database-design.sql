-- 技术内容分享平台数据库设计
-- 仿稀土掘金功能

-- 1. 文章表
CREATE TABLE `article` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '文章ID',
    `title` VARCHAR(200) NOT NULL COMMENT '文章标题',
    `summary` VARCHAR(500) COMMENT '文章摘要',
    `content` LONGTEXT NOT NULL COMMENT '文章内容',
    `cover_image` VARCHAR(500) COMMENT '封面图片URL',
    `author_id` BIGINT NOT NULL COMMENT '作者ID',
    `category_id` BIGINT COMMENT '分类ID',
    `tags` VARCHAR(500) COMMENT '标签，逗号分隔',
    `view_count` INT DEFAULT 0 COMMENT '浏览量',
    `like_count` INT DEFAULT 0 COMMENT '点赞数',
    `comment_count` INT DEFAULT 0 COMMENT '评论数',
    `collect_count` INT DEFAULT 0 COMMENT '收藏数',
    `share_count` INT DEFAULT 0 COMMENT '分享数',
    `is_original` TINYINT DEFAULT 1 COMMENT '是否原创 0-转载 1-原创',
    `is_top` TINYINT DEFAULT 0 COMMENT '是否置顶 0-否 1-是',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-草稿 1-已发布 2-已删除',
    `publish_time` DATETIME COMMENT '发布时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_author_id` (`author_id`),
    INDEX `idx_category_id` (`category_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 2. 文章分类表
CREATE TABLE `article_category` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `description` VARCHAR(200) COMMENT '分类描述',
    `icon` VARCHAR(200) COMMENT '分类图标',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- 3. 文章标签表
CREATE TABLE `article_tag` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `color` VARCHAR(20) COMMENT '标签颜色',
    `use_count` INT DEFAULT 0 COMMENT '使用次数',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签表';

-- 4. 评论表
CREATE TABLE `comment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '评论ID',
    `article_id` BIGINT NOT NULL COMMENT '文章ID',
    `user_id` BIGINT NOT NULL COMMENT '评论用户ID',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父评论ID，0表示顶级评论',
    `reply_to_user_id` BIGINT COMMENT '回复的用户ID',
    `content` TEXT NOT NULL COMMENT '评论内容',
    `like_count` INT DEFAULT 0 COMMENT '点赞数',
    `reply_count` INT DEFAULT 0 COMMENT '回复数',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-已删除 1-正常',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_article_id` (`article_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 5. 用户点赞表
CREATE TABLE `user_like` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `target_id` BIGINT NOT NULL COMMENT '目标ID（文章ID或评论ID）',
    `target_type` TINYINT NOT NULL COMMENT '目标类型 1-文章 2-评论',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-取消点赞 1-点赞',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_target` (`user_id`, `target_id`, `target_type`),
    INDEX `idx_target` (`target_id`, `target_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';

-- 6. 用户收藏表
CREATE TABLE `user_collect` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `article_id` BIGINT NOT NULL COMMENT '文章ID',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-取消收藏 1-收藏',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_article` (`user_id`, `article_id`),
    INDEX `idx_article_id` (`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 7. 用户关注表
CREATE TABLE `user_follow` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关注ID',
    `follower_id` BIGINT NOT NULL COMMENT '关注者ID',
    `following_id` BIGINT NOT NULL COMMENT '被关注者ID',
    `status` TINYINT DEFAULT 1 COMMENT '状态 0-取消关注 1-关注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_follower_following` (`follower_id`, `following_id`),
    INDEX `idx_following_id` (`following_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 8. 用户统计表
CREATE TABLE `user_stats` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `article_count` INT DEFAULT 0 COMMENT '发布文章数',
    `total_view_count` BIGINT DEFAULT 0 COMMENT '文章总浏览量',
    `total_like_count` INT DEFAULT 0 COMMENT '文章总点赞数',
    `total_comment_count` INT DEFAULT 0 COMMENT '文章总评论数',
    `total_collect_count` INT DEFAULT 0 COMMENT '文章总收藏数',
    `follower_count` INT DEFAULT 0 COMMENT '粉丝数',
    `following_count` INT DEFAULT 0 COMMENT '关注数',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户统计表';

-- 插入默认分类数据
INSERT INTO `article_category` (`name`, `description`, `icon`, `sort_order`) VALUES
('前端', '前端开发技术分享', 'frontend-icon', 1),
('后端', '后端开发技术分享', 'backend-icon', 2),
('移动开发', '移动端开发技术', 'mobile-icon', 3),
('人工智能', 'AI与机器学习', 'ai-icon', 4),
('开发工具', '开发工具与效率', 'tools-icon', 5),
('代码人生', '程序员生活与思考', 'life-icon', 6);

-- 9. 用户活动记录表
CREATE TABLE `user_activity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '活动记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `activity_type` VARCHAR(50) NOT NULL COMMENT '活动类型',
    `activity_description` VARCHAR(500) COMMENT '活动描述',
    `target_id` BIGINT COMMENT '目标ID（文章ID、用户ID等）',
    `target_type` VARCHAR(50) COMMENT '目标类型（article、user、comment等）',
    `ip_address` VARCHAR(128) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `result` VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '操作结果（SUCCESS、FAILED）',
    `extra_data` JSON COMMENT '额外数据（JSON格式）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_activity_type` (`activity_type`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_user_activity` (`user_id`, `activity_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动记录表';

-- 插入默认标签数据
INSERT INTO `article_tag` (`name`, `color`) VALUES
('JavaScript', '#f7df1e'),
('Vue.js', '#4fc08d'),
('React', '#61dafb'),
('Java', '#ed8b00'),
('Spring Boot', '#6db33f'),
('Python', '#3776ab'),
('MySQL', '#4479a1'),
('Redis', '#dc382d'),
('Docker', '#2496ed'),
('Kubernetes', '#326ce5');

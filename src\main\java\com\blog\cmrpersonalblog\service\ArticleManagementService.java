package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.cmrpersonalblog.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 文章管理服务接口
 */
public interface ArticleManagementService {

    /**
     * 分页查询文章列表
     * @param queryRequest 查询条件
     * @return 文章分页列表
     */
    IPage<ArticleManagementResponse> getArticleList(ArticleQueryRequest queryRequest);

    /**
     * 获取文章详情
     * @param articleId 文章ID
     * @return 文章详情
     */
    ArticleDetailResponse getArticleDetail(Long articleId);

    /**
     * 审核文章
     * @param auditRequest 审核请求
     * @param auditorId 审核人ID
     * @return 审核结果
     */
    boolean auditArticle(ArticleAuditRequest auditRequest, Long auditorId);

    /**
     * 批量操作文章
     * @param batchRequest 批量操作请求
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Map<String, Object> batchOperateArticles(ArticleBatchOperationRequest batchRequest, Long operatorId);

    /**
     * 发布文章
     * @param articleId 文章ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean publishArticle(Long articleId, Long operatorId);

    /**
     * 下架文章
     * @param articleId 文章ID
     * @param operatorId 操作人ID
     * @param reason 下架原因
     * @return 是否成功
     */
    boolean unpublishArticle(Long articleId, Long operatorId, String reason);

    /**
     * 置顶文章
     * @param articleId 文章ID
     * @param isTop 是否置顶
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean topArticle(Long articleId, boolean isTop, Long operatorId);

    /**
     * 删除文章
     * @param articleId 文章ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean deleteArticle(Long articleId, Long operatorId);

    /**
     * 恢复文章
     * @param articleId 文章ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean restoreArticle(Long articleId, Long operatorId);

    /**
     * 获取文章统计信息
     * @return 统计信息
     */
    Map<String, Object> getArticleStatistics();

    /**
     * 获取待审核文章数量
     * @return 待审核数量
     */
    Long getPendingAuditCount();

    /**
     * 获取文章状态分布
     * @return 状态分布
     */
    Map<String, Long> getArticleStatusDistribution();

    /**
     * 获取热门文章列表
     * @param limit 数量限制
     * @return 热门文章列表
     */
    List<ArticleManagementResponse> getPopularArticles(Integer limit);

    /**
     * 获取最新文章列表
     * @param limit 数量限制
     * @return 最新文章列表
     */
    List<ArticleManagementResponse> getLatestArticles(Integer limit);

    /**
     * 搜索文章
     * @param keyword 关键词
     * @param current 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<ArticleManagementResponse> searchArticles(String keyword, Long current, Long size);

    /**
     * 获取用户文章列表
     * @param userId 用户ID
     * @param current 页码
     * @param size 每页大小
     * @return 用户文章列表
     */
    IPage<ArticleManagementResponse> getUserArticles(Long userId, Long current, Long size);

    /**
     * 获取分类文章列表
     * @param categoryId 分类ID
     * @param current 页码
     * @param size 每页大小
     * @return 分类文章列表
     */
    IPage<ArticleManagementResponse> getCategoryArticles(Long categoryId, Long current, Long size);

    /**
     * 更新文章统计数据
     * @param articleId 文章ID
     * @param field 字段名（view_count, like_count, comment_count等）
     * @param increment 增量
     * @return 是否成功
     */
    boolean updateArticleStats(Long articleId, String field, Integer increment);

    /**
     * 重新计算文章统计数据
     * @param articleId 文章ID
     * @return 是否成功
     */
    boolean recalculateArticleStats(Long articleId);
}

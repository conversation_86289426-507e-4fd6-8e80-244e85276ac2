package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 角色查询请求DTO
 */
@Data
public class RoleQueryRequest {

    /**
     * 角色标识（模糊查询）
     */
    private String roleKey;

    /**
     * 角色名称（模糊查询）
     */
    private String roleName;

    /**
     * 角色状态（0正常 1停用）
     */
    private String status;

    /**
     * 权限ID（精确查询）
     */
    private Long permissionId;

    /**
     * 页码（从1开始）
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;
}

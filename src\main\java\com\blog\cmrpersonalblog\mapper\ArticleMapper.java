package com.blog.cmrpersonalblog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blog.cmrpersonalblog.dto.ArticleQueryRequest;
import com.blog.cmrpersonalblog.dto.ArticleManagementResponse;
import com.blog.cmrpersonalblog.dto.ArticleDetailResponse;
import com.blog.cmrpersonalblog.dto.UserArticleQueryRequest;
import com.blog.cmrpersonalblog.dto.UserProfileResponse;
import com.blog.cmrpersonalblog.entity.Article;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 文章Mapper接口
 */
public interface ArticleMapper extends BaseMapper<Article> {

    /**
     * 分页查询用户文章列表
     * @param page 分页对象
     * @param query 查询条件
     * @return 文章列表
     */
    IPage<UserProfileResponse.ArticleInfo> selectUserArticlePage(Page<UserProfileResponse.ArticleInfo> page, 
                                                                  @Param("query") UserArticleQueryRequest query);

    /**
     * 获取用户最近发布的文章
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 文章列表
     */
    List<UserProfileResponse.ArticleInfo> selectRecentArticlesByUserId(@Param("userId") Long userId, 
                                                                        @Param("limit") Integer limit);

    /**
     * 统计用户文章数据
     * @param userId 用户ID
     * @return 统计数据
     */
    UserProfileResponse.UserStatsInfo selectUserArticleStats(@Param("userId") Long userId);

    /**
     * 更新文章统计数据
     * @param articleId 文章ID
     * @param field 字段名
     * @param increment 增量
     */
    void updateArticleStats(@Param("articleId") Long articleId,
                           @Param("field") String field,
                           @Param("increment") Integer increment);

    // ==================== 文章管理相关方法 ====================

    /**
     * 分页查询文章管理列表
     * @param page 分页对象
     * @param query 查询条件
     * @return 文章管理列表
     */
    IPage<ArticleManagementResponse> selectArticleManagementPage(Page<ArticleManagementResponse> page,
                                                                 @Param("query") ArticleQueryRequest query);

    /**
     * 获取文章详情（包含作者和分类信息）
     * @param articleId 文章ID
     * @return 文章详情
     */
    ArticleDetailResponse selectArticleDetailById(@Param("articleId") Long articleId);

    /**
     * 批量更新文章状态
     * @param articleIds 文章ID列表
     * @param status 目标状态
     * @param operatorId 操作人ID
     * @return 更新数量
     */
    int batchUpdateArticleStatus(@Param("articleIds") List<Long> articleIds,
                                @Param("status") Integer status,
                                @Param("operatorId") Long operatorId);

    /**
     * 批量更新文章置顶状态
     * @param articleIds 文章ID列表
     * @param isTop 是否置顶
     * @param operatorId 操作人ID
     * @return 更新数量
     */
    int batchUpdateArticleTop(@Param("articleIds") List<Long> articleIds,
                             @Param("isTop") Integer isTop,
                             @Param("operatorId") Long operatorId);

    /**
     * 批量删除文章
     * @param articleIds 文章ID列表
     * @param operatorId 操作人ID
     * @return 删除数量
     */
    int batchDeleteArticles(@Param("articleIds") List<Long> articleIds,
                           @Param("operatorId") Long operatorId);

    /**
     * 更新文章审核信息
     * @param articleId 文章ID
     * @param status 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @param auditReason 审核原因
     * @return 更新数量
     */
    int updateArticleAuditInfo(@Param("articleId") Long articleId,
                              @Param("status") Integer status,
                              @Param("auditUserId") Long auditUserId,
                              @Param("auditRemark") String auditRemark,
                              @Param("auditReason") String auditReason);

    /**
     * 获取文章统计信息
     * @return 统计信息
     */
    Map<String, Object> selectArticleStatistics();

    /**
     * 获取待审核文章数量
     * @return 待审核数量
     */
    Long selectPendingAuditCount();

    /**
     * 获取文章状态分布
     * @return 状态分布
     */
    List<Map<String, Object>> selectArticleStatusDistribution();

    /**
     * 获取热门文章列表
     * @param limit 数量限制
     * @return 热门文章列表
     */
    List<ArticleManagementResponse> selectPopularArticles(@Param("limit") Integer limit);

    /**
     * 获取最新文章列表
     * @param limit 数量限制
     * @return 最新文章列表
     */
    List<ArticleManagementResponse> selectLatestArticles(@Param("limit") Integer limit);

    /**
     * 搜索文章
     * @param page 分页对象
     * @param keyword 关键词
     * @return 搜索结果
     */
    IPage<ArticleManagementResponse> searchArticles(Page<ArticleManagementResponse> page,
                                                   @Param("keyword") String keyword);

    /**
     * 获取用户文章管理列表
     * @param page 分页对象
     * @param userId 用户ID
     * @return 用户文章列表
     */
    IPage<ArticleManagementResponse> selectUserArticleManagementPage(Page<ArticleManagementResponse> page,
                                                                     @Param("userId") Long userId);

    /**
     * 获取分类文章管理列表
     * @param page 分页对象
     * @param categoryId 分类ID
     * @return 分类文章列表
     */
    IPage<ArticleManagementResponse> selectCategoryArticleManagementPage(Page<ArticleManagementResponse> page,
                                                                         @Param("categoryId") Long categoryId);

    /**
     * 重新计算文章统计数据
     * @param articleId 文章ID
     * @return 更新数量
     */
    int recalculateArticleStats(@Param("articleId") Long articleId);
}

package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.cmrpersonalblog.dto.RoleCreateRequest;
import com.blog.cmrpersonalblog.dto.RoleQueryRequest;
import com.blog.cmrpersonalblog.dto.RoleResponse;
import com.blog.cmrpersonalblog.dto.RoleUpdateRequest;
import com.blog.cmrpersonalblog.entity.SysRole;
import com.blog.cmrpersonalblog.entity.SysPermission;

import java.util.List;

/**
 * 系统角色服务接口
 */
public interface SysRoleService extends IService<SysRole> {

    /**
     * 根据角色key查询角色信息
     * @param roleKey 角色标识
     * @return 角色信息
     */
    SysRole getRoleByRoleKey(String roleKey);

    /**
     * 根据角色ID查询角色拥有的权限
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> getPermissionsByRoleId(Long roleId);

    /**
     * 查询所有启用的角色
     * @return 角色列表
     */
    List<SysRole> getEnabledRoles();

    /**
     * 根据用户ID查询用户拥有的角色（带权限信息）
     * @param userId 用户ID
     * @return 角色列表（包含权限）
     */
    List<SysRole> getRolesByUserIdWithPermissions(Long userId);

    /**
     * 检查角色是否可以删除（是否被用户使用）
     * @param roleId 角色ID
     * @return 是否可以删除
     */
    boolean canDeleteRole(Long roleId);

    /**
     * 为角色分配权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean assignPermissions(Long roleId, List<Long> permissionIds);

    /**
     * 移除角色的所有权限
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean removeAllPermissions(Long roleId);

    /**
     * 创建角色
     * @param createRequest 创建请求
     * @return 是否成功
     */
    boolean createRole(RoleCreateRequest createRequest);

    /**
     * 更新角色
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateRole(RoleUpdateRequest updateRequest);

    /**
     * 删除角色
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long roleId);

    /**
     * 批量删除角色
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean deleteRoles(List<Long> roleIds);

    /**
     * 分页查询角色列表
     * @param queryRequest 查询请求
     * @return 分页结果
     */
    IPage<RoleResponse> getRoleList(RoleQueryRequest queryRequest);

    /**
     * 根据角色ID获取角色详情
     * @param roleId 角色ID
     * @return 角色详情
     */
    RoleResponse getRoleDetail(Long roleId);

    /**
     * 更新角色状态
     * @param roleId 角色ID
     * @param status 状态（0正常 1停用）
     * @param updateBy 操作人
     * @return 是否成功
     */
    boolean updateRoleStatus(Long roleId, String status, String updateBy);

    /**
     * 检查角色标识是否存在
     * @param roleKey 角色标识
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsRoleKey(String roleKey, Long excludeId);

    /**
     * 获取所有角色（用于下拉选择）
     * @return 角色列表
     */
    List<RoleResponse> getAllRolesForSelect();
}

package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 后台管理用户详情响应DTO
 */
@Data
public class AdminUserProfileResponse {

    /**
     * 用户基本信息
     */
    private UserBasicInfo basicInfo;

    /**
     * 用户统计数据
     */
    private UserStatsInfo stats;

    /**
     * 内容发布情况
     */
    private ContentInfo contentInfo;

    /**
     * 互动行为数据
     */
    private InteractionInfo interactionInfo;

    /**
     * 安全信息
     */
    private SecurityInfo securityInfo;

    /**
     * 最近活动记录
     */
    private List<ActivityRecord> recentActivities;

    /**
     * 用户基本信息
     */
    @Data
    public static class UserBasicInfo {
        private Long userId;
        private String userName;
        private String nickName;
        private String email;
        private String phonenumber;
        private String avatar;
        private String sex;
        private String sexName;
        private Integer status;
        private String statusName;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createBy;
        private String updateBy;
        private List<String> roleNames;
    }

    /**
     * 用户统计信息
     */
    @Data
    public static class UserStatsInfo {
        private Integer articleCount;           // 发布文章数
        private Integer draftCount;             // 草稿数
        private Integer deletedCount;           // 已删除文章数
        private Long totalViewCount;           // 文章总浏览量
        private Integer totalLikeCount;        // 文章总点赞数
        private Integer totalCommentCount;     // 文章总评论数
        private Integer totalCollectCount;     // 文章总收藏数
        private Integer followerCount;         // 粉丝数
        private Integer followingCount;        // 关注数
        private Integer receivedLikeCount;     // 收到的点赞数
        private Integer givenLikeCount;        // 给出的点赞数
        private Integer commentGivenCount;     // 发出的评论数
        private Integer commentReceivedCount;  // 收到的评论数
    }

    /**
     * 内容发布信息
     */
    @Data
    public static class ContentInfo {
        private LocalDateTime firstPublishTime;    // 首次发布时间
        private LocalDateTime lastPublishTime;     // 最后发布时间
        private Double avgViewsPerArticle;         // 平均每篇文章浏览量
        private Double avgLikesPerArticle;         // 平均每篇文章点赞数
        private List<CategoryStats> categoryStats; // 分类统计
        private List<String> topTags;              // 常用标签
        private Integer publishFrequency;          // 发布频率（天/篇）
    }

    /**
     * 互动行为信息
     */
    @Data
    public static class InteractionInfo {
        private LocalDateTime lastLoginTime;      // 最后登录时间
        private LocalDateTime lastActiveTime;     // 最后活跃时间
        private Integer loginDays;                 // 登录天数
        private Double avgDailyActiveTime;         // 平均每日活跃时间（分钟）
        private Integer totalSessions;            // 总会话数
        private List<String> activeTimeRanges;    // 活跃时间段
    }

    /**
     * 安全信息
     */
    @Data
    public static class SecurityInfo {
        private String lastLoginIp;               // 最后登录IP
        private String lastLoginLocation;         // 最后登录地点
        private String lastLoginDevice;           // 最后登录设备
        private List<String> recentLoginIps;      // 最近登录IP列表
        private Integer suspiciousLoginCount;     // 可疑登录次数
        private Boolean isEmailVerified;          // 邮箱是否验证
        private Boolean isPhoneVerified;          // 手机是否验证
        private LocalDateTime passwordLastChanged; // 密码最后修改时间
    }

    /**
     * 分类统计
     */
    @Data
    public static class CategoryStats {
        private String categoryName;
        private Integer articleCount;
        private Long totalViews;
        private Integer totalLikes;
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActivityRecord {
        private String activityType;              // 活动类型
        private String activityDescription;       // 活动描述
        private LocalDateTime activityTime;       // 活动时间
        private String ipAddress;                 // IP地址
        private String userAgent;                 // 用户代理
        private String result;                     // 操作结果
    }
}

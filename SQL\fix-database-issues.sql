-- 修复数据库问题的SQL脚本
-- 解决主键冲突和字段不匹配问题

-- 1. 检查当前数据库
SELECT DATABASE() as current_database;

-- 2. 检查表是否存在
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('sys_user', 'article', 'comment', 'user_ban', 'user_like', 'sensitive_word')
ORDER BY TABLE_NAME;

-- 3. 检查sys_user表结构
DESCRIBE sys_user;

-- 4. 检查是否有冲突的用户ID
SELECT 'Existing user IDs that might conflict:' as message;
SELECT user_id, user_name, nick_name FROM sys_user WHERE user_id IN (1,2,3,4,5) ORDER BY user_id;

-- 5. 检查是否有冲突的文章ID
SELECT 'Existing article IDs that might conflict:' as message;
SELECT id, title, author_id FROM article WHERE id IN (1,2,3) ORDER BY id;

-- 6. 检查评论表是否有数据
SELECT 'Existing comments count:' as message, COUNT(*) as count FROM comment;

-- 7. 显示建议的解决方案
SELECT '=== 解决方案建议 ===' as message;
SELECT '1. 如果出现主键冲突，请使用 test-data-comments-safe.sql' as solution;
SELECT '2. 该文件使用ID 100-104，避免与现有数据冲突' as solution;
SELECT '3. 或者先备份现有数据，然后清理冲突记录' as solution;

-- 8. 清理冲突数据的安全脚本（可选执行）
-- 注意：只有在确认要清理时才执行以下语句

/*
-- 备份现有数据（可选）
CREATE TABLE sys_user_backup AS SELECT * FROM sys_user WHERE user_id IN (1,2,3,4,5);
CREATE TABLE article_backup AS SELECT * FROM article WHERE id IN (1,2,3);
CREATE TABLE comment_backup AS SELECT * FROM comment WHERE article_id IN (1,2,3);

-- 清理冲突数据
SET SQL_SAFE_UPDATES = 0;
DELETE FROM user_like WHERE user_id IN (1,2,3,4,5) OR target_id IN (1,2,3);
DELETE FROM user_ban WHERE user_id IN (1,2,3,4,5);
DELETE FROM comment WHERE article_id IN (1,2,3) OR user_id IN (1,2,3,4,5);
DELETE FROM article WHERE id IN (1,2,3);
DELETE FROM sys_user WHERE user_id IN (1,2,3,4,5);
SET SQL_SAFE_UPDATES = 1;
*/

-- 9. 检查字段名称兼容性
SELECT '=== 字段名称检查 ===' as message;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'sys_user' 
AND COLUMN_NAME IN ('phonenumber', 'phone_number', 'nick_name', 'user_name')
ORDER BY COLUMN_NAME;

-- 10. 显示推荐的执行步骤
SELECT '=== 推荐执行步骤 ===' as message;
SELECT '1. 先执行: mysql -u root -p < fix-database-issues.sql' as step;
SELECT '2. 检查输出结果，确认数据库结构' as step;
SELECT '3. 如果有冲突，使用: mysql -u root -p < test-data-comments-safe.sql' as step;
SELECT '4. 如果无冲突，使用: mysql -u root -p < test-data-comments.sql' as step;
SELECT '5. 检查插入结果，确认测试数据正确' as step;

-- 评论管理系统测试数据（安全版本）
-- 根据application.yml配置，数据库名为cmr_blog
USE cmr_blog;

-- 设置安全模式，避免意外删除
SET SQL_SAFE_UPDATES = 0;

-- 检查数据库是否存在
SELECT 'Database cmr_blog exists' as status;

-- 显示当前数据库中的表
SHOW TABLES;

-- 清理现有测试数据（仅清理测试用户的数据，避免影响真实数据）
DELETE FROM `user_like` WHERE user_id IN (100,101,102,103,104) OR target_id IN (100,101,102);
DELETE FROM `user_ban` WHERE user_id IN (100,101,102,103,104);
DELETE FROM `comment` WHERE user_id IN (100,101,102,103,104);
DELETE FROM `article` WHERE id IN (100,101,102) AND author_id IN (100,101,102,103,104);
DELETE FROM `sys_user` WHERE user_id IN (100,101,102,103,104);

-- 插入测试用户数据（使用不冲突的ID）
INSERT INTO `sys_user` (`user_id`, `user_name`, `nick_name`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
(100, 'test_admin', '测试管理员', '<EMAIL>', '13800138100', '1', 'https://example.com/avatar100.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(101, 'test_user1', '测试用户1', '<EMAIL>', '13800138101', '1', 'https://example.com/avatar101.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(102, 'test_user2', '测试用户2', '<EMAIL>', '13800138102', '0', 'https://example.com/avatar102.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(103, 'test_user3', '测试用户3', '<EMAIL>', '13800138103', '1', 'https://example.com/avatar103.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(104, 'test_banned', '测试被禁用户', '<EMAIL>', '13800138104', '1', 'https://example.com/avatar104.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW());

-- 插入测试文章数据
INSERT INTO `article` (`id`, `title`, `content`, `summary`, `category_id`, `tags`, `cover_image`, `author_id`, `view_count`, `like_count`, `comment_count`, `collect_count`, `share_count`, `is_original`, `is_top`, `status`, `publish_time`, `create_time`, `update_time`) VALUES
(100, 'Spring Boot 测试教程', '这是一篇关于Spring Boot的详细测试教程...', 'Spring Boot基础知识介绍', 1, 'Spring Boot,Java,教程', 'https://example.com/cover100.jpg', 100, 150, 25, 8, 12, 5, 1, 0, 1, NOW(), NOW(), NOW()),
(101, 'Vue.js 测试实战', '本文介绍Vue.js的核心概念和实战应用...', 'Vue.js实战开发指南', 2, 'Vue.js,前端,JavaScript', 'https://example.com/cover101.jpg', 100, 200, 35, 12, 18, 8, 1, 1, 1, NOW(), NOW(), NOW()),
(102, 'MySQL 测试优化', '分享一些MySQL数据库性能优化的实用技巧...', 'MySQL性能优化指南', 3, 'MySQL,数据库,优化', 'https://example.com/cover102.jpg', 101, 180, 28, 15, 20, 6, 1, 0, 1, NOW(), NOW(), NOW());

-- 插入测试评论数据
INSERT INTO `comment` (`article_id`, `user_id`, `parent_id`, `reply_to_user_id`, `content`, `original_content`, `root_id`, `level`, `path`, `like_count`, `reply_count`, `status`, `is_sensitive`, `sensitive_type`, `sensitive_words`, `sensitive_score`, `ip_address`, `location`, `user_agent`, `create_time`, `update_time`, `audit_time`, `audit_user_id`, `audit_remark`) VALUES

-- 正常评论（已通过审核）
(100, 101, 0, NULL, '这篇文章写得很好，对初学者很有帮助！', '这篇文章写得很好，对初学者很有帮助！', 0, 1, '', 5, 2, 1, 0, NULL, NULL, 0, '*************', '北京市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY, 100, '内容正常，审核通过'),

(100, 102, 0, NULL, '同意楼主的观点，Spring Boot确实简化了很多配置', '同意楼主的观点，Spring Boot确实简化了很多配置', 0, 1, '', 3, 0, 1, 0, NULL, NULL, 0, '*************', '上海市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, 100, '回复内容正常'),

(100, 103, 0, NULL, '感谢分享，学到了很多新知识', '感谢分享，学到了很多新知识', 0, 1, '', 2, 0, 1, 0, NULL, NULL, 0, '*************', '广州市', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, 100, '感谢类评论，通过'),

-- 包含敏感词的评论（已标记敏感但通过审核）
(101, 101, 0, NULL, '这个教程还不错，比那些***的教程强多了', '这个教程还不错，比那些垃圾的教程强多了', 0, 1, '', 1, 1, 1, 1, 'profanity', '[{"word":"垃圾","type":"profanity","severity":2}]', 25, '*************', '深圳市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, 100, '轻微敏感词，过滤后通过'),

-- 待审核的评论
(102, 103, 0, NULL, '这个数据库优化方案很实用，收藏了', '这个数据库优化方案很实用，收藏了', 0, 1, '', 0, 0, 0, 0, NULL, NULL, 0, '*************', '成都市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR, NULL, NULL, NULL),

-- 包含严重敏感词的评论（待审核）
(100, 104, 0, NULL, '这文章写得还行，但是作者可能是个***', '这文章写得还行，但是作者可能是个傻逼', 0, 1, '', 0, 0, 0, 1, 'profanity', '[{"word":"傻逼","type":"profanity","severity":4}]', 65, '192.168.1.107', '西安市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 20 MINUTE, NOW() - INTERVAL 20 MINUTE, NULL, NULL, NULL),

-- 已拒绝的评论
(100, 104, 0, NULL, '这种***文章有什么好看的', '这种垃圾文章有什么好看的', 0, 1, '', 0, 0, 2, 1, 'profanity', '[{"word":"垃圾","type":"profanity","severity":2}]', 35, '192.168.1.110', '天津市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, 100, '内容不当，审核拒绝');

-- 插入用户禁言测试数据
INSERT INTO `user_ban` (`user_id`, `ban_type`, `ban_reason`, `ban_start_time`, `ban_end_time`, `is_permanent`, `status`, `operator_id`, `related_comment_id`, `create_time`, `update_time`) VALUES
-- 生效中的禁言
(104, 'COMMENT', '发布不当评论，包含敏感词', NOW() - INTERVAL 1 HOUR, NOW() + INTERVAL 23 HOUR, 0, 1, 100, NULL, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR),
-- 已过期的禁言
(103, 'COMMENT', '发布垃圾评论', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 1 DAY, 0, 0, 100, NULL, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 1 DAY);

-- 插入用户点赞测试数据
INSERT INTO `user_like` (`user_id`, `target_id`, `target_type`, `status`, `create_time`, `update_time`) VALUES
-- 文章点赞数据 (target_type = 1)
(101, 100, 1, 1, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(102, 100, 1, 1, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(103, 100, 1, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(101, 101, 1, 1, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR),
(102, 101, 1, 1, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR),
(101, 102, 1, 1, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR),
(102, 102, 1, 1, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR);

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;

-- 显示插入结果
SELECT '=== 测试数据插入完成 ===' as message;
SELECT 'Users inserted:' as type, COUNT(*) as count FROM sys_user WHERE user_id BETWEEN 100 AND 104;
SELECT 'Articles inserted:' as type, COUNT(*) as count FROM article WHERE id BETWEEN 100 AND 102;
SELECT 'Comments inserted:' as type, COUNT(*) as count FROM comment WHERE user_id BETWEEN 100 AND 104;
SELECT 'Bans inserted:' as type, COUNT(*) as count FROM user_ban WHERE user_id BETWEEN 100 AND 104;
SELECT 'Likes inserted:' as type, COUNT(*) as count FROM user_like WHERE user_id BETWEEN 100 AND 104;

SELECT '=== 测试数据准备完成，可以开始测试评论管理功能 ===' as message;

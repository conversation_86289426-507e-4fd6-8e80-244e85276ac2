package com.blog.cmrpersonalblog.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

/**
 * 评论审核请求DTO
 */
@Data
public class CommentAuditRequest {

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long commentId;

    /**
     * 审核结果（1-通过 2-拒绝）
     */
    @NotNull(message = "审核结果不能为空")
    private Integer auditResult;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 拒绝原因（拒绝时必填）
     */
    private String rejectReason;

    /**
     * 是否发送通知给评论者
     */
    private Boolean sendNotification = true;

    /**
     * 是否同时处理该用户的其他待审核评论
     */
    private Boolean batchProcess = false;

    /**
     * 审核结果枚举
     */
    public enum AuditResult {
        APPROVE(1, "审核通过"),
        REJECT(2, "审核拒绝");

        private final Integer code;
        private final String description;

        AuditResult(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static AuditResult fromCode(Integer code) {
            for (AuditResult result : values()) {
                if (result.code.equals(code)) {
                    return result;
                }
            }
            return null;
        }
    }

    /**
     * 验证审核结果是否有效
     */
    public boolean isValidAuditResult() {
        return AuditResult.fromCode(this.auditResult) != null;
    }

    /**
     * 获取审核结果枚举
     */
    public AuditResult getAuditResultEnum() {
        return AuditResult.fromCode(this.auditResult);
    }

    /**
     * 是否为审核通过
     */
    public boolean isApprove() {
        return AuditResult.APPROVE.getCode().equals(this.auditResult);
    }

    /**
     * 是否为审核拒绝
     */
    public boolean isReject() {
        return AuditResult.REJECT.getCode().equals(this.auditResult);
    }
}

package com.blog.cmrpersonalblog.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户统计实体类
 */
@Data
@TableName("user_stats")
public class UserStats {

    /**
     * 统计ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 发布文章数
     */
    @TableField("article_count")
    private Integer articleCount;

    /**
     * 文章总浏览量
     */
    @TableField("total_view_count")
    private Long totalViewCount;

    /**
     * 文章总点赞数
     */
    @TableField("total_like_count")
    private Integer totalLikeCount;

    /**
     * 文章总评论数
     */
    @TableField("total_comment_count")
    private Integer totalCommentCount;

    /**
     * 文章总收藏数
     */
    @TableField("total_collect_count")
    private Integer totalCollectCount;

    /**
     * 粉丝数
     */
    @TableField("follower_count")
    private Integer followerCount;

    /**
     * 关注数
     */
    @TableField("following_count")
    private Integer followingCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}

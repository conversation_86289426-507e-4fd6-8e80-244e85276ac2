# 角色管理 API 接口文档

## 概述

本文档描述了个人博客系统中角色管理模块的所有API接口。角色管理功能允许管理员对系统角色进行完整的生命周期管理，包括创建、查询、更新、删除角色，以及管理角色权限。

## 基本信息

- **基础URL**: `http://localhost:8081`
- **权限要求**: 所有接口都需要管理员权限 (`admin` 角色)
- **认证方式**: Bearer Token
- **数据格式**: JSON

## 安全限制

⚠️ **重要安全机制**：
- 管理员不能修改自己拥有的角色
- 管理员不能删除自己拥有的角色
- 管理员不能修改自己拥有的角色权限
- 角色删除前会检查是否有用户正在使用该角色
- 角色标识（roleKey）创建后不可修改

## 通用响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {}
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "错误描述",
    "data": null
}
```

## 角色管理接口

### 1. 分页查询角色列表

**接口地址**: `GET /admin/roles/list`

**接口描述**: 分页查询系统中的角色列表，支持多种条件筛选

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| size | Integer | 否 | 10 | 每页大小 |
| roleKey | String | 否 | - | 角色标识（模糊查询） |
| roleName | String | 否 | - | 角色名称（模糊查询） |
| status | String | 否 | - | 状态（0正常 1停用） |
| permissionId | Long | 否 | - | 权限ID（精确查询） |

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "roleKey": "admin",
                "roleName": "管理员",
                "description": "超级管理员",
                "status": "0",
                "statusText": "正常",
                "createBy": "system",
                "createTime": "2024-01-01T10:00:00",
                "updateBy": "admin",
                "updateTime": "2024-01-01T10:00:00",
                "permissions": [
                    {
                        "id": 1,
                        "permKey": "user:list",
                        "permName": "查看用户列表",
                        "description": "查看用户列表权限"
                    }
                ],
                "userCount": 1,
                "canDelete": false
            }
        ],
        "total": 10,
        "current": 1,
        "size": 10,
        "pages": 1,
        "hasPrevious": false,
        "hasNext": false
    }
}
```

### 2. 获取角色详情

**接口地址**: `GET /admin/roles/{roleId}/detail`

**接口描述**: 根据角色ID获取角色的详细信息，包括权限列表和使用统计

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "roleKey": "admin",
        "roleName": "管理员",
        "description": "超级管理员",
        "status": "0",
        "statusText": "正常",
        "createBy": "system",
        "createTime": "2024-01-01T10:00:00",
        "updateBy": "admin",
        "updateTime": "2024-01-01T10:00:00",
        "permissions": [
            {
                "id": 1,
                "permKey": "user:list",
                "permName": "查看用户列表",
                "description": "查看用户列表权限"
            }
        ],
        "userCount": 1,
        "canDelete": false
    }
}
```

### 3. 创建角色

**接口地址**: `POST /admin/roles/createRole`

**接口描述**: 创建新的系统角色，可以同时分配权限

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数**:
```json
{
    "roleKey": "string",         // 角色标识，必填，2-50字符，只能包含字母数字下划线
    "roleName": "string",        // 角色名称，必填，2-100字符
    "description": "string",     // 角色描述，选填，最大500字符
    "status": "0",               // 角色状态，选填，0正常 1停用，默认0
    "permissionIds": [1, 2, 3]   // 权限ID列表，选填
}
```

**字段说明**:
- `roleKey`: 角色标识，创建后不可修改，用于系统内部识别
- `roleName`: 角色显示名称，用于前端展示
- `description`: 角色描述信息
- `status`: 角色状态，0表示正常，1表示停用
- `permissionIds`: 要分配给角色的权限ID数组

**响应示例**:
```json
{
    "code": 200,
    "message": "角色创建成功"
}
```

### 4. 更新角色

**接口地址**: `PUT /admin/roles/{roleId}/update`

**接口描述**: 更新角色信息，包括基本信息和权限分配

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**请求参数**:
```json
{
    "roleName": "string",        // 角色名称，选填，2-100字符
    "description": "string",     // 角色描述，选填，最大500字符
    "status": "0",               // 角色状态，选填，0正常 1停用
    "permissionIds": [1, 2, 3]   // 权限ID列表，选填
}
```

**注意事项**:
- `roleKey` 不可修改
- 如果提供 `permissionIds`，将完全替换角色的权限列表
- 管理员不能修改自己拥有的角色

**响应示例**:
```json
{
    "code": 200,
    "message": "角色更新成功"
}
```

### 5. 删除角色

**接口地址**: `DELETE /admin/roles/{roleId}/delete`

**接口描述**: 删除指定的角色，删除前会检查角色使用情况

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**删除条件**:
- 角色没有被任何用户使用
- 不是管理员自己拥有的角色

**响应示例**:
```json
{
    "code": 200,
    "message": "角色删除成功"
}
```

### 6. 批量删除角色

**接口地址**: `DELETE /admin/roles/batchDelete`

**接口描述**: 批量删除多个角色

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数**:
```json
[1, 2, 3]  // 角色ID数组
```

**删除条件**:
- 所有角色都没有被用户使用
- 不包含管理员自己拥有的角色

**响应示例**:
```json
{
    "code": 200,
    "message": "角色批量删除成功"
}
```

### 7. 更新角色状态

**接口地址**: `PUT /admin/roles/{roleId}/updateStatus`

**接口描述**: 启用或停用指定角色

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | String | 是 | 状态（0正常 1停用） |

**注意事项**:
- 管理员不能修改自己拥有的角色状态
- 停用的角色不能分配给用户

**响应示例**:
```json
{
    "code": 200,
    "message": "角色启用成功"  // 或 "角色停用成功"
}
```

### 8. 检查角色标识是否存在

**接口地址**: `GET /admin/roles/checkRoleKey`

**接口描述**: 检查角色标识是否已经存在，用于创建和编辑时的验证

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleKey | String | 是 | 要检查的角色标识 |
| excludeId | Long | 否 | 排除的角色ID（编辑时使用） |

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true  // true: 可用（不存在）, false: 不可用（已存在）
}
```

### 9. 获取所有角色（下拉选择用）

**接口地址**: `GET /admin/roles/getAllForSelect`

**接口描述**: 获取所有正常状态的角色列表，用于下拉选择组件

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "roleKey": "admin",
            "roleName": "管理员",
            "description": "超级管理员",
            "status": "0",
            "statusText": "正常"
        },
        {
            "id": 2,
            "roleKey": "user",
            "roleName": "普通用户",
            "description": "普通用户角色",
            "status": "0",
            "statusText": "正常"
        }
    ]
}
```

## 角色权限管理接口

### 1. 获取角色权限列表

**接口地址**: `GET /admin/roles/{roleId}/getPermissions`

**接口描述**: 获取指定角色拥有的所有权限列表

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "permKey": "user:list",
            "permName": "查看用户列表",
            "description": "查看用户列表权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        },
        {
            "id": 2,
            "permKey": "user:add",
            "permName": "添加用户",
            "description": "添加用户权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        }
    ]
}
```

### 2. 为角色分配权限

**接口地址**: `POST /admin/roles/{roleId}/assignPermissions`

**接口描述**: 为指定角色分配权限，会完全替换角色的权限列表

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**请求参数**:
```json
[1, 2, 3]  // 权限ID数组
```

**注意事项**:
- 权限ID必须存在于系统中
- 会完全替换角色的权限列表（不是追加）
- 管理员不能修改自己拥有的角色权限

**响应示例**:
```json
{
    "code": 200,
    "message": "权限分配成功"
}
```

### 3. 移除角色所有权限

**接口地址**: `DELETE /admin/roles/{roleId}/removeAllPermissions`

**接口描述**: 移除指定角色的所有权限

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleId | Long | 是 | 角色ID |

**注意事项**:
- 管理员不能移除自己拥有的角色权限
- 操作后角色将没有任何权限

**响应示例**:
```json
{
    "code": 200,
    "message": "权限移除成功"
}
```

### 4. 获取所有权限列表

**接口地址**: `GET /admin/roles/getAllPermissions`

**接口描述**: 获取系统中所有可用的权限列表，用于权限分配时的选择

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "permKey": "user:list",
            "permName": "查看用户列表",
            "description": "查看用户列表权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        },
        {
            "id": 2,
            "permKey": "user:add",
            "permName": "添加用户",
            "description": "添加用户权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        },
        {
            "id": 3,
            "permKey": "user:edit",
            "permName": "编辑用户",
            "description": "编辑用户权限",
            "createBy": "system",
            "createTime": "2024-01-01T10:00:00",
            "updateBy": "admin",
            "updateTime": "2024-01-01T10:00:00"
        }
    ]
}
```

## 错误码说明

### 常见错误码

| 错误码 | 说明 | 可能原因 |
|--------|------|----------|
| 400 | 参数验证失败 | 角色标识格式错误、角色名称长度错误等 |
| 401 | 未登录或Token过期 | 需要重新登录获取Token |
| 403 | 权限不足或安全限制 | 非管理员用户或尝试修改自己的角色 |
| 404 | 资源不存在 | 角色不存在、权限不存在 |
| 409 | 数据冲突 | 角色标识已存在、角色正在被用户使用 |
| 500 | 服务器内部错误 | 数据库操作失败等 |

### 错误响应示例

#### 参数验证失败
```json
{
    "code": 400,
    "message": "角色标识只能包含字母、数字和下划线",
    "data": null
}
```

#### 权限不足
```json
{
    "code": 403,
    "message": "管理员不能修改自己拥有的角色",
    "data": null
}
```

#### 资源不存在
```json
{
    "code": 404,
    "message": "角色不存在",
    "data": null
}
```

#### 数据冲突
```json
{
    "code": 409,
    "message": "角色标识已存在",
    "data": null
}
```

## 使用示例

### 创建角色的完整流程

1. **检查角色标识是否可用**
```bash
GET /admin/roles/checkRoleKey?roleKey=editor
```

2. **获取可用权限列表**
```bash
GET /admin/roles/getAllPermissions
```

3. **创建角色**
```bash
POST /admin/roles/createRole
{
    "roleKey": "editor",
    "roleName": "编辑者",
    "description": "内容编辑角色",
    "status": "0",
    "permissionIds": [1, 2, 3]
}
```

### 更新角色权限的流程

1. **获取角色当前权限**
```bash
GET /admin/roles/2/getPermissions
```

2. **分配新权限**
```bash
POST /admin/roles/2/assignPermissions
[1, 2, 3, 4]
```

## 注意事项

1. **安全限制**：
   - 所有接口都需要管理员权限
   - 管理员不能操作自己拥有的角色
   - 删除角色前会检查使用情况

2. **数据一致性**：
   - 角色标识创建后不可修改
   - 权限分配是完全替换，不是追加
   - 停用的角色不能分配给用户

3. **性能考虑**：
   - 分页查询支持多种筛选条件
   - 角色详情包含完整的权限信息
   - 下拉选择接口只返回必要字段

4. **最佳实践**：
   - 创建角色前先检查标识是否可用
   - 删除角色前确认没有用户使用
   - 定期检查和清理不必要的权限分配

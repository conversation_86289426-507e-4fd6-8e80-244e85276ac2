package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户详情响应DTO
 */
@Data
public class UserProfileResponse {

    /**
     * 用户基本信息
     */
    private Long userId;
    private String userName;
    private String nickName;
    private String email;
    private String avatar;
    private String sex;
    private String sexName;
    private LocalDateTime createTime;

    /**
     * 用户统计数据
     */
    private UserStatsInfo stats;

    /**
     * 最近发布的文章
     */
    private List<ArticleInfo> recentArticles;

    /**
     * 是否被当前用户关注
     */
    private Boolean isFollowed;

    /**
     * 用户统计信息
     */
    @Data
    public static class UserStatsInfo {
        private Integer articleCount;           // 发布文章数
        private Long totalViewCount;           // 文章总浏览量
        private Integer totalLikeCount;        // 文章总点赞数
        private Integer totalCommentCount;     // 文章总评论数
        private Integer totalCollectCount;     // 文章总收藏数
        private Integer followerCount;         // 粉丝数
        private Integer followingCount;        // 关注数
    }

    /**
     * 文章信息
     */
    @Data
    public static class ArticleInfo {
        private Long id;
        private String title;
        private String summary;
        private String coverImage;
        private Integer viewCount;
        private Integer likeCount;
        private Integer commentCount;
        private Integer collectCount;
        private LocalDateTime publishTime;
        private String categoryName;
        private List<String> tagList;
    }
}

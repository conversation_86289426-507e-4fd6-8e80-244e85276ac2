package com.blog.cmrpersonalblog.enums;

/**
 * 用户活动类型枚举
 * 只记录重要的用户操作，避免产生过多无用记录
 */
public enum ActivityType {
    
    // ==================== 认证相关 ====================
    LOGIN("LOGIN", "用户登录", true, 30),
    LOGOUT("LOGOUT", "用户登出", false, 30),
    LOGIN_FAILED("LOGIN_FAILED", "登录失败", true, 7),
    PASSWORD_CHANGED("PASSWORD_CHANGED", "修改密码", true, 90),
    
    // ==================== 用户管理 ====================
    USER_CREATED("USER_CREATED", "创建用户", true, 365),
    USER_UPDATED("USER_UPDATED", "更新用户信息", true, 90),
    USER_DELETED("USER_DELETED", "删除用户", true, 365),
    USER_STATUS_CHANGED("USER_STATUS_CHANGED", "用户状态变更", true, 180),
    USER_ROLE_ASSIGNED("USER_ROLE_ASSIGNED", "分配用户角色", true, 180),
    USER_ROLE_REMOVED("USER_ROLE_REMOVED", "移除用户角色", true, 180),
    
    // ==================== 角色权限管理 ====================
    ROLE_CREATED("ROLE_CREATED", "创建角色", true, 365),
    ROLE_UPDATED("ROLE_UPDATED", "更新角色", true, 180),
    ROLE_DELETED("ROLE_DELETED", "删除角色", true, 365),
    PERMISSION_ASSIGNED("PERMISSION_ASSIGNED", "分配权限", true, 180),
    PERMISSION_REMOVED("PERMISSION_REMOVED", "移除权限", true, 180),
    
    // ==================== 内容管理 ====================
    ARTICLE_PUBLISHED("ARTICLE_PUBLISHED", "发布文章", true, 180),
    ARTICLE_UPDATED("ARTICLE_UPDATED", "更新文章", false, 30),
    ARTICLE_DELETED("ARTICLE_DELETED", "删除文章", true, 365),
    ARTICLE_STATUS_CHANGED("ARTICLE_STATUS_CHANGED", "文章状态变更", true, 90),
    
    // ==================== 社交互动 ====================
    USER_FOLLOWED("USER_FOLLOWED", "关注用户", false, 30),
    USER_UNFOLLOWED("USER_UNFOLLOWED", "取消关注", false, 30),
    ARTICLE_LIKED("ARTICLE_LIKED", "点赞文章", false, 7),
    ARTICLE_COLLECTED("ARTICLE_COLLECTED", "收藏文章", false, 30),
    COMMENT_POSTED("COMMENT_POSTED", "发表评论", false, 30),
    
    // ==================== 系统管理 ====================
    SYSTEM_CONFIG_CHANGED("SYSTEM_CONFIG_CHANGED", "系统配置变更", true, 365),
    DATA_EXPORT("DATA_EXPORT", "数据导出", true, 90),
    DATA_IMPORT("DATA_IMPORT", "数据导入", true, 90),
    SECURITY_VIOLATION("SECURITY_VIOLATION", "安全违规", true, 365),
    
    // ==================== 文件管理 ====================
    FILE_UPLOADED("FILE_UPLOADED", "文件上传", false, 30),
    FILE_DELETED("FILE_DELETED", "文件删除", true, 90);
    
    private final String code;
    private final String description;
    private final boolean isImportant;  // 是否为重要操作
    private final int retentionDays;    // 保留天数
    
    ActivityType(String code, String description, boolean isImportant, int retentionDays) {
        this.code = code;
        this.description = description;
        this.isImportant = isImportant;
        this.retentionDays = retentionDays;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isImportant() {
        return isImportant;
    }
    
    public int getRetentionDays() {
        return retentionDays;
    }
    
    /**
     * 根据代码获取活动类型
     */
    public static ActivityType fromCode(String code) {
        for (ActivityType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 获取所有重要操作类型
     */
    public static ActivityType[] getImportantTypes() {
        return java.util.Arrays.stream(values())
                .filter(ActivityType::isImportant)
                .toArray(ActivityType[]::new);
    }
    
    /**
     * 获取认证相关操作类型
     */
    public static ActivityType[] getAuthTypes() {
        return new ActivityType[]{LOGIN, LOGOUT, LOGIN_FAILED, PASSWORD_CHANGED};
    }
    
    /**
     * 获取管理操作类型
     */
    public static ActivityType[] getManagementTypes() {
        return new ActivityType[]{
            USER_CREATED, USER_UPDATED, USER_DELETED, USER_STATUS_CHANGED,
            USER_ROLE_ASSIGNED, USER_ROLE_REMOVED,
            ROLE_CREATED, ROLE_UPDATED, ROLE_DELETED,
            PERMISSION_ASSIGNED, PERMISSION_REMOVED,
            SYSTEM_CONFIG_CHANGED, DATA_EXPORT, DATA_IMPORT
        };
    }
}

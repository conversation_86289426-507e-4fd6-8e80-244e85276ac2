package com.blog.cmrpersonalblog.dto;

import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 文章批量操作请求DTO
 */
@Data
public class ArticleBatchOperationRequest {

    /**
     * 文章ID列表
     */
    @NotEmpty(message = "文章ID列表不能为空")
    private List<Long> articleIds;

    /**
     * 操作类型
     * APPROVE - 审核通过
     * REJECT - 审核拒绝
     * PUBLISH - 发布
     * UNPUBLISH - 下架
     * TOP - 置顶
     * UNTOP - 取消置顶
     * DELETE - 删除
     * RESTORE - 恢复
     */
    @NotNull(message = "操作类型不能为空")
    private String operationType;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 目标状态（用于状态变更操作）
     */
    private Integer targetStatus;

    /**
     * 是否置顶（用于置顶操作）
     */
    private Integer isTop;

    /**
     * 分类ID（用于批量修改分类）
     */
    private Long categoryId;

    /**
     * 标签（用于批量修改标签）
     */
    private String tags;

    /**
     * 操作原因（审核拒绝时必填）
     */
    private String reason;

    /**
     * 是否发送通知给作者
     */
    private Boolean sendNotification = true;

    /**
     * 批量操作类型枚举
     */
    public enum OperationType {
        APPROVE("APPROVE", "审核通过"),
        REJECT("REJECT", "审核拒绝"),
        PUBLISH("PUBLISH", "发布"),
        UNPUBLISH("UNPUBLISH", "下架"),
        TOP("TOP", "置顶"),
        UNTOP("UNTOP", "取消置顶"),
        DELETE("DELETE", "删除"),
        RESTORE("RESTORE", "恢复"),
        CHANGE_CATEGORY("CHANGE_CATEGORY", "修改分类"),
        CHANGE_TAGS("CHANGE_TAGS", "修改标签");

        private final String code;
        private final String description;

        OperationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static OperationType fromCode(String code) {
            for (OperationType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 验证操作类型是否有效
     */
    public boolean isValidOperationType() {
        return OperationType.fromCode(this.operationType) != null;
    }

    /**
     * 获取操作类型枚举
     */
    public OperationType getOperationTypeEnum() {
        return OperationType.fromCode(this.operationType);
    }
}

package com.blog.cmrpersonalblog.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            SaRouter.match("/**")    // 拦截所有路由
                    .notMatch("/auth/login")    // 排除登录接口
                    .notMatch("/auth/logout")   // 排除注销接口
                    .notMatch("/test/health")   // 排除健康检查接口
                    .notMatch("/error")         // 排除错误页面
                    .notMatch("/favicon.ico")   // 排除图标
                    .notMatch("/static/**")     // 排除静态资源
                    .notMatch("/files/**")      // 排除文件访问路径（图片等静态文件）
                    .check(r -> StpUtil.checkLogin());        // 登录校验
        })).addPathPatterns("/**");
    }
}

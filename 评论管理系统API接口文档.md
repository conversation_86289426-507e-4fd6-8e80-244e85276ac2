# 评论管理API 接口文档

## 📋 概述

评论管理系统提供了完整的评论管理功能，包括评论审核、敏感词过滤、批量操作、统计分析等功能。

### 基础信息
- **基础URL**: `http://localhost:8081`
- **API前缀**: `/admin/comments`
- **权限要求**: 大部分接口需要管理员权限 (`admin` 角色)
- **认证方式**: Bearer <PERSON> (Sa-Token)

### 通用响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

### 评论状态说明
- `0`: 待审核
- `1`: 已通过
- `2`: 已拒绝  
- `3`: 已删除

---

## 🔍 1. 评论查询接口

### 1.1 分页查询评论列表
```http
GET /admin/comments/list
Authorization: Bearer {admin_token}
```

**查询参数**:
```json
{
  "current": 1,              // 页码，默认1
  "size": 10,                // 每页大小，默认10
  "articleId": 1,            // 文章ID（可选）
  "userId": 2,               // 用户ID（可选）
  "userName": "用户名",       // 用户名模糊查询（可选）
  "content": "评论内容",      // 评论内容模糊查询（可选）
  "status": 0,               // 评论状态（可选）
  "isSensitive": 1,          // 是否敏感评论 0-否 1-是（可选）
  "sensitiveType": "profanity", // 敏感词类型（可选）
  "parentId": 0,             // 父评论ID（可选）
  "rootId": 1,               // 根评论ID（可选）
  "ipAddress": "***********", // IP地址（可选）
  "startTime": "2024-01-01 00:00:00", // 开始时间（可选）
  "endTime": "2024-12-31 23:59:59",   // 结束时间（可选）
  "keyword": "搜索关键词",    // 关键词搜索（可选）
  "sortField": "create_time", // 排序字段（可选）
  "sortOrder": "desc",       // 排序方向 asc/desc（可选）
  "onlyTopLevel": false,     // 是否只查询顶级评论（可选）
  "includeChildren": true,   // 是否包含子评论（可选）
  "minLikeCount": 0,         // 最小点赞数（可选）
  "maxLikeCount": 100,       // 最大点赞数（可选）
  "minReplyCount": 0,        // 最小回复数（可选）
  "maxReplyCount": 50        // 最大回复数（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "articleId": 1,
        "articleTitle": "Spring Boot 入门教程",
        "userId": 2,
        "userName": "testuser1",
        "userNickName": "测试用户1",
        "userAvatar": "https://example.com/avatar.jpg",
        "content": "这篇文章写得很好！",
        "originalContent": "这篇文章写得很好！",
        "parentId": 0,
        "rootId": 0,
        "level": 1,
        "path": "1",
        "likeCount": 5,
        "replyCount": 2,
        "status": 1,
        "statusName": "已通过",
        "isSensitive": 0,
        "sensitiveType": null,
        "sensitiveWords": null,
        "sensitiveScore": 0,
        "ipAddress": "*************",
        "location": "北京市",
        "userAgent": "Mozilla/5.0...",
        "createTime": "2024-01-01T12:00:00",
        "updateTime": "2024-01-01T12:00:00",
        "auditTime": "2024-01-01T12:05:00",
        "auditUserId": 1,
        "auditUserName": "admin",
        "auditRemark": "内容正常，审核通过",
        "children": []
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 1.2 获取评论详情
```http
GET /admin/comments/{commentId}
Authorization: Bearer {admin_token}
```

**路径参数**:
- `commentId`: 评论ID

**响应**: 返回单个评论的详细信息，格式同上。

### 1.3 获取评论树形结构
```http
GET /admin/comments/tree?articleId={articleId}&rootId={rootId}
Authorization: Bearer {admin_token}
```

**查询参数**:
- `articleId`: 文章ID（必填）
- `rootId`: 根评论ID（可选，不填则获取所有顶级评论）

**响应**: 返回树形结构的评论列表，包含父子关系。

---

## ✅ 2. 评论审核接口

### 2.1 审核评论
```http
POST /admin/comments/audit
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**请求体**:
```json
{
  "commentId": 1,            // 评论ID（必填）
  "auditResult": 1,          // 审核结果：1-通过 2-拒绝（必填）
  "auditRemark": "审核通过", // 审核备注（可选）
  "rejectReason": "违反社区规范", // 拒绝原因（拒绝时建议填写）
  "sendNotification": true,  // 是否发送通知给评论者（可选，默认true）
  "batchProcess": false      // 是否同时处理该用户的其他待审核评论（可选，默认false）
}
```

**响应**:
```json
{
  "code": 200,
  "message": "审核成功",
  "data": null
}
```

### 2.2 批量操作评论
```http
POST /admin/comments/batch
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**请求体**:
```json
{
  "commentIds": [1, 2, 3],   // 评论ID列表（必填）
  "operationType": "APPROVE", // 操作类型（必填）
  "remark": "批量审核通过",   // 操作备注（可选）
  "rejectReason": "违规内容", // 拒绝原因（拒绝时使用）
  "sensitiveType": "profanity", // 敏感词类型（标记敏感时使用）
  "sendNotification": true,  // 是否发送通知（可选）
  "banUsers": false,         // 是否同时禁言用户（可选）
  "banDuration": 1440        // 禁言时长（分钟，如果选择禁言）
}
```

**操作类型**:
- `APPROVE`: 审核通过
- `REJECT`: 审核拒绝
- `DELETE`: 删除
- `RESTORE`: 恢复
- `MARK_SENSITIVE`: 标记为敏感
- `UNMARK_SENSITIVE`: 取消敏感标记

**响应**:
```json
{
  "code": 200,
  "message": "批量操作完成",
  "data": {
    "success": true,
    "affectedRows": 3,
    "successIds": ["1", "2", "3"],
    "failedIds": []
  }
}
```

---

## 🗑️ 3. 评论操作接口

### 3.1 删除评论
```http
DELETE /admin/comments/{commentId}
Authorization: Bearer {admin_token}
```

### 3.2 恢复评论
```http
POST /admin/comments/{commentId}/restore
Authorization: Bearer {admin_token}
```

### 3.3 标记敏感评论
```http
POST /admin/comments/{commentId}/mark-sensitive?sensitiveType={type}
Authorization: Bearer {admin_token}
```

**查询参数**:
- `sensitiveType`: 敏感词类型（profanity/politics/advertisement/spam/illegal）

### 3.4 取消敏感标记
```http
POST /admin/comments/{commentId}/unmark-sensitive
Authorization: Bearer {admin_token}
```

---

## 🔍 4. 敏感词检测接口

### 4.1 检测敏感词
```http
POST /admin/comments/check-sensitive
Authorization: Bearer {token}  // 注意：此接口只需登录即可，不需要管理员权限
Content-Type: text/plain
```

**请求体**: 直接发送要检测的文本内容

**响应**:
```json
{
  "code": 200,
  "message": "检测完成",
  "data": {
    "hasSensitiveWords": true,
    "sensitiveScore": 75,
    "sensitiveWords": [
      {
        "word": "敏感词",
        "type": "profanity",
        "severity": 4,
        "position": 10,
        "replacement": "***",
        "typeDescription": "脏话"
      }
    ],
    "filteredContent": "这是一段包含***的评论",
    "originalContent": "这是一段包含敏感词的评论",
    "primaryType": "profanity",
    "suggestedAction": "REVIEW",
    "details": {
      "processTime": 15,
      "totalWords": 1,
      "maxSeverity": 4
    }
  }
}
```

---

## 📊 5. 统计分析接口

### 5.1 获取评论统计信息
```http
GET /admin/comments/statistics
Authorization: Bearer {admin_token}
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "totalComments": 1000,
    "pendingAudit": 50,
    "approved": 800,
    "rejected": 100,
    "deleted": 50,
    "sensitiveComments": 200,
    "todayComments": 20,
    "weekComments": 150,
    "monthComments": 600
  }
}
```

### 5.2 获取待审核评论数量
```http
GET /admin/comments/pending-audit-count
Authorization: Bearer {admin_token}
```

### 5.3 获取敏感评论数量
```http
GET /admin/comments/sensitive-count
Authorization: Bearer {admin_token}
```

### 5.4 获取评论状态分布
```http
GET /admin/comments/status-distribution
Authorization: Bearer {admin_token}
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "待审核": 50,
    "已通过": 800,
    "已拒绝": 100,
    "已删除": 50
  }
}
```

### 5.5 获取敏感词类型分布
```http
GET /admin/comments/sensitive-type-distribution
Authorization: Bearer {admin_token}
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "profanity": 80,
    "politics": 20,
    "advertisement": 50,
    "spam": 30,
    "illegal": 10
  }
}
```

---

## 📝 6. 评论查询扩展接口

### 6.1 分页获取最新评论列表
```http
GET /admin/comments/latest
Authorization: Bearer {admin_token}
```

**查询参数**:

**基础分页参数**:
- `current`: 页码（可选，默认1）
- `size`: 每页大小（可选，默认10）

**筛选条件参数**:
- `articleId`: 文章ID（可选，筛选特定文章的评论）
- `userId`: 用户ID（可选，筛选特定用户的评论）
- `status`: 评论状态（可选，默认1，0-待审核 1-已通过 2-已拒绝 3-已删除）
- `includeSensitive`: 是否包含敏感评论（可选，默认true）
- `startTime`: 开始时间（可选，格式：2024-01-01 00:00:00）
- `endTime`: 结束时间（可选，格式：2024-01-31 23:59:59）
- `keyword`: 关键词搜索（可选，在评论内容和用户名中搜索）
- `onlyTopLevel`: 是否只查询顶级评论（可选，默认false）
- `minLikeCount`: 最小点赞数（可选，筛选点赞数不少于指定值的评论）
- `minReplyCount`: 最小回复数（可选，筛选回复数不少于指定值的评论）

**使用示例**:
```http
# 基础查询
GET /admin/comments/latest?current=1&size=20

# 查询特定文章的最新评论
GET /admin/comments/latest?articleId=123&current=1&size=10

# 查询不包含敏感词的评论
GET /admin/comments/latest?includeSensitive=false&current=1&size=10

# 查询指定时间范围内的评论
GET /admin/comments/latest?startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59

# 搜索包含关键词的顶级评论
GET /admin/comments/latest?keyword=Spring Boot&onlyTopLevel=true&current=1&size=10

# 查询点赞数不少于5的最新评论
GET /admin/comments/latest?minLikeCount=5&current=1&size=10
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 20,
        "articleId": 3,
        "articleTitle": "MySQL 数据库优化技巧",
        "userId": 3,
        "userName": "testuser2",
        "userNickName": "测试用户2",
        "userAvatar": "https://example.com/avatar.jpg",
        "content": "这个优化方案很实用！",
        "originalContent": "这个优化方案很实用！",
        "parentId": 0,
        "rootId": 0,
        "level": 1,
        "path": "20",
        "likeCount": 5,
        "replyCount": 2,
        "status": 1,
        "statusName": "已通过",
        "isSensitive": 0,
        "sensitiveType": null,
        "sensitiveWords": null,
        "sensitiveScore": 0,
        "ipAddress": "*************",
        "location": "北京市",
        "userAgent": "Mozilla/5.0...",
        "createTime": "2024-01-01T15:30:00",
        "updateTime": "2024-01-01T15:30:00",
        "auditTime": "2024-01-01T15:35:00",
        "auditUserId": 1,
        "auditUserName": "admin",
        "auditRemark": "内容正常，审核通过"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 6.2 分页获取热门评论列表
```http
GET /admin/comments/popular
Authorization: Bearer {admin_token}
```

**查询参数**:

**基础分页参数**:
- `current`: 页码（可选，默认1）
- `size`: 每页大小（可选，默认10）

**筛选条件参数**:
- `articleId`: 文章ID（可选，筛选特定文章的评论）
- `userId`: 用户ID（可选，筛选特定用户的评论）
- `status`: 评论状态（可选，默认1，0-待审核 1-已通过 2-已拒绝 3-已删除）
- `includeSensitive`: 是否包含敏感评论（可选，默认true）
- `startTime`: 开始时间（可选，格式：2024-01-01 00:00:00）
- `endTime`: 结束时间（可选，格式：2024-01-31 23:59:59）
- `keyword`: 关键词搜索（可选，在评论内容和用户名中搜索）
- `onlyTopLevel`: 是否只查询顶级评论（可选，默认false）
- `minLikeCount`: 最小点赞数（可选，筛选点赞数不少于指定值的评论）
- `minReplyCount`: 最小回复数（可选，筛选回复数不少于指定值的评论）

**热门评论特有参数**:
- `likeWeight`: 点赞权重（可选，默认2，表示1个点赞=2分热度）
- `replyWeight`: 回复权重（可选，默认1，表示1个回复=1分热度）
- `minHotScore`: 最小热度分数（可选，筛选热度不少于指定值的评论）
- `timeRange`: 时间范围类型（可选，默认all）
  - `today`: 今天
  - `week`: 最近7天
  - `month`: 最近30天
  - `all`: 全部时间

**热度计算公式**: `热度分数 = 点赞数 × likeWeight + 回复数 × replyWeight`

**使用示例**:
```http
# 基础查询
GET /admin/comments/popular?current=1&size=20

# 查询特定文章的热门评论
GET /admin/comments/popular?articleId=123&current=1&size=10

# 查询本周热门评论
GET /admin/comments/popular?timeRange=week&current=1&size=15

# 自定义热度权重
GET /admin/comments/popular?likeWeight=3&replyWeight=2&current=1&size=10

# 查询热度不少于20分的评论
GET /admin/comments/popular?minHotScore=20&current=1&size=10

# 查询今天热度最高的顶级评论
GET /admin/comments/popular?timeRange=today&onlyTopLevel=true&current=1&size=10

# 搜索包含关键词的热门评论
GET /admin/comments/popular?keyword=Vue.js&minLikeCount=5&current=1&size=10
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 15,
        "articleId": 1,
        "articleTitle": "Spring Boot 入门教程",
        "userId": 2,
        "userName": "testuser1",
        "userNickName": "测试用户1",
        "userAvatar": "https://example.com/avatar.jpg",
        "content": "这篇教程太棒了，收藏了！",
        "originalContent": "这篇教程太棒了，收藏了！",
        "parentId": 0,
        "rootId": 0,
        "level": 1,
        "path": "15",
        "likeCount": 25,
        "replyCount": 8,
        "status": 1,
        "statusName": "已通过",
        "isSensitive": 0,
        "sensitiveType": null,
        "sensitiveWords": null,
        "sensitiveScore": 0,
        "ipAddress": "*************",
        "location": "上海市",
        "userAgent": "Mozilla/5.0...",
        "createTime": "2024-01-01T10:00:00",
        "updateTime": "2024-01-01T10:00:00",
        "auditTime": "2024-01-01T10:05:00",
        "auditUserId": 1,
        "auditUserName": "admin",
        "auditRemark": "优质评论，审核通过",
        "hotScore": 58
      }
    ],
    "total": 50,
    "size": 10,
    "current": 1,
    "pages": 5
  }
}
```

### 6.3 搜索评论
```http
GET /admin/comments/search?keyword={keyword}&current={current}&size={size}
Authorization: Bearer {admin_token}
```

**查询参数**:
- `keyword`: 搜索关键词（必填）
- `current`: 页码（可选，默认1）
- `size`: 每页大小（可选，默认10）

**响应**: 返回包含关键词的评论分页列表。

### 6.4 获取用户评论列表
```http
GET /admin/comments/user/{userId}?current={current}&size={size}
Authorization: Bearer {admin_token}
```

**路径参数**:
- `userId`: 用户ID

**查询参数**:
- `current`: 页码（可选，默认1）
- `size`: 每页大小（可选，默认10）

**响应**: 返回指定用户的评论分页列表。

### 6.5 获取文章评论列表
```http
GET /admin/comments/article/{articleId}?current={current}&size={size}
Authorization: Bearer {admin_token}
```

**路径参数**:
- `articleId`: 文章ID

**查询参数**:
- `current`: 页码（可选，默认1）
- `size`: 每页大小（可选，默认10）

**响应**: 返回指定文章的评论分页列表。

### 6.6 根据IP地址获取评论列表
```http
GET /admin/comments/ip/{ipAddress}?current={current}&size={size}
Authorization: Bearer {admin_token}
```

**路径参数**:
- `ipAddress`: IP地址（需要URL编码）

**查询参数**:
- `current`: 页码（可选，默认1）
- `size`: 每页大小（可选，默认10）

**响应**: 返回指定IP地址的评论分页列表。

---

## 🔧 7. 系统维护接口

### 7.1 重新计算评论统计数据
```http
POST /admin/comments/{commentId}/recalculate-stats
Authorization: Bearer {admin_token}
```

**路径参数**:
- `commentId`: 评论ID

**功能**: 重新计算指定评论的点赞数、回复数等统计数据。

**响应**:
```json
{
  "code": 200,
  "message": "统计数据重新计算成功",
  "data": null
}
```

---

## 🚨 8. 错误码说明

### 常见错误码
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未登录或token无效
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误示例
```json
{
  "code": 400,
  "message": "评论不存在",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

```json
{
  "code": 403,
  "message": "权限不足，需要管理员权限",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

---

## 📋 9. 数据模型说明

### 9.1 评论状态枚举
```java
public enum CommentStatus {
    PENDING(0, "待审核"),
    APPROVED(1, "已通过"),
    REJECTED(2, "已拒绝"),
    DELETED(3, "已删除");
}
```

### 9.2 敏感词类型枚举
```java
public enum SensitiveType {
    PROFANITY("profanity", "脏话"),
    POLITICS("politics", "政治敏感"),
    ADVERTISEMENT("advertisement", "广告"),
    SPAM("spam", "垃圾信息"),
    ILLEGAL("illegal", "违法内容");
}
```

### 9.3 批量操作类型枚举
```java
public enum BatchOperationType {
    APPROVE("APPROVE", "审核通过"),
    REJECT("REJECT", "审核拒绝"),
    DELETE("DELETE", "删除"),
    RESTORE("RESTORE", "恢复"),
    MARK_SENSITIVE("MARK_SENSITIVE", "标记为敏感"),
    UNMARK_SENSITIVE("UNMARK_SENSITIVE", "取消敏感标记");
}
```

---

## 🔐 10. 权限说明

### 10.1 管理员权限接口
以下接口需要 `admin` 角色：
- 所有评论管理接口（除敏感词检测外）
- 评论审核接口
- 批量操作接口
- 统计分析接口

### 10.2 登录用户权限接口
以下接口只需要登录即可：
- 敏感词检测接口 (`/admin/comments/check-sensitive`)

### 10.3 权限验证
系统使用Sa-Token进行权限验证：
- `@SaCheckRole("admin")`: 需要管理员角色
- `@SaCheckLogin`: 需要登录

---

## 📖 11. 使用示例

### 11.1 获取待审核评论列表
```bash
curl -X GET "http://localhost:8081/admin/comments/list?status=0&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"
```

### 11.2 获取最新评论列表（基础分页）
```bash
curl -X GET "http://localhost:8081/admin/comments/latest?current=1&size=20" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"
```

### 11.3 获取最新评论列表（带筛选条件）
```bash
# 查询特定文章的最新评论
curl -X GET "http://localhost:8081/admin/comments/latest?articleId=123&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"

# 搜索包含关键词的评论
curl -X GET "http://localhost:8081/admin/comments/latest?keyword=Spring Boot&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"

# 查询指定时间范围内的评论
curl -X GET "http://localhost:8081/admin/comments/latest?startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"
```

### 11.4 获取热门评论列表（基础分页）
```bash
curl -X GET "http://localhost:8081/admin/comments/popular?current=1&size=15" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"
```

### 11.5 获取热门评论列表（带筛选条件）
```bash
# 查询本周热门评论
curl -X GET "http://localhost:8081/admin/comments/popular?timeRange=week&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"

# 自定义热度权重查询
curl -X GET "http://localhost:8081/admin/comments/popular?likeWeight=3&replyWeight=2&minHotScore=20&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"

# 查询今天热度最高的顶级评论
curl -X GET "http://localhost:8081/admin/comments/popular?timeRange=today&onlyTopLevel=true&current=1&size=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json"
```

### 11.6 审核通过评论
```bash
curl -X POST "http://localhost:8081/admin/comments/audit" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "commentId": 1,
    "auditResult": 1,
    "auditRemark": "内容正常，审核通过"
  }'
```

### 11.7 批量删除评论
```bash
curl -X POST "http://localhost:8081/admin/comments/batch" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "commentIds": [1, 2, 3],
    "operationType": "DELETE",
    "remark": "批量删除违规评论"
  }'
```

### 11.8 检测敏感词
```bash
curl -X POST "http://localhost:8081/admin/comments/check-sensitive" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: text/plain" \
  -d "这是一段需要检测的评论内容"
```

---

## 🌐 12. 前端JavaScript调用示例

### 12.1 最新评论查询函数
```javascript
/**
 * 获取最新评论列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise对象
 */
const getLatestComments = async (params = {}) => {
  const defaultParams = {
    current: 1,
    size: 10,
    status: 1,
    includeSensitive: true
  };

  const queryParams = { ...defaultParams, ...params };
  const queryString = new URLSearchParams(queryParams).toString();

  try {
    const response = await fetch(`/admin/comments/latest?${queryString}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('获取最新评论失败:', error);
    throw error;
  }
};

// 使用示例
getLatestComments({
  current: 1,
  size: 20,
  articleId: 123,
  minLikeCount: 5
}).then(data => {
  console.log('最新评论:', data.data.records);
  console.log('总数:', data.data.total);
});
```

### 12.2 热门评论查询函数
```javascript
/**
 * 获取热门评论列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise对象
 */
const getPopularComments = async (params = {}) => {
  const defaultParams = {
    current: 1,
    size: 10,
    status: 1,
    timeRange: 'all',
    likeWeight: 2,
    replyWeight: 1
  };

  const queryParams = { ...defaultParams, ...params };
  const queryString = new URLSearchParams(queryParams).toString();

  try {
    const response = await fetch(`/admin/comments/popular?${queryString}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('获取热门评论失败:', error);
    throw error;
  }
};

// 使用示例
getPopularComments({
  current: 1,
  size: 15,
  timeRange: 'week',
  minHotScore: 10,
  likeWeight: 3,
  replyWeight: 2
}).then(data => {
  console.log('热门评论:', data.data.records);
  console.log('总页数:', data.data.pages);
});
```

### 12.3 React组件集成示例
```javascript
import React, { useState, useEffect } from 'react';
import { Table, Pagination, Form, Input, Select, DatePicker, Button } from 'antd';

const CommentManagement = () => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [queryParams, setQueryParams] = useState({});

  // 获取最新评论
  const fetchLatestComments = async (params = {}) => {
    setLoading(true);
    try {
      const response = await getLatestComments({
        current: pagination.current,
        size: pagination.pageSize,
        ...queryParams,
        ...params
      });

      if (response.code === 200) {
        setComments(response.data.records);
        setPagination({
          current: response.data.current,
          pageSize: response.data.size,
          total: response.data.total
        });
      }
    } catch (error) {
      console.error('获取评论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取热门评论
  const fetchPopularComments = async (params = {}) => {
    setLoading(true);
    try {
      const response = await getPopularComments({
        current: pagination.current,
        size: pagination.pageSize,
        ...queryParams,
        ...params
      });

      if (response.code === 200) {
        setComments(response.data.records);
        setPagination({
          current: response.data.current,
          pageSize: response.data.size,
          total: response.data.total
        });
      }
    } catch (error) {
      console.error('获取评论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 分页改变处理
  const handlePaginationChange = (page, pageSize) => {
    setPagination({ ...pagination, current: page, pageSize });
    fetchLatestComments({ current: page, size: pageSize });
  };

  // 搜索处理
  const handleSearch = (values) => {
    setQueryParams(values);
    setPagination({ ...pagination, current: 1 });
    fetchLatestComments({ current: 1, ...values });
  };

  useEffect(() => {
    fetchLatestComments();
  }, []);

  return (
    <div>
      {/* 搜索表单 */}
      <Form onFinish={handleSearch} layout="inline">
        <Form.Item name="keyword">
          <Input placeholder="搜索关键词" />
        </Form.Item>
        <Form.Item name="articleId">
          <Input placeholder="文章ID" />
        </Form.Item>
        <Form.Item name="minLikeCount">
          <Input placeholder="最小点赞数" type="number" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">搜索</Button>
        </Form.Item>
        <Form.Item>
          <Button onClick={() => fetchLatestComments()}>最新评论</Button>
        </Form.Item>
        <Form.Item>
          <Button onClick={() => fetchPopularComments()}>热门评论</Button>
        </Form.Item>
      </Form>

      {/* 评论表格 */}
      <Table
        dataSource={comments}
        loading={loading}
        pagination={false}
        rowKey="id"
        columns={[
          { title: 'ID', dataIndex: 'id', width: 80 },
          { title: '内容', dataIndex: 'content', ellipsis: true },
          { title: '用户', dataIndex: 'userNickName', width: 120 },
          { title: '点赞数', dataIndex: 'likeCount', width: 80 },
          { title: '回复数', dataIndex: 'replyCount', width: 80 },
          { title: '创建时间', dataIndex: 'createTime', width: 180 }
        ]}
      />

      {/* 分页组件 */}
      <Pagination
        current={pagination.current}
        pageSize={pagination.pageSize}
        total={pagination.total}
        showSizeChanger
        showQuickJumper
        showTotal={(total, range) =>
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条评论`
        }
        onChange={handlePaginationChange}
        onShowSizeChange={handlePaginationChange}
      />
    </div>
  );
};

export default CommentManagement;
```

### 12.4 Vue.js组件集成示例
```javascript
<template>
  <div>
    <!-- 搜索表单 -->
    <el-form :model="searchForm" inline @submit.native.prevent="handleSearch">
      <el-form-item>
        <el-input v-model="searchForm.keyword" placeholder="搜索关键词" />
      </el-form-item>
      <el-form-item>
        <el-input v-model="searchForm.articleId" placeholder="文章ID" />
      </el-form-item>
      <el-form-item>
        <el-input v-model="searchForm.minLikeCount" placeholder="最小点赞数" type="number" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="fetchLatestComments">最新评论</el-button>
        <el-button @click="fetchPopularComments">热门评论</el-button>
      </el-form-item>
    </el-form>

    <!-- 评论表格 -->
    <el-table :data="comments" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="content" label="内容" show-overflow-tooltip />
      <el-table-column prop="userNickName" label="用户" width="120" />
      <el-table-column prop="likeCount" label="点赞数" width="80" />
      <el-table-column prop="replyCount" label="回复数" width="80" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.current"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      comments: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      searchForm: {}
    };
  },

  methods: {
    async fetchLatestComments(params = {}) {
      this.loading = true;
      try {
        const response = await getLatestComments({
          current: this.pagination.current,
          size: this.pagination.pageSize,
          ...this.searchForm,
          ...params
        });

        if (response.code === 200) {
          this.comments = response.data.records;
          this.pagination = {
            current: response.data.current,
            pageSize: response.data.size,
            total: response.data.total
          };
        }
      } catch (error) {
        this.$message.error('获取评论失败');
      } finally {
        this.loading = false;
      }
    },

    async fetchPopularComments(params = {}) {
      this.loading = true;
      try {
        const response = await getPopularComments({
          current: this.pagination.current,
          size: this.pagination.pageSize,
          ...this.searchForm,
          ...params
        });

        if (response.code === 200) {
          this.comments = response.data.records;
          this.pagination = {
            current: response.data.current,
            pageSize: response.data.size,
            total: response.data.total
          };
        }
      } catch (error) {
        this.$message.error('获取评论失败');
      } finally {
        this.loading = false;
      }
    },

    handleSearch() {
      this.pagination.current = 1;
      this.fetchLatestComments({ current: 1 });
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchLatestComments({ size: val });
    },

    handleCurrentChange(val) {
      this.pagination.current = val;
      this.fetchLatestComments({ current: val });
    }
  },

  mounted() {
    this.fetchLatestComments();
  }
};
</script>
```

---

## 📞 13. 技术支持

### 开发环境
- Java 17
- Spring Boot 3.5.3
- MySQL 8.0
- Sa-Token 权限框架
- MyBatis-Plus 数据访问

### 日志记录
所有接口调用都会记录详细日志，包括：
- 请求参数
- 操作结果
- 错误信息
- 操作人信息

### 性能优化
- 分页查询避免大数据量查询
- 敏感词检测使用缓存机制
- 统计数据支持缓存
- 数据库索引优化

### 数据库索引建议
```sql
-- 最新评论查询优化
CREATE INDEX idx_comment_latest ON comment(status, create_time DESC, id ASC);

-- 热门评论查询优化
CREATE INDEX idx_comment_popular ON comment(status, like_count DESC, reply_count DESC, create_time DESC, id ASC);

-- 文章评论查询优化
CREATE INDEX idx_comment_article ON comment(article_id, status, create_time DESC, id ASC);

-- 用户评论查询优化
CREATE INDEX idx_comment_user ON comment(user_id, status, create_time DESC, id ASC);

-- 敏感词筛选优化
CREATE INDEX idx_comment_sensitive ON comment(is_sensitive, status, create_time DESC, id ASC);
```

---

## 📝 14. 更新日志

### v2.0.0 (2024-01-27)
- 🎯 **重大更新**: 最新评论和热门评论接口支持完全灵活的分页和筛选
- ✅ **新增功能**: 支持10+种查询条件组合
- 🔧 **接口优化**: 统一所有查询接口的操作体验
- 📊 **热度算法**: 支持自定义热度计算权重
- 🌐 **前端友好**: 提供完整的JavaScript调用示例和组件集成
- 🚀 **性能提升**: 优化数据库查询和索引设计

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础评论管理功能
- 支持敏感词过滤
- 支持批量操作
- 支持统计分析

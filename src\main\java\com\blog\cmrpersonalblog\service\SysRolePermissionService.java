package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.cmrpersonalblog.entity.SysRolePermission;

import java.util.List;

/**
 * 角色权限关联服务接口
 */
public interface SysRolePermissionService extends IService<SysRolePermission> {

    /**
     * 为角色分配权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 移除角色的所有权限
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean removeAllPermissionsFromRole(Long roleId);

    /**
     * 移除权限的所有角色关联
     * @param permissionId 权限ID
     * @return 是否成功
     */
    boolean removeAllRolesFromPermission(Long permissionId);

    /**
     * 根据角色ID查询权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getPermissionIdsByRoleId(Long roleId);

    /**
     * 根据权限ID查询角色ID列表
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    List<Long> getRoleIdsByPermissionId(Long permissionId);

    /**
     * 检查角色是否拥有指定权限
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否拥有权限
     */
    boolean hasPermission(Long roleId, Long permissionId);

    /**
     * 批量移除权限的角色关联
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean removeRolesByPermissionIds(List<Long> permissionIds);

    /**
     * 批量移除角色的权限关联
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean removePermissionsByRoleIds(List<Long> roleIds);
}

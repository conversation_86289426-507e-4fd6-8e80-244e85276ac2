package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.cmrpersonalblog.entity.SysUser;
import com.blog.cmrpersonalblog.dto.*;

import java.util.List;

/**
 * 系统用户服务接口
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 根据用户名查询用户信息
     * @param userName 用户名
     * @return 用户信息
     */
    SysUser getUserByUserName(String userName);

    /**
     * 根据用户ID查询用户权限
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> getUserPermissions(Long userId);

    /**
     * 根据用户ID查询用户角色
     * @param userId 用户ID
     * @return 角色列表
     */
    List<String> getUserRoles(Long userId);

    /**
     * 更新用户登录信息
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserLoginInfo(SysUser user);

    /**
     * 验证用户密码
     * @param user 用户信息
     * @param password 密码
     * @return 是否正确
     */
    boolean verifyPassword(SysUser user, String password);

    /**
     * 加密密码（生成盐值并加密）
     * @param password 原始密码
     * @return 加密后的密码信息（包含盐值）
     * @deprecated 请使用 encryptPasswordForUser(password, userName) 方法
     */
    @Deprecated
    SysUser encryptPassword(String password);

    /**
     * 修改用户密码
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String newPassword);

    // ==================== 用户管理功能 ====================

    /**
     * 分页查询用户列表
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    PageResult<UserResponse> getUserPage(UserQueryRequest queryRequest);

    /**
     * 根据ID查询用户详情（包含角色信息）
     * @param userId 用户ID
     * @return 用户详情
     */
    UserResponse getUserDetail(Long userId);

    /**
     * 创建用户
     * @param createRequest 创建请求
     * @return 是否成功
     */
    boolean createUser(UserCreateRequest createRequest);

    /**
     * 更新用户信息
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateUser(UserUpdateRequest updateRequest);

    /**
     * 删除用户
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean deleteUsers(List<Long> userIds);

    /**
     * 重置用户密码
     * @param passwordChangeRequest 密码修改请求
     * @return 是否成功
     */
    boolean resetPassword(UserPasswordChangeRequest passwordChangeRequest);

    /**
     * 启用/禁用用户
     * @param userId 用户ID
     * @param status 状态（0=禁用，1=启用）
     * @param updateBy 操作人
     * @return 是否成功
     */
    boolean updateUserStatus(Long userId, Integer status, String updateBy);

    /**
     * 检查用户名是否存在
     * @param userName 用户名
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isUserNameExists(String userName, Long excludeUserId);

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeUserId);

    /**
     * 检查手机号是否存在
     * @param phonenumber 手机号
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isPhonenumberExists(String phonenumber, Long excludeUserId);
}

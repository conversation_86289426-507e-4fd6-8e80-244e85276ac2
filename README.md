# Cmr Personal Blog - RBAC权限管理系统

## 项目简介

基于Spring Boot 3 + SaToken + Redis + MyBatis-Plus实现的RBAC（基于角色的访问控制）权限管理系统，提供完整的用户登录、注销、权限验证功能。

## 技术栈

- **后端框架**: Spring Boot 3.5.3
- **权限认证**: SaToken 1.37.0 (Spring Boot 3)
- **缓存**: Redis + Jedis
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis-Plus 3.5.5 (Spring Boot 3)
- **JSON处理**: <PERSON> (Spring Boot默认)
- **工具库**: Lombok
- **Java版本**: JDK 17+

## 核心功能

### 🔐 认证功能
- 用户登录/注销
- Token自动刷新
- 登录状态检查
- 密码MD5加密

### 🛡️ 权限控制
- 基于角色的访问控制(RBAC)
- 细粒度权限验证
- 注解式权限控制
- 动态权限加载

### 📊 会话管理
- Redis存储会话信息
- Token过期自动清理
- 并发登录控制
- 登录IP记录

### 🔧 系统特性
- 统一异常处理
- 统一响应格式
- 参数校验
- 健康检查

## 数据库设计

### RBAC权限模型
```text
用户(User) ←→ 角色(Role) ←→ 权限(Permission)
```

### 核心表结构
- **sys_user**: 用户表
- **sys_role**: 角色表
- **sys_permission**: 权限表
- **sys_user_role**: 用户角色关联表
- **sys_role_permission**: 角色权限关联表

## 快速开始

### 1. 环境准备
- MySQL 8.0+
- Redis 6.0+
- Java 17+
- Maven 3.6+

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE cmr_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行建表语句
source sql.txt

-- 执行初始化数据
source src/main/resources/init-data.sql
```

### 3. 配置修改
修改 `src/main/resources/application.yml` 中的数据库和Redis连接信息

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 测试验证
访问健康检查接口：`GET http://localhost:8080/test/health`

## 测试账号

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 所有权限 |
| editor | 123456 | 编辑员 | 用户相关权限 |
| user | 123456 | 普通用户 | 查看权限 |

## 权限注解使用

```java
// 检查角色
@SaCheckRole("admin")
public Result adminOnly() {
    return Result.success("管理员专用接口");
}

// 检查权限
@SaCheckPermission("user:list")
public Result userList() {
    return Result.success("用户列表");
}
```

## 项目状态

### ✅ 已完成功能
- 用户登录/注销
- Token管理和刷新
- RBAC权限控制
- 注解式权限验证
- Redis会话存储
- 统一异常处理
- 统一响应格式

### 🚧 待扩展功能
- 验证码功能
- 菜单权限管理
- 操作日志记录
- 多租户模式
- OAuth2.0集成
- API限流功能

## 开发日志

### 用户管理功能
- **用户CRUD操作**: 在 `UserManagementController` 中实现，包括创建、查询、更新、删除用户
- **用户昵称支持**: 在 `SysUser` 实体类和相关DTO中添加 `nickName` 字段，支持用户昵称的设置和查询
- **角色分配限制**: 在 `UserManagementController.assignRolesToUser()` 和 `updateUser()` 方法中实现，管理员无法为自己分配角色
- **用户状态管理**: 在 `UserManagementController.updateUserStatus()` 中实现用户启用/禁用功能
- **密码重置**: 在 `UserManagementController.resetPassword()` 中实现管理员重置用户密码功能

### 文件上传功能
- **头像上传**: 在 `UserAvatarController` 和 `UserManagementController` 中实现用户头像上传功能
- **文件存储**: 本地存储在 `E:/CmrBlogImage/UserAvatar/` 目录，支持多种图片格式
- **文件管理**: 在 `FileUploadService` 中实现文件上传、删除等操作

### 权限认证功能
- **RBAC权限模型**: 在 `SysUserService`、`SysRoleService`、`SysPermissionService` 中实现
- **Sa-Token集成**: 在 `AuthService` 和相关配置类中实现登录认证和权限验证
- **角色权限管理**: 在 `SysUserRoleService`、`SysRolePermissionService` 中实现角色权限关联管理

### 角色管理功能
- **角色CRUD操作**: 在 `RoleManagementController` 中实现，包括创建、查询、更新、删除角色
- **角色权限管理**: 在 `RoleManagementController` 中实现角色权限分配、移除等功能
- **角色保护机制**: 管理员无法修改、删除自己拥有的角色，防止权限问题
- **角色权限关联**: 在 `SysRolePermissionService` 中实现角色与权限的关联管理

### 用户详情功能
- **用户档案**: 在 `UserProfileController` 中实现用户详情查看功能，包含基本信息和统计数据
- **发布记录**: 支持分页查询用户发布的文章，包含筛选和排序功能
- **互动数据**: 展示用户的关注列表、粉丝列表、点赞和收藏记录
- **关注系统**: 在 `UserFollowMapper` 中实现用户关注/取消关注功能
- **统计数据**: 在 `UserStatsMapper` 中维护用户的文章数、浏览量、点赞数等统计信息

### 后台用户详情管理
- **管理员用户详情**: 在 `AdminUserProfileController` 中实现后台管理的用户详情功能
- **用户行为分析**: 在 `UserActivityMapper` 中记录和分析用户活动，包含登录记录、操作日志等
- **安全监控**: 提供用户登录IP、设备信息、可疑登录检测等安全功能
- **数据统计**: 提供用户内容发布趋势、互动行为分析、活跃度统计等管理功能
- **活动记录**: 在 `UserActivity` 实体中记录用户的所有操作活动，支持审计和分析
- **角色状态管理**: 支持角色启用/禁用功能，停用的角色不可分配给用户

### 测试接口
- **API测试**: 在 `TestController` 中提供各种测试接口，避免污染业务控制器
- **权限测试**: 提供角色和权限验证的测试接口
- **角色管理测试**: 提供角色管理功能的测试接口

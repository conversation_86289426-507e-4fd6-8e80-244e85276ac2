-- 评论管理系统测试数据
-- 根据application.yml配置，数据库名为cmr_blog
USE cmr_blog;

-- 清理现有测试数据（避免主键冲突）
DELETE FROM `user_like` WHERE user_id IN (1,2,3,4,5) OR target_id IN (1,2,3);
DELETE FROM `user_ban` WHERE user_id IN (1,2,3,4,5);
DELETE FROM `comment` WHERE id BETWEEN 1 AND 50;
DELETE FROM `article` WHERE id IN (1,2,3);
DELETE FROM `sys_user` WHERE user_id IN (1,2,3,4,5);

-- 重置自增ID
ALTER TABLE `comment` AUTO_INCREMENT = 1;
ALTER TABLE `user_ban` AUTO_INCREMENT = 1;
ALTER TABLE `user_like` AUTO_INCREMENT = 1;

-- 首先确保有测试用户数据
INSERT INTO `sys_user` (`user_id`, `user_name`, `nick_name`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
(1, 'admin', '管理员', '<EMAIL>', '13800138000', '1', 'https://example.com/avatar1.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(2, 'testuser1', '测试用户1', '<EMAIL>', '13800138001', '1', 'https://example.com/avatar2.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(3, 'testuser2', '测试用户2', '<EMAIL>', '13800138002', '0', 'https://example.com/avatar3.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(4, 'testuser3', '测试用户3', '<EMAIL>', '13800138003', '1', 'https://example.com/avatar4.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW()),
(5, 'banneduser', '被禁用户', '<EMAIL>', '13800138004', '1', 'https://example.com/avatar5.jpg', '$2a$10$7JB720yubVSOfvVMe6/b.eHZb.7nbQUBdyhz/SjLigN0FjXuSJjE.', '1', '127.0.0.1', NOW(), 'system', NOW(), 'system', NOW());

-- 确保有测试文章数据
INSERT INTO `article` (`id`, `title`, `content`, `summary`, `category_id`, `tags`, `cover_image`, `author_id`, `view_count`, `like_count`, `comment_count`, `collect_count`, `share_count`, `is_original`, `is_top`, `status`, `publish_time`, `create_time`, `update_time`) VALUES
(1, 'Spring Boot 入门教程', '这是一篇关于Spring Boot的详细教程...', 'Spring Boot基础知识介绍', 1, 'Spring Boot,Java,教程', 'https://example.com/cover1.jpg', 1, 150, 25, 8, 12, 5, 1, 0, 1, NOW(), NOW(), NOW()),
(2, 'Vue.js 前端开发实战', '本文介绍Vue.js的核心概念和实战应用...', 'Vue.js实战开发指南', 2, 'Vue.js,前端,JavaScript', 'https://example.com/cover2.jpg', 1, 200, 35, 12, 18, 8, 1, 1, 1, NOW(), NOW(), NOW()),
(3, 'MySQL 数据库优化技巧', '分享一些MySQL数据库性能优化的实用技巧...', 'MySQL性能优化指南', 3, 'MySQL,数据库,优化', 'https://example.com/cover3.jpg', 2, 180, 28, 15, 20, 6, 1, 0, 1, NOW(), NOW(), NOW());

-- 插入测试评论数据（包含各种状态和敏感内容）
INSERT INTO `comment` (`id`, `article_id`, `user_id`, `parent_id`, `reply_to_user_id`, `content`, `original_content`, `root_id`, `level`, `path`, `like_count`, `reply_count`, `status`, `is_sensitive`, `sensitive_type`, `sensitive_words`, `sensitive_score`, `ip_address`, `location`, `user_agent`, `create_time`, `update_time`, `audit_time`, `audit_user_id`, `audit_remark`) VALUES

-- 正常评论（已通过审核）
(1, 1, 2, 0, NULL, '这篇文章写得很好，对初学者很有帮助！', '这篇文章写得很好，对初学者很有帮助！', 0, 1, '1', 5, 2, 1, 0, NULL, NULL, 0, '*************', '北京市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY, 1, '内容正常，审核通过'),

(2, 1, 3, 1, 2, '同意楼主的观点，Spring Boot确实简化了很多配置', '同意楼主的观点，Spring Boot确实简化了很多配置', 1, 2, '1/2', 3, 0, 1, 0, NULL, NULL, 0, '*************', '上海市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, 1, '回复内容正常'),

(3, 1, 4, 1, 2, '感谢分享，学到了很多新知识', '感谢分享，学到了很多新知识', 1, 2, '1/3', 2, 0, 1, 0, NULL, NULL, 0, '*************', '广州市', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, 1, '感谢类评论，通过'),

-- 包含敏感词的评论（已标记敏感但通过审核）
(4, 2, 2, 0, NULL, '这个教程还不错，比那些***的教程强多了', '这个教程还不错，比那些垃圾的教程强多了', 0, 1, '4', 1, 1, 1, 1, 'profanity', '[{"word":"垃圾","type":"profanity","severity":2}]', 25, '*************', '深圳市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, 1, '轻微敏感词，过滤后通过'),

(5, 2, 3, 4, 2, '确实，现在网上很多教程质量参差不齐', '确实，现在网上很多教程质量参差不齐', 4, 2, '4/5', 0, 0, 1, 0, NULL, NULL, 0, '*************', '杭州市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR, 1, '正常回复'),

-- 待审核的评论
(6, 3, 4, 0, NULL, '这个数据库优化方案很实用，收藏了', '这个数据库优化方案很实用，收藏了', 0, 1, '6', 0, 0, 0, 0, NULL, NULL, 0, '*************', '成都市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR, NULL, NULL, NULL),

(7, 3, 5, 0, NULL, '作者写得不错，但是有些地方还可以优化', '作者写得不错，但是有些地方还可以优化', 0, 1, '7', 0, 0, 0, 0, NULL, NULL, 0, '*************', '武汉市', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)', NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 30 MINUTE, NULL, NULL, NULL),

-- 包含严重敏感词的评论（待审核）
(8, 1, 5, 0, NULL, '这文章写得还行，但是作者可能是个***', '这文章写得还行，但是作者可能是个傻逼', 0, 1, '8', 0, 0, 0, 1, 'profanity', '[{"word":"傻逼","type":"profanity","severity":4}]', 65, '*************', '西安市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 20 MINUTE, NOW() - INTERVAL 20 MINUTE, NULL, NULL, NULL),

-- 包含政治敏感词的评论（待审核）
(9, 2, 5, 0, NULL, '这个技术在某些特殊时期可能会被限制使用', '这个技术在六四时期可能会被限制使用', 0, 1, '9', 0, 0, 0, 1, 'politics', '[{"word":"六四","type":"politics","severity":5}]', 85, '*************', '南京市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 15 MINUTE, NOW() - INTERVAL 15 MINUTE, NULL, NULL, NULL),

-- 广告类评论（待审核）
(10, 3, 5, 0, NULL, '需要***的朋友可以联系我，价格优惠', '需要代开发票的朋友可以联系我，价格优惠', 0, 1, '10', 0, 0, 0, 1, 'advertisement', '[{"word":"代开发票","type":"advertisement","severity":3}]', 55, '*************', '重庆市', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', NOW() - INTERVAL 10 MINUTE, NOW() - INTERVAL 10 MINUTE, NULL, NULL, NULL),

-- 已拒绝的评论
(11, 1, 5, 0, NULL, '这种***文章有什么好看的', '这种垃圾文章有什么好看的', 0, 1, '11', 0, 0, 2, 1, 'profanity', '[{"word":"垃圾","type":"profanity","severity":2}]', 35, '*************', '天津市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, 1, '内容不当，审核拒绝'),

-- 已删除的评论
(12, 2, 5, 0, NULL, '作者联系方式：微信号123456', '作者联系方式：微信号123456', 0, 1, '12', 0, 0, 3, 1, 'advertisement', '[{"word":"微信号","type":"advertisement","severity":2}]', 30, '*************', '青岛市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 6 HOUR, NOW() - INTERVAL 6 HOUR, NOW() - INTERVAL 5 HOUR, 1, '广告内容，删除处理');

-- 更新文章的评论数统计（使用JOIN避免1093错误）
UPDATE `article` a
LEFT JOIN (
    SELECT article_id, COUNT(*) as comment_count
    FROM `comment`
    WHERE `status` = 1
    GROUP BY article_id
) c ON a.id = c.article_id
SET a.comment_count = COALESCE(c.comment_count, 0);

-- 更新评论的回复数统计（使用JOIN避免1093错误）
UPDATE `comment` c1
LEFT JOIN (
    SELECT parent_id, COUNT(*) as reply_count
    FROM `comment`
    WHERE `status` = 1 AND `parent_id` > 0
    GROUP BY parent_id
) c2 ON c1.id = c2.parent_id
SET c1.reply_count = COALESCE(c2.reply_count, 0);

-- 插入用户禁言测试数据
INSERT INTO `user_ban` (`id`, `user_id`, `ban_type`, `ban_reason`, `ban_start_time`, `ban_end_time`, `is_permanent`, `status`, `operator_id`, `related_comment_id`, `create_time`, `update_time`) VALUES

-- 生效中的禁言
(1, 5, 'COMMENT', '发布不当评论，包含敏感词', NOW() - INTERVAL 1 HOUR, NOW() + INTERVAL 23 HOUR, 0, 1, 1, 8, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR),

-- 已过期的禁言
(2, 4, 'COMMENT', '发布垃圾评论', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 1 DAY, 0, 0, 1, NULL, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 1 DAY),

-- 永久禁言（已解除）
(3, 5, 'LOGIN', '多次违规，永久禁止登录', NOW() - INTERVAL 7 DAY, NULL, 1, 0, 1, NULL, NOW() - INTERVAL 7 DAY, NOW() - INTERVAL 3 DAY);

-- 插入更多敏感词到敏感词表
INSERT IGNORE INTO `sensitive_word` (`word`, `type`, `severity`, `status`) VALUES
-- 网络用语类敏感词
('沙雕', 'profanity', 2, 1),
('脑残', 'profanity', 3, 1),
('智商堪忧', 'profanity', 2, 1),
('低能', 'profanity', 3, 1),

-- 广告类敏感词
('加微信', 'advertisement', 2, 1),
('扫码关注', 'advertisement', 2, 1),
('免费领取', 'advertisement', 2, 1),
('限时优惠', 'advertisement', 2, 1),
('代理招商', 'advertisement', 3, 1),

-- 垃圾信息类
('点击链接', 'spam', 3, 1),
('免费下载', 'spam', 2, 1),
('注册送', 'spam', 3, 1),
('刷单', 'spam', 4, 1),

-- 测试用敏感词
('测试敏感词', 'profanity', 1, 1),
('系统测试', 'profanity', 1, 1);

-- 插入用户点赞测试数据
INSERT INTO `user_like` (`user_id`, `target_id`, `target_type`, `status`, `create_time`, `update_time`) VALUES
-- 文章点赞数据 (target_type = 1)
(2, 1, 1, 1, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(3, 1, 1, 1, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(4, 1, 1, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(5, 1, 1, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(1, 1, 1, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),

(2, 2, 1, 1, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR),
(3, 2, 1, 1, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR),
(4, 2, 1, 1, NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR),
(1, 2, 1, 1, NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR),

(2, 3, 1, 1, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR),
(3, 3, 1, 1, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR),
(4, 3, 1, 1, NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 30 MINUTE),

-- 评论点赞数据 (target_type = 2)
-- 评论1的点赞
(3, 1, 2, 1, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(4, 1, 2, 1, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY),
(5, 1, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(1, 1, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(2, 1, 2, 0, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 12 HOUR), -- 取消点赞

-- 评论2的点赞
(2, 2, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(4, 2, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(1, 2, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),

-- 评论3的点赞
(2, 3, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),
(1, 3, 2, 1, NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY),

-- 评论4的点赞（包含敏感词的评论）
(3, 4, 2, 1, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR),

-- 评论5的点赞
(4, 5, 2, 0, NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 1 HOUR); -- 取消点赞

-- 更新文章和评论的点赞数统计
UPDATE `article` a
LEFT JOIN (
    SELECT target_id, COUNT(*) as like_count
    FROM `user_like`
    WHERE `target_type` = 1 AND `status` = 1
    GROUP BY target_id
) ul ON a.id = ul.target_id
SET a.like_count = COALESCE(ul.like_count, 0);

UPDATE `comment` c
LEFT JOIN (
    SELECT target_id, COUNT(*) as like_count
    FROM `user_like`
    WHERE `target_type` = 2 AND `status` = 1
    GROUP BY target_id
) ul ON c.id = ul.target_id
SET c.like_count = COALESCE(ul.like_count, 0);

-- 插入更多测试评论数据（用于测试分页和复杂场景）
INSERT INTO `comment` (`article_id`, `user_id`, `parent_id`, `reply_to_user_id`, `content`, `original_content`, `root_id`, `level`, `path`, `like_count`, `reply_count`, `status`, `is_sensitive`, `sensitive_type`, `sensitive_words`, `sensitive_score`, `ip_address`, `location`, `user_agent`, `create_time`, `update_time`, `audit_time`, `audit_user_id`, `audit_remark`) VALUES

-- 更多正常评论（用于测试分页）
(1, 2, 0, NULL, '这个教程的代码示例很清晰，跟着做了一遍', '这个教程的代码示例很清晰，跟着做了一遍', 0, 1, '13', 0, 0, 1, 0, NULL, NULL, 0, '*************', '长沙市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 5 HOUR, NOW() - INTERVAL 5 HOUR, NOW() - INTERVAL 5 HOUR, 1, '正常评论'),

(2, 3, 0, NULL, 'Vue的响应式原理讲得很透彻，学到了', 'Vue的响应式原理讲得很透彻，学到了', 0, 1, '14', 0, 0, 1, 0, NULL, NULL, 0, '*************', '郑州市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, 1, '正常评论'),

(3, 4, 0, NULL, '数据库索引优化这部分很实用', '数据库索引优化这部分很实用', 0, 1, '15', 0, 0, 1, 0, NULL, NULL, 0, '*************', '石家庄市', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)', NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, 1, '正常评论'),

-- 多层级回复测试
(1, 3, 13, 2, '确实，我也跟着敲了一遍代码', '确实，我也跟着敲了一遍代码', 13, 2, '13/16', 0, 0, 1, 0, NULL, NULL, 0, '*************', '哈尔滨市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR, 1, '正常回复'),

(1, 4, 16, 3, '有没有遇到什么问题？', '有没有遇到什么问题？', 13, 3, '13/16/17', 0, 0, 1, 0, NULL, NULL, 0, '*************', '沈阳市', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR, 1, '正常回复'),

-- 包含轻微敏感词的评论（已通过审核）
(2, 2, 0, NULL, '这个框架比其他那些***框架好用多了', '这个框架比其他那些垃圾框架好用多了', 0, 1, '18', 0, 0, 1, 1, 'profanity', '[{"word":"垃圾","type":"profanity","severity":2}]', 20, '192.168.1.117', '太原市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR, 1, '轻微敏感词，过滤后通过'),

-- 待审核的长评论
(3, 3, 0, NULL, '这篇文章写得非常详细，特别是关于MySQL查询优化的部分。我在实际项目中也遇到过类似的性能问题，按照文章中的方法优化后，查询速度提升了很多。希望作者能继续分享更多这样的实战经验。', '这篇文章写得非常详细，特别是关于MySQL查询优化的部分。我在实际项目中也遇到过类似的性能问题，按照文章中的方法优化后，查询速度提升了很多。希望作者能继续分享更多这样的实战经验。', 0, 1, '19', 0, 0, 0, 0, NULL, NULL, 0, '*************', '合肥市', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR, NULL, NULL, NULL),

-- 包含多种敏感词的评论（待审核）
(1, 5, 0, NULL, '这教程还行，但是有些地方写得像***一样，而且还有***的嫌疑', '这教程还行，但是有些地方写得像垃圾一样，而且还有广告的嫌疑', 0, 1, '20', 0, 0, 0, 1, 'mixed', '[{"word":"垃圾","type":"profanity","severity":2},{"word":"广告","type":"advertisement","severity":2}]', 40, '*************', '福州市', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 30 MINUTE, NULL, NULL, NULL);

-- 显示插入的测试数据统计
SELECT '=== 测试数据插入完成 ===' as message;

SELECT
    '评论数据统计' as category,
    COUNT(*) as total_count,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_audit,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected,
    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as deleted,
    SUM(CASE WHEN is_sensitive = 1 THEN 1 ELSE 0 END) as sensitive_comments
FROM comment;

SELECT
    '禁言数据统计' as category,
    COUNT(*) as total_bans,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_bans,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as expired_bans,
    SUM(CASE WHEN is_permanent = 1 THEN 1 ELSE 0 END) as permanent_bans
FROM user_ban;

SELECT
    '点赞数据统计' as category,
    COUNT(*) as total_likes,
    SUM(CASE WHEN target_type = 1 THEN 1 ELSE 0 END) as article_likes,
    SUM(CASE WHEN target_type = 2 THEN 1 ELSE 0 END) as comment_likes,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_likes,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as cancelled_likes
FROM user_like;

SELECT
    '敏感词统计' as category,
    COUNT(*) as total_words,
    COUNT(DISTINCT type) as word_types
FROM sensitive_word WHERE status = 1;

-- 详细的数据统计报告
SELECT '=== 详细数据统计报告 ===' as message;

-- 按文章统计评论数
SELECT
    a.id as article_id,
    a.title as article_title,
    COUNT(c.id) as comment_count,
    SUM(CASE WHEN c.status = 1 THEN 1 ELSE 0 END) as approved_comments,
    SUM(CASE WHEN c.status = 0 THEN 1 ELSE 0 END) as pending_comments,
    SUM(CASE WHEN c.is_sensitive = 1 THEN 1 ELSE 0 END) as sensitive_comments,
    SUM(c.like_count) as total_likes
FROM article a
LEFT JOIN comment c ON a.id = c.article_id
GROUP BY a.id, a.title
ORDER BY comment_count DESC;

-- 按用户统计评论活跃度
SELECT
    u.user_id,
    u.nick_name,
    COUNT(c.id) as comment_count,
    SUM(CASE WHEN c.status = 1 THEN 1 ELSE 0 END) as approved_comments,
    SUM(CASE WHEN c.status = 0 THEN 1 ELSE 0 END) as pending_comments,
    SUM(CASE WHEN c.status = 2 THEN 1 ELSE 0 END) as rejected_comments,
    SUM(CASE WHEN c.is_sensitive = 1 THEN 1 ELSE 0 END) as sensitive_comments,
    SUM(c.like_count) as total_likes_received,
    (SELECT COUNT(*) FROM user_like ul WHERE ul.user_id = u.user_id AND ul.status = 1) as likes_given,
    (SELECT COUNT(*) FROM user_ban ub WHERE ub.user_id = u.user_id) as ban_count
FROM sys_user u
LEFT JOIN comment c ON u.user_id = c.user_id
GROUP BY u.user_id, u.nick_name
ORDER BY comment_count DESC;

-- 敏感词类型统计
SELECT
    type as sensitive_type,
    COUNT(*) as word_count,
    AVG(severity) as avg_severity,
    MIN(severity) as min_severity,
    MAX(severity) as max_severity
FROM sensitive_word
WHERE status = 1
GROUP BY type
ORDER BY word_count DESC;

-- 评论层级分布统计
SELECT
    level as comment_level,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM comment), 2) as percentage
FROM comment
WHERE status = 1
GROUP BY level
ORDER BY level;

-- 评论状态分布
SELECT
    CASE
        WHEN status = 0 THEN '待审核'
        WHEN status = 1 THEN '已通过'
        WHEN status = 2 THEN '已拒绝'
        WHEN status = 3 THEN '已删除'
        ELSE '未知状态'
    END as status_name,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM comment), 2) as percentage
FROM comment
GROUP BY status
ORDER BY status;

-- 最近活跃的评论（最近24小时）
SELECT
    c.id,
    c.content,
    u.nick_name as user_name,
    a.title as article_title,
    c.create_time,
    CASE
        WHEN c.status = 0 THEN '待审核'
        WHEN c.status = 1 THEN '已通过'
        WHEN c.status = 2 THEN '已拒绝'
        WHEN c.status = 3 THEN '已删除'
    END as status_name,
    c.is_sensitive,
    c.like_count
FROM comment c
LEFT JOIN sys_user u ON c.user_id = u.user_id
LEFT JOIN article a ON c.article_id = a.id
WHERE c.create_time >= NOW() - INTERVAL 24 HOUR
ORDER BY c.create_time DESC
LIMIT 10;

SELECT '=== 测试数据准备完成，可以开始测试评论管理功能 ===' as message;

package com.blog.cmrpersonalblog.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 文件上传配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 文件上传根路径
     */
    private String basePath = "E:/CmrBlogImage";

    /**
     * 用户头像上传路径
     */
    private String avatarPath = "/UserAvatar";

    /**
     * 文件访问URL前缀
     */
    private String urlPrefix = "/files";

    /**
     * 最大文件大小（字节）
     */
    private long maxFileSize = 5 * 1024 * 1024; // 5MB

    /**
     * 允许的图片文件类型
     */
    private List<String> allowedImageTypes = List.of(
        "image/jpeg",
        "image/jpg", 
        "image/png",
        "image/gif",
        "image/webp"
    );

    /**
     * 允许的文件扩展名
     */
    private List<String> allowedExtensions = List.of(
        ".jpg", ".jpeg", ".png", ".gif", ".webp"
    );

    /**
     * 获取用户头像完整上传路径
     */
    public String getAvatarFullPath() {
        return basePath + avatarPath;
    }

    /**
     * 获取文件访问URL
     */
    public String getFileUrl(String relativePath) {
        return urlPrefix + relativePath;
    }

    /**
     * 检查文件类型是否允许
     */
    public boolean isAllowedImageType(String contentType) {
        return allowedImageTypes.contains(contentType.toLowerCase());
    }

    /**
     * 检查文件扩展名是否允许
     */
    public boolean isAllowedExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        String extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        return allowedExtensions.contains(extension);
    }
}

package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.blog.cmrpersonalblog.dto.AdminUserProfileResponse;
import com.blog.cmrpersonalblog.dto.UserActivityQueryRequest;
import com.blog.cmrpersonalblog.enums.ActivityType;

/**
 * 用户活动记录服务接口
 */
public interface UserActivityService {

    /**
     * 记录用户活动（智能记录策略）
     * @param userId 用户ID
     * @param activityType 活动类型
     * @param description 活动描述
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param result 操作结果
     * @param extraData 额外数据
     */
    void recordActivity(Long userId, ActivityType activityType, String description, 
                       Long targetId, String targetType, String ipAddress, 
                       String userAgent, String result, String extraData);

    /**
     * 记录用户活动（简化版本）
     * @param userId 用户ID
     * @param activityType 活动类型
     * @param description 活动描述
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void recordActivity(Long userId, ActivityType activityType, String description,
                       String ipAddress, String userAgent);

    /**
     * 记录用户活动（字符串活动类型版本）
     * @param userId 用户ID
     * @param activityType 活动类型字符串
     * @param description 活动描述
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @param result 操作结果
     * @param extraData 额外数据
     */
    void recordActivity(Long userId, String activityType, String description,
                       Long targetId, String targetType, String result,
                       java.util.Map<String, Object> extraData);

    /**
     * 记录登录活动
     * @param userId 用户ID
     * @param success 是否成功
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param failureReason 失败原因（如果失败）
     */
    void recordLoginActivity(Long userId, boolean success, String ipAddress, 
                           String userAgent, String failureReason);

    /**
     * 分页查询用户活动记录
     * @param query 查询条件
     * @return 活动记录分页数据
     */
    IPage<AdminUserProfileResponse.ActivityRecord> getActivityPage(UserActivityQueryRequest query);

    /**
     * 获取用户最近活动记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 活动记录列表
     */
    java.util.List<AdminUserProfileResponse.ActivityRecord> getRecentActivities(Long userId, Integer limit);

    /**
     * 获取用户安全信息统计
     * @param userId 用户ID
     * @return 安全信息
     */
    AdminUserProfileResponse.SecurityInfo getUserSecurityInfo(Long userId);

    /**
     * 获取用户互动行为统计
     * @param userId 用户ID
     * @param days 统计天数
     * @return 互动行为信息
     */
    AdminUserProfileResponse.InteractionInfo getUserInteractionInfo(Long userId, Integer days);

    /**
     * 清理过期活动记录
     * @return 清理数量
     */
    Integer cleanExpiredActivities();

    /**
     * 清理指定类型的过期活动记录
     * @param activityType 活动类型
     * @return 清理数量
     */
    Integer cleanExpiredActivities(ActivityType activityType);

    /**
     * 获取活动统计信息
     * @param userId 用户ID（可选）
     * @param days 统计天数
     * @return 统计信息
     */
    java.util.Map<String, Object> getActivityStatistics(Long userId, Integer days);

    /**
     * 检查是否应该记录该活动
     * @param activityType 活动类型
     * @param userId 用户ID
     * @return 是否应该记录
     */
    boolean shouldRecord(ActivityType activityType, Long userId);
}

-- 文章管理系统测试数据（简化版）
-- 避免复杂的代码块，确保SQL语法正确

-- 首先确保分类数据存在
INSERT IGNORE INTO `article_category` (`id`, `name`, `description`, `icon`, `sort_order`, `status`) VALUES
(1, '前端', '前端开发技术分享', 'frontend-icon', 1, 1),
(2, '后端', '后端开发技术分享', 'backend-icon', 2, 1),
(3, '移动开发', '移动端开发技术', 'mobile-icon', 3, 1),
(4, '人工智能', 'AI与机器学习', 'ai-icon', 4, 1),
(5, '开发工具', '开发工具与效率', 'tools-icon', 5, 1),
(6, '代码人生', '程序员生活与思考', 'life-icon', 6, 1);

-- 插入文章测试数据
INSERT INTO `article` (`title`, `summary`, `content`, `cover_image`, `author_id`, `category_id`, `tags`, `view_count`, `like_count`, `comment_count`, `collect_count`, `share_count`, `is_original`, `is_top`, `status`, `publish_time`, `create_time`, `update_time`) VALUES

-- 已发布文章 (status = 1)
('Spring Boot 3.0 新特性详解', 'Spring Boot 3.0 带来了许多激动人心的新特性，包括对Java 17的支持、GraalVM原生镜像支持等。本文将详细介绍这些新特性。', 
'# Spring Boot 3.0 新特性详解

## 概述
Spring Boot 3.0 是一个重要的里程碑版本，带来了许多激动人心的新特性和改进。

## 主要新特性

### 1. Java 17 基线支持
Spring Boot 3.0 要求 Java 17 作为最低版本，这意味着可以使用所有 Java 17 的新特性。

### 2. GraalVM 原生镜像支持
现在可以将 Spring Boot 应用编译为原生镜像，大大提升启动速度。

### 3. 观测性改进
增强了对 Micrometer 和 OpenTelemetry 的支持。

## 总结
Spring Boot 3.0 为现代 Java 开发带来了更多可能性。', 
'/images/spring-boot-3.jpg', 1, 2, 'Spring Boot,Java,后端开发', 1250, 89, 23, 45, 12, 1, 1, 1, '2024-01-15 10:30:00', '2024-01-15 09:00:00', '2024-01-15 10:30:00'),

('Vue 3 Composition API 最佳实践', 'Vue 3 的 Composition API 为组件逻辑复用提供了更好的方案。本文分享一些实际项目中的最佳实践。', 
'# Vue 3 Composition API 最佳实践

## 为什么选择 Composition API

Composition API 解决了 Options API 在大型组件中的一些问题：
- 逻辑复用困难
- 类型推断不够友好
- 代码组织不够灵活

## 核心概念

### 1. setup() 函数
setup() 函数是 Composition API 的入口点，在组件创建之前执行。

### 2. 组合式函数
组合式函数是利用 Vue 组合式 API 来封装和复用有状态逻辑的函数。

## 最佳实践

1. **逻辑分组**: 将相关的响应式数据和方法放在一起
2. **组合式函数**: 提取可复用的逻辑
3. **TypeScript 支持**: 充分利用类型推断

## 总结
Composition API 让 Vue 3 更加灵活和强大。', 
'/images/vue3-composition.jpg', 2, 1, 'Vue.js,前端,JavaScript', 980, 67, 18, 32, 8, 1, 0, 1, '2024-01-14 14:20:00', '2024-01-14 13:00:00', '2024-01-14 14:20:00'),

('React Native 跨平台开发实战', '使用 React Native 开发跨平台移动应用的完整指南，包括环境搭建、组件开发、性能优化等。', 
'# React Native 跨平台开发实战

## 环境搭建

### 1. 安装依赖
首先需要安装 Node.js 和 React Native CLI。

### 2. 创建项目
使用 CLI 工具创建新的 React Native 项目。

## 核心组件

### 1. View 和 Text
View 和 Text 是 React Native 中最基础的组件。

### 2. 导航
使用 React Navigation 实现页面导航。

## 性能优化

1. **使用 FlatList**: 处理长列表
2. **图片优化**: 使用合适的图片格式和尺寸
3. **避免不必要的重渲染**: 使用 React.memo

## 总结
React Native 是跨平台开发的优秀选择。', 
'/images/react-native.jpg', 2, 3, 'React Native,移动开发,跨平台', 756, 45, 12, 28, 5, 1, 0, 1, '2024-01-13 16:45:00', '2024-01-13 15:30:00', '2024-01-13 16:45:00'),

-- 待审核文章 (status = 3)
('深度学习入门：从零开始构建神经网络', '本文将从基础概念开始，逐步介绍如何使用 Python 和 TensorFlow 构建你的第一个神经网络。', 
'# 深度学习入门：从零开始构建神经网络

## 什么是深度学习

深度学习是机器学习的一个分支，它模仿人脑神经网络的工作方式。

## 基础概念

### 1. 神经元
神经元是神经网络的基本单元，接收输入信号，经过处理后输出结果。

### 2. 神经网络
多个神经元组成神经网络，通过层层传递信息来学习复杂的模式。

## 实战项目：手写数字识别

使用 TensorFlow 构建一个简单的神经网络来识别手写数字。

### 数据预处理
首先需要对 MNIST 数据集进行预处理。

### 模型训练
使用训练数据来训练神经网络模型。

### 模型评估
在测试集上评估模型的性能。

## 总结
深度学习虽然复杂，但通过实践可以逐步掌握。', 
'/images/deep-learning.jpg', 3, 4, '深度学习,人工智能,Python,TensorFlow', 234, 15, 3, 8, 1, 1, 0, 3, NULL, '2024-01-16 11:20:00', '2024-01-16 11:20:00'),

('Docker 容器化部署最佳实践', '详细介绍如何使用 Docker 进行应用容器化部署，包括 Dockerfile 编写、镜像优化、多阶段构建等。', 
'# Docker 容器化部署最佳实践

## Docker 基础

### 1. 什么是 Docker
Docker 是一个开源的容器化平台，可以将应用及其依赖打包成轻量级、可移植的容器。

### 2. 核心概念
- **镜像 (Image)**: 只读模板
- **容器 (Container)**: 镜像的运行实例
- **仓库 (Repository)**: 存储镜像的地方

## Dockerfile 最佳实践

### 1. 基础镜像选择
选择合适的基础镜像是构建高效 Docker 镜像的第一步。

### 2. 多阶段构建
多阶段构建可以显著减小最终镜像的大小。

## Docker Compose

Docker Compose 用于定义和运行多容器 Docker 应用程序。

## 安全最佳实践

1. **使用非 root 用户**
2. **最小化镜像层**
3. **定期更新基础镜像**
4. **扫描安全漏洞**

## 总结
Docker 让应用部署变得简单可靠。', 
'/images/docker.jpg', 1, 5, 'Docker,容器化,DevOps,部署', 189, 12, 2, 5, 0, 1, 0, 3, NULL, '2024-01-16 09:15:00', '2024-01-16 09:15:00'),

-- 草稿文章 (status = 0)
('微服务架构设计模式', '探讨微服务架构中的常见设计模式，包括服务发现、配置管理、熔断器等。', 
'# 微服务架构设计模式

## 服务发现模式

### 客户端发现
客户端负责确定可用服务实例的位置。

### 服务端发现
客户端通过负载均衡器发送请求。

## 配置管理

### 外部化配置
将配置信息存储在外部系统中，如配置中心。

## 熔断器模式

熔断器模式用于防止级联故障，当服务不可用时快速失败。

## API 网关

统一入口点，处理：
- 路由
- 认证
- 限流
- 监控

## 数据管理

### 每个服务一个数据库
每个微服务都有自己的数据库，保证数据的独立性。

### 事件驱动架构
通过事件实现服务间通信，降低耦合度。

## 总结
微服务架构需要合理的设计模式支撑。', 
'/images/microservices.jpg', 1, 2, '微服务,架构设计,Spring Cloud', 0, 0, 0, 0, 0, 1, 0, 0, NULL, '2024-01-16 14:30:00', '2024-01-16 14:30:00'),

-- 审核拒绝文章 (status = 4)
('如何快速学会编程', '编程学习的一些个人心得体会。', 
'# 如何快速学会编程

编程其实很简单，只要掌握几个要点就行了。

## 要点一：多练习
每天写代码，熟能生巧。

## 要点二：看视频
网上有很多免费的编程视频。

## 要点三：找工作
直接找个编程工作，边做边学。

总之，编程没有想象中那么难。', 
NULL, 3, 6, '编程,学习', 45, 2, 1, 0, 0, 0, 0, 4, NULL, '2024-01-15 20:00:00', '2024-01-15 20:00:00'),

-- 更多已发布文章
('Git 版本控制进阶技巧', 'Git 是现代软件开发中不可或缺的工具。本文介绍一些高级使用技巧，帮助你更高效地管理代码版本。', 
'# Git 版本控制进阶技巧

## 分支管理策略

### Git Flow
Git Flow 是一种分支管理策略，适合有计划发布周期的项目。

### GitHub Flow
更简单的分支策略，适合持续部署。

## 高级命令

### 1. 交互式变基
交互式变基允许你修改提交历史。

### 2. 选择性提交
可以选择文件的部分内容进行提交。

### 3. 暂存工作
临时保存当前工作状态。

## 钩子 (Hooks)

Git 钩子可以在特定事件发生时自动执行脚本。

## 最佳实践

1. **提交信息规范**: 使用 Conventional Commits
2. **小而频繁的提交**: 便于回滚和调试
3. **保护主分支**: 设置分支保护规则

## 总结
掌握 Git 高级技巧能显著提升开发效率。', 
'/images/git-advanced.jpg', 2, 5, 'Git,版本控制,开发工具', 892, 56, 14, 23, 7, 1, 0, 1, '2024-01-12 11:15:00', '2024-01-12 10:00:00', '2024-01-12 11:15:00'),

('程序员的自我修养', '作为一名程序员，技术能力固然重要，但软技能同样不可忽视。本文分享一些个人成长的思考。', 
'# 程序员的自我修养

## 技术成长

### 1. 持续学习
技术更新换代很快，需要保持学习的习惯。

- 关注技术博客和社区
- 参与开源项目
- 阅读优秀的代码

### 2. 深度与广度并重
既要在某个领域有深入的理解，也要对相关技术有广泛的了解。

## 软技能培养

### 1. 沟通能力
- 技术方案的表达
- 需求的理解和确认
- 团队协作

### 2. 问题解决能力
遇到问题时，要有系统性的解决思路。

## 职业规划

### 技术路线
- 初级开发者 → 中级开发者 → 高级开发者
- 专家路线 vs 管理路线

### 个人品牌
- 技术博客
- 开源贡献
- 技术分享

## 工作生活平衡

编程是一份需要持续投入的工作，但也要注意：
- 适当休息，避免过度疲劳
- 培养其他兴趣爱好
- 关注身体健康

## 总结
技术是基础，但全面发展才能走得更远。', 
'/images/programmer-growth.jpg', 3, 6, '程序员,职业发展,软技能', 1156, 78, 25, 41, 15, 1, 1, 1, '2024-01-11 15:30:00', '2024-01-11 14:00:00', '2024-01-11 15:30:00'),

-- 置顶文章
('2024年前端技术趋势预测', '回顾2023年前端技术的发展，展望2024年可能出现的新趋势和技术方向。', 
'# 2024年前端技术趋势预测

## 2023年回顾

### 主要发展
- React 18 的广泛采用
- Vue 3 生态系统的完善
- TypeScript 的进一步普及
- 构建工具的性能提升 (Vite, esbuild)

## 2024年趋势预测

### 1. 框架发展
React Server Components 将更加成熟，为服务器端渲染带来新的可能性。

### 2. 构建工具演进
- **Turbopack**: Webpack 的继任者
- **SWC**: 更快的 JavaScript/TypeScript 编译器
- **Bun**: 新的 JavaScript 运行时

### 3. CSS 新特性
容器查询和 CSS 嵌套等新特性将得到更广泛的支持。

### 4. Web 平台新能力
- **Web Components** 的进一步标准化
- **PWA** 功能增强
- **WebAssembly** 在前端的应用

### 5. 开发体验改进
更好的类型推断和开发工具将进一步提升开发效率。

## 学习建议

### 对于初学者
1. 扎实掌握 JavaScript 基础
2. 选择一个主流框架深入学习
3. 了解现代构建工具

### 对于有经验的开发者
1. 关注性能优化技术
2. 学习新的开发模式 (如 Islands Architecture)
3. 探索全栈开发

## 总结
前端技术发展迅速，保持学习和实践是关键。', 
'/images/frontend-trends-2024.jpg', 2, 1, '前端,技术趋势,2024,预测', 2341, 156, 48, 89, 32, 1, 1, 1, '2024-01-10 09:00:00', '2024-01-10 08:00:00', '2024-01-10 09:00:00');

-- 更新文章状态，添加审核信息
UPDATE `article` SET 
    `audit_time` = '2024-01-15 20:30:00',
    `audit_user_id` = 1,
    `audit_remark` = '内容过于简单，缺乏深度和实用性',
    `audit_reason` = '文章内容质量不符合发布标准，建议补充更多技术细节和实践案例'
WHERE `status` = 4;

-- 为已发布的文章设置发布时间
UPDATE `article` SET `publish_time` = `create_time` WHERE `status` = 1 AND `publish_time` IS NULL;

package com.blog.cmrpersonalblog.dto;

import lombok.Data;

/**
 * 密码生成响应DTO
 */
@Data
public class PasswordGenerateResponse {

    /**
     * 原始密码
     */
    private String originalPassword;

    /**
     * 盐值
     */
    private String salt;

    /**
     * 加密后的密码
     */
    private String encryptedPassword;

    /**
     * 验证结果
     */
    private boolean verified;

    /**
     * SQL更新语句
     */
    private String sqlUpdate;

    public PasswordGenerateResponse(String originalPassword, String salt, String encryptedPassword, boolean verified) {
        this.originalPassword = originalPassword;
        this.salt = salt;
        this.encryptedPassword = encryptedPassword;
        this.verified = verified;
        this.sqlUpdate = String.format(
            "UPDATE `sys_user` SET `salt` = '%s', `password` = '%s' WHERE `user_name` = 'your_username';",
            salt, encryptedPassword
        );
    }
}

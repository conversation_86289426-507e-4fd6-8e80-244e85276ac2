package com.blog.cmrpersonalblog.service.impl;

import com.blog.cmrpersonalblog.config.FileUploadConfig;
import com.blog.cmrpersonalblog.dto.FileUploadResponse;
import com.blog.cmrpersonalblog.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件上传服务实现类
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public FileUploadResponse uploadAvatar(MultipartFile file, Long userId) {
        try {
            // 验证文件
            if (!isValidImage(file)) {
                throw new RuntimeException("不支持的文件类型");
            }

            // 检查文件大小
            if (file.getSize() > fileUploadConfig.getMaxFileSize()) {
                throw new RuntimeException("文件大小超过限制");
            }

            // 创建上传目录
            String uploadPath = fileUploadConfig.getAvatarFullPath();
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    throw new RuntimeException("创建上传目录失败");
                }
            }

            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename(), userId);
            
            // 构建文件路径
            Path filePath = Paths.get(uploadPath, fileName);
            
            // 保存文件
            Files.copy(file.getInputStream(), filePath);
            
            // 构建相对路径和访问URL
            String relativePath = fileUploadConfig.getAvatarPath() + "/" + fileName;
            String fileUrl = fileUploadConfig.getFileUrl(relativePath);

            log.info("文件上传成功: {}", filePath.toString());

            return new FileUploadResponse(
                fileName,
                file.getOriginalFilename(),
                file.getSize(),
                file.getContentType(),
                relativePath,
                fileUrl
            );

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String relativePath) {
        try {
            if (!StringUtils.hasText(relativePath)) {
                return false;
            }

            String fullPath = fileUploadConfig.getBasePath() + relativePath;
            Path filePath = Paths.get(fullPath);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("文件删除成功: {}", fullPath);
                return true;
            } else {
                log.warn("文件不存在: {}", fullPath);
                return false;
            }
        } catch (IOException e) {
            log.error("文件删除失败: {}", relativePath, e);
            return false;
        }
    }

    @Override
    public boolean isValidImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (!fileUploadConfig.isAllowedImageType(contentType)) {
            return false;
        }

        // 检查文件扩展名
        String originalFileName = file.getOriginalFilename();
        if (!fileUploadConfig.isAllowedExtension(originalFileName)) {
            return false;
        }

        return true;
    }

    @Override
    public String generateFileName(String originalFileName, Long userId) {
        // 获取文件扩展名
        String extension = getFileExtension(originalFileName);
        
        // 生成时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 生成UUID
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        // 构建文件名：avatar_用户ID_时间戳_UUID.扩展名
        StringBuilder fileName = new StringBuilder("avatar");
        if (userId != null) {
            fileName.append("_").append(userId);
        }
        fileName.append("_").append(timestamp);
        fileName.append("_").append(uuid.substring(0, 8)); // 只取UUID前8位
        fileName.append(extension);
        
        return fileName.toString();
    }

    @Override
    public String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex).toLowerCase();
    }
}

package com.blog.cmrpersonalblog.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文章详情响应DTO（包含完整内容）
 */
@Data
public class ArticleDetailResponse {

    /**
     * 文章ID
     */
    private Long id;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章摘要
     */
    private String summary;

    /**
     * 文章内容（Markdown格式）
     */
    private String content;

    /**
     * 文章内容（HTML格式）
     */
    private String htmlContent;

    /**
     * 封面图片URL
     */
    private String coverImage;

    /**
     * 作者ID
     */
    private Long authorId;

    /**
     * 作者用户名
     */
    private String authorName;

    /**
     * 作者昵称
     */
    private String authorNickName;

    /**
     * 作者头像
     */
    private String authorAvatar;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 标签，逗号分隔
     */
    private String tags;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 是否原创 0-转载 1-原创
     */
    private Integer isOriginal;

    /**
     * 是否置顶 0-否 1-是
     */
    private Integer isTop;

    /**
     * 状态 0-草稿 1-已发布 2-已删除 3-待审核 4-审核拒绝 5-已下架
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人用户名
     */
    private String auditUserName;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 字数统计
     */
    private Integer wordCount;

    /**
     * 阅读时长（分钟）
     */
    private Integer readingTime;

    /**
     * 获取状态文本描述
     */
    public String getStatusText() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "草稿";
            case 1:
                return "已发布";
            case 2:
                return "已删除";
            case 3:
                return "待审核";
            case 4:
                return "审核拒绝";
            case 5:
                return "已下架";
            default:
                return "未知";
        }
    }

    /**
     * 是否可以编辑
     */
    public boolean isEditable() {
        return status != null && (status == 0 || status == 4); // 草稿或审核拒绝可以编辑
    }

    /**
     * 是否可以审核
     */
    public boolean isAuditable() {
        return status != null && status == 3; // 待审核状态可以审核
    }

    /**
     * 是否已发布
     */
    public boolean isPublished() {
        return status != null && status == 1;
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return status != null && status == 2;
    }
}

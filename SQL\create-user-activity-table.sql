-- 创建用户活动记录表
-- 如果您遇到 "Table 'cmr_blog.user_activity' doesn't exist" 错误，请执行此SQL

USE cmr_blog;

-- 创建用户活动记录表
CREATE TABLE IF NOT EXISTS `user_activity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '活动记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `activity_type` VARCHAR(50) NOT NULL COMMENT '活动类型',
    `activity_description` VARCHAR(500) COMMENT '活动描述',
    `target_id` BIGINT COMMENT '目标ID（文章ID、用户ID等）',
    `target_type` VARCHAR(50) COMMENT '目标类型（article、user、comment等）',
    `ip_address` VARCHAR(128) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `result` VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '操作结果（SUCCESS、FAILED）',
    `extra_data` JSON COMMENT '额外数据（JSON格式）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_activity_type` (`activity_type`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_user_activity` (`user_id`, `activity_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动记录表';

-- 插入一些示例数据（可选）
INSERT INTO `user_activity` (`user_id`, `activity_type`, `activity_description`, `target_id`, `target_type`, `ip_address`, `user_agent`, `result`) VALUES
(1, 'LOGIN', '管理员登录', NULL, 'system', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS'),
(1, 'VIEW_USER_PROFILE', '查看用户详情', 2, 'user', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS'),
(2, 'LOGIN', '用户登录', NULL, 'system', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS'),
(2, 'FOLLOW_USER', '关注用户', 1, 'user', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'SUCCESS');

-- 验证表是否创建成功
SELECT COUNT(*) as record_count FROM `user_activity`;

-- 查看表结构
DESCRIBE `user_activity`;

-- 显示创建表的SQL语句
SHOW CREATE TABLE `user_activity`;

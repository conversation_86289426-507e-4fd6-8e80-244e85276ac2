package com.blog.cmrpersonalblog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.blog.cmrpersonalblog.entity.SysUserRole;

import java.util.List;

/**
 * 用户角色关联服务接口
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    /**
     * 为用户分配角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 移除用户的所有角色
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeAllRolesFromUser(Long userId);

    /**
     * 移除角色的所有用户关联
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean removeAllUsersFromRole(Long roleId);

    /**
     * 根据用户ID查询角色ID列表
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getRoleIdsByUserId(Long userId);

    /**
     * 根据角色ID查询用户ID列表
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> getUserIdsByRoleId(Long roleId);

    /**
     * 检查用户是否拥有指定角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否拥有
     */
    boolean hasRole(Long userId, Long roleId);

    /**
     * 批量删除用户的角色关联
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean removeRolesByUserIds(List<Long> userIds);
}

-- 调试用户角色信息的SQL查询
-- 用于检查用户角色数据是否正确

USE cmr_blog;

-- 1. 查看所有用户
SELECT user_id, user_name, nick_name, status FROM sys_user;

-- 2. 查看所有角色
SELECT id, role_key, role_name, description, status FROM sys_role;

-- 3. 查看用户角色关联
SELECT ur.user_id, ur.role_id, u.user_name, r.role_name 
FROM sys_user_role ur
LEFT JOIN sys_user u ON ur.user_id = u.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id;

-- 4. 查看用户1的角色信息（模拟应用查询）
SELECT u.user_id, u.user_name, u.nick_name, u.password, u.phonenumber, u.email, u.avatar, u.sex, u.status,
       u.login_ip, u.login_date, u.create_by, u.create_time, u.update_by, u.update_time,
       r.id as role_id, r.role_key, r.role_name, r.description as role_description, r.status as role_status
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id AND r.status = '0'
WHERE u.user_id = 1
ORDER BY r.id;

-- 5. 检查是否有用户没有角色
SELECT u.user_id, u.user_name, u.nick_name
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
WHERE ur.user_id IS NULL;

-- 6. 检查是否有角色状态不正确的情况
SELECT r.id, r.role_key, r.role_name, r.status
FROM sys_role r
WHERE r.status != '0';

-- 7. 如果用户1没有角色，为其分配管理员角色
-- INSERT INTO sys_user_role (user_id, role_id) VALUES (1, 1);

-- 8. 如果没有管理员角色，创建一个
-- INSERT INTO sys_role (role_key, role_name, description, status, create_time, update_time) 
-- VALUES ('admin', '管理员', '系统管理员角色', '0', NOW(), NOW());

-- 9. 验证修复后的查询
-- SELECT u.user_id, u.user_name, r.role_name
-- FROM sys_user u
-- LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
-- LEFT JOIN sys_role r ON ur.role_id = r.id AND r.status = '0'
-- WHERE u.user_id = 1;

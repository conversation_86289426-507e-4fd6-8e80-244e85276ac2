package com.blog.cmrpersonalblog.service;

import com.blog.cmrpersonalblog.dto.SensitiveWordCheckResult;

import java.util.List;
import java.util.Set;

/**
 * 敏感词过滤服务接口
 */
public interface SensitiveWordService {

    /**
     * 检测文本中的敏感词
     * @param content 待检测的内容
     * @return 检测结果
     */
    SensitiveWordCheckResult checkSensitiveWords(String content);

    /**
     * 过滤敏感词（替换为*）
     * @param content 原始内容
     * @return 过滤后的内容
     */
    String filterSensitiveWords(String content);

    /**
     * 检测是否包含敏感词
     * @param content 待检测的内容
     * @return 是否包含敏感词
     */
    boolean containsSensitiveWords(String content);

    /**
     * 获取文本中的所有敏感词
     * @param content 待检测的内容
     * @return 敏感词列表
     */
    List<String> findSensitiveWords(String content);

    /**
     * 添加敏感词
     * @param word 敏感词
     * @param type 类型
     * @param severity 严重程度（1-5）
     */
    void addSensitiveWord(String word, String type, int severity);

    /**
     * 删除敏感词
     * @param word 敏感词
     */
    void removeSensitiveWord(String word);

    /**
     * 批量添加敏感词
     * @param words 敏感词列表
     * @param type 类型
     * @param severity 严重程度
     */
    void addSensitiveWords(List<String> words, String type, int severity);

    /**
     * 获取所有敏感词
     * @return 敏感词集合
     */
    Set<String> getAllSensitiveWords();

    /**
     * 按类型获取敏感词
     * @param type 敏感词类型
     * @return 敏感词集合
     */
    Set<String> getSensitiveWordsByType(String type);

    /**
     * 刷新敏感词库（从数据库重新加载）
     */
    void refreshSensitiveWords();

    /**
     * 获取敏感词统计信息
     * @return 统计信息
     */
    java.util.Map<String, Object> getSensitiveWordStatistics();

    /**
     * 验证敏感词配置
     * @return 验证结果
     */
    boolean validateSensitiveWordConfig();

    /**
     * 导出敏感词库
     * @return 敏感词数据
     */
    String exportSensitiveWords();

    /**
     * 导入敏感词库
     * @param data 敏感词数据
     * @return 导入结果
     */
    boolean importSensitiveWords(String data);
}
